(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.parseLengthAndUnit = parseLengthAndUnit;
    exports.cssValue = cssValue;
    var cssUnit = {
        cm: true,
        mm: true,
        in: true,
        px: true,
        pt: true,
        pc: true,
        em: true,
        ex: true,
        ch: true,
        rem: true,
        vw: true,
        vh: true,
        vmin: true,
        vmax: true,
        "%": true,
    };
    /**
     * If size is a number, append px to the value as default unit.
     * If size is a string, validate against list of valid units.
     * If unit is valid, return size as is.
     * If unit is invalid, console warn issue, replace with px as the unit.
     *
     * @param {(number | string)} size
     * @return {LengthObject} LengthObject
     */
    function parseLengthAndUnit(size) {
        if (typeof size === "number") {
            return {
                value: size,
                unit: "px",
            };
        }
        var value;
        var valueString = (size.match(/^[0-9.]*/) || "").toString();
        if (valueString.includes(".")) {
            value = parseFloat(valueString);
        }
        else {
            value = parseInt(valueString, 10);
        }
        var unit = (size.match(/[^0-9]*$/) || "").toString();
        if (cssUnit[unit]) {
            return {
                value: value,
                unit: unit,
            };
        }
        console.warn("React Spinners: ".concat(size, " is not a valid css value. Defaulting to ").concat(value, "px."));
        return {
            value: value,
            unit: "px",
        };
    }
    /**
     * Take value as an input and return valid css value
     *
     * @param {(number | string)} value
     * @return {string} valid css value
     */
    function cssValue(value) {
        var lengthWithunit = parseLengthAndUnit(value);
        return "".concat(lengthWithunit.value).concat(lengthWithunit.unit);
    }
});
