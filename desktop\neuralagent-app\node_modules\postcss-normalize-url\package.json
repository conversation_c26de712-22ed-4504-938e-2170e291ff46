{"name": "postcss-normalize-url", "version": "5.1.0", "description": "Normalize URLs with PostCSS", "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "LICENSE-MIT", "types"], "keywords": ["css", "normalize", "optimise", "optimisation", "postcss", "postcss-plugin", "url"], "license": "MIT", "dependencies": {"normalize-url": "^6.0.1", "postcss-value-parser": "^4.2.0"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-normalize-url\n\n> [Normalize URLs](https://github.com/sindresorhus/normalize-url) with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-normalize-url) do:\n\n```\nnpm install postcss-normalize-url --save\n```\n\n## Example\n\n### Input\n\n```css\nh1 {\n    background: url(\"http://site.com:80/image.jpg\")\n}\n```\n\n### Output\n\n```css\nh1 {\n    background: url(http://site.com/image.jpg)\n}\n```\n\nNote that this module will also try to normalize relative URLs, and is capable\nof stripping unnecessary quotes. For more examples, see the [tests](test.js).\n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## API\n\n### normalize([options])\n\nPlease see the [normalize-url documentation][docs]. By default,\n`normalizeProtocol`, `stripHash` & `stripWWW` are set to `false`.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n[docs]: https://github.com/sindresorhus/normalize-url#options\n[postcss]: https://github.com/postcss/postcss\n"}