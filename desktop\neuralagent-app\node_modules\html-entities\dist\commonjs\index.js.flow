/**
 * Flowtype definitions for index
 * Generated by Flowgen from a Typescript Definition
 * Flowgen v1.13.0
 * @flow
 */

export type Level = "xml" | "html4" | "html5" | "all";
declare interface CommonOptions {
  level?: Level;
}
export type EncodeMode =
  | "specialChars"
  | "nonAscii"
  | "nonAsciiPrintable"
  | "nonAsciiPrintableOnly"
  | "extensive";
export type EncodeOptions = {
  mode?: EncodeMode,
  numeric?: "decimal" | "hexadecimal",
  ...
} & CommonOptions;
export type DecodeScope = "strict" | "body" | "attribute";
export type DecodeOptions = {
  scope?: DecodeScope,
  ...
} & CommonOptions;

/**
 * Encodes all the necessary (specified by `level`) characters in the text
 */
declare export function encode(
  text: string | void | null,
  x?: EncodeOptions
): string;

/**
 * Decodes a single entity
 */
declare export function decodeEntity(
  entity: string | void | null,
  x?: CommonOptions
): string;

/**
 * Decodes all entities in the text
 */
declare export function decode(
  text: string | void | null,
  x?: DecodeOptions
): string;
declare export {};
