{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\layouts\\\\Sidebar\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport neuralagent_logo_white from '../../assets/neuralagent_logo_white.png';\nimport { BtnIcon, Button } from '../../components/Elements/Button';\nimport { MdAddCircleOutline } from 'react-icons/md';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setLoadingDialog } from '../../store';\nimport constants from '../../utils/constants';\nimport axios from '../../utils/axios';\nimport { SidebarContainer, LogoWrapper, Logo } from './SidebarElements';\nimport { List, ListItemRR, ListItemContent, ListItemTitle } from '../../components/Elements/List';\nimport { Text } from '../../components/Elements/Typography';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function Sidebar() {\n  _s();\n  const [threads, setThreads] = useState([]);\n  const isLoading = useSelector(state => state.isLoading);\n  const accessToken = useSelector(state => state.accessToken);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const getThreads = () => {\n    dispatch(setLoadingDialog(true));\n    axios.get('/threads', {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      setThreads(response.data);\n      dispatch(setLoadingDialog(false));\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n  useEffect(() => {\n    getThreads();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(SidebarContainer, {\n    children: [/*#__PURE__*/_jsxDEV(LogoWrapper, {\n      to: \"/\",\n      children: /*#__PURE__*/_jsxDEV(Logo, {\n        src: neuralagent_logo_white,\n        alt: \"NeuralAgent\",\n        height: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      padding: \"7px 15px\",\n      color: 'var(--primary-color)',\n      borderRadius: 6,\n      fontSize: \"15px\",\n      dark: true,\n      onClick: () => navigate('/'),\n      children: [/*#__PURE__*/_jsxDEV(BtnIcon, {\n        left: true,\n        color: \"#fff\",\n        iconSize: \"23px\",\n        children: /*#__PURE__*/_jsxDEV(MdAddCircleOutline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), \"New Task\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      padding: \"0px 10px\",\n      style: {\n        marginTop: '10px',\n        overflowY: 'auto'\n      },\n      children: !isLoading && threads.length === 0 ? /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          marginTop: '7px',\n          padding: '8px'\n        },\n        fontSize: \"14px\",\n        textAlign: \"center\",\n        color: 'rgba(255,255,255,0.7)',\n        children: \"You currently have no threads\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: threads.map(thread => {\n          return /*#__PURE__*/_jsxDEV(ListItemRR, {\n            padding: \"10px\",\n            to: '/threads/' + thread.id,\n            isDarkMode: true,\n            borderRadius: \"8px\",\n            style: {\n              marginTop: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemContent, {\n              children: /*#__PURE__*/_jsxDEV(ListItemTitle, {\n                fontSize: \"14px\",\n                color: \"#fff\",\n                fontWeight: \"400\",\n                children: thread.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 21\n            }, this)\n          }, 'thread__' + thread.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 19\n          }, this);\n        })\n      }, void 0, false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(Sidebar, \"KUSWcVCAf2GHA2bTyjPsbBXy994=\", false, function () {\n  return [useSelector, useSelector, useNavigate, useDispatch];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "neuralagent_logo_white", "BtnIcon", "<PERSON><PERSON>", "MdAddCircleOutline", "useNavigate", "useDispatch", "useSelector", "setLoadingDialog", "constants", "axios", "SidebarContainer", "LogoWrapper", "Logo", "List", "ListItemRR", "ListItemContent", "ListItemTitle", "Text", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "_s", "threads", "setThreads", "isLoading", "state", "accessToken", "navigate", "dispatch", "getThreads", "get", "headers", "then", "response", "data", "catch", "error", "status", "UNAUTHORIZED", "window", "location", "reload", "children", "to", "src", "alt", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "color", "borderRadius", "fontSize", "dark", "onClick", "left", "iconSize", "style", "marginTop", "overflowY", "length", "textAlign", "map", "thread", "id", "isDarkMode", "fontWeight", "title", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/layouts/Sidebar/index.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport neuralagent_logo_white from '../../assets/neuralagent_logo_white.png';\nimport { BtnIcon, Button } from '../../components/Elements/Button';\nimport { MdAddCircleOutline } from 'react-icons/md';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setLoadingDialog } from '../../store';\nimport constants from '../../utils/constants';\nimport axios from '../../utils/axios';\nimport {\n  SidebarContainer,\n  LogoWrapper,\n  Logo\n} from './SidebarElements';\nimport {\n  List,\n  ListItemRR,\n  ListItemContent,\n  ListItemTitle\n} from '../../components/Elements/List';\nimport { Text } from '../../components/Elements/Typography';\n\n\nexport default function Sidebar() {\n\n  const [threads, setThreads] = useState([]);\n\n  const isLoading = useSelector(state => state.isLoading);\n  const accessToken = useSelector(state => state.accessToken);\n\n  const navigate = useNavigate();\n\n  const dispatch = useDispatch();\n\n  const getThreads = () => {\n    dispatch(setLoadingDialog(true));\n    axios.get('/threads', {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      setThreads(response.data);\n      dispatch(setLoadingDialog(false));\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  }\n\n  useEffect(() => {\n    getThreads();\n  }, []);\n\n  return (\n    <SidebarContainer>\n      <LogoWrapper to=\"/\">\n        <Logo\n          src={neuralagent_logo_white}\n          alt=\"NeuralAgent\"\n          height={40}\n        />\n      </LogoWrapper>\n      <Button padding='7px 15px' color={'var(--primary-color)'} borderRadius={6} fontSize='15px' dark\n         onClick={() => navigate('/')}>\n        <BtnIcon left color='#fff' iconSize='23px'>\n          <MdAddCircleOutline />\n        </BtnIcon>\n        New Task\n      </Button>\n      <List padding='0px 10px' style={{marginTop: '10px', overflowY: 'auto'}}>\n        {\n          !isLoading && threads.length === 0 ? (\n            <Text style={{marginTop: '7px', padding: '8px'}}\n              fontSize='14px'\n              textAlign='center'\n              color={'rgba(255,255,255,0.7)'}>\n              You currently have no threads\n            </Text>\n          ) : (\n            <>\n              {threads.map((thread) => {\n                return (\n                  <ListItemRR key={'thread__' + thread.id} padding='10px' to={'/threads/' + thread.id} isDarkMode\n                    borderRadius='8px'\n                    style={{marginTop: '5px'}}>\n                    <ListItemContent>\n                      <ListItemTitle fontSize='14px' color='#fff' fontWeight='400'>\n                        {thread.title}\n                      </ListItemTitle>\n                    </ListItemContent>\n                  </ListItemRR>\n                )\n              })}\n            </>\n          )\n        }\n      </List>\n    </SidebarContainer>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,SAASC,OAAO,EAAEC,MAAM,QAAQ,kCAAkC;AAClE,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SACEC,gBAAgB,EAChBC,WAAW,EACXC,IAAI,QACC,mBAAmB;AAC1B,SACEC,IAAI,EACJC,UAAU,EACVC,eAAe,EACfC,aAAa,QACR,gCAAgC;AACvC,SAASC,IAAI,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG5D,eAAe,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EAEhC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM2B,SAAS,GAAGpB,WAAW,CAACqB,KAAK,IAAIA,KAAK,CAACD,SAAS,CAAC;EACvD,MAAME,WAAW,GAAGtB,WAAW,CAACqB,KAAK,IAAIA,KAAK,CAACC,WAAW,CAAC;EAE3D,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAE9B,MAAM0B,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAE9B,MAAM0B,UAAU,GAAGA,CAAA,KAAM;IACvBD,QAAQ,CAACvB,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCE,KAAK,CAACuB,GAAG,CAAC,UAAU,EAAE;MACpBC,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGL;MAC/B;IACF,CAAC,CAAC,CAACM,IAAI,CAAEC,QAAQ,IAAK;MACpBV,UAAU,CAACU,QAAQ,CAACC,IAAI,CAAC;MACzBN,QAAQ,CAACvB,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC8B,KAAK,CAAEC,KAAK,IAAK;MAClBR,QAAQ,CAACvB,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAI+B,KAAK,CAACH,QAAQ,CAACI,MAAM,KAAK/B,SAAS,CAAC+B,MAAM,CAACC,YAAY,EAAE;QAC3DC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED7C,SAAS,CAAC,MAAM;IACdiC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAACT,gBAAgB;IAAAkC,QAAA,gBACfzB,OAAA,CAACR,WAAW;MAACkC,EAAE,EAAC,GAAG;MAAAD,QAAA,eACjBzB,OAAA,CAACP,IAAI;QACHkC,GAAG,EAAE9C,sBAAuB;QAC5B+C,GAAG,EAAC,aAAa;QACjBC,MAAM,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eACdjC,OAAA,CAACjB,MAAM;MAACmD,OAAO,EAAC,UAAU;MAACC,KAAK,EAAE,sBAAuB;MAACC,YAAY,EAAE,CAAE;MAACC,QAAQ,EAAC,MAAM;MAACC,IAAI;MAC5FC,OAAO,EAAEA,CAAA,KAAM7B,QAAQ,CAAC,GAAG,CAAE;MAAAe,QAAA,gBAC9BzB,OAAA,CAAClB,OAAO;QAAC0D,IAAI;QAACL,KAAK,EAAC,MAAM;QAACM,QAAQ,EAAC,MAAM;QAAAhB,QAAA,eACxCzB,OAAA,CAAChB,kBAAkB;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,YAEZ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTjC,OAAA,CAACN,IAAI;MAACwC,OAAO,EAAC,UAAU;MAACQ,KAAK,EAAE;QAACC,SAAS,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAM,CAAE;MAAAnB,QAAA,EAEnE,CAAClB,SAAS,IAAIF,OAAO,CAACwC,MAAM,KAAK,CAAC,gBAChC7C,OAAA,CAACF,IAAI;QAAC4C,KAAK,EAAE;UAACC,SAAS,EAAE,KAAK;UAAET,OAAO,EAAE;QAAK,CAAE;QAC9CG,QAAQ,EAAC,MAAM;QACfS,SAAS,EAAC,QAAQ;QAClBX,KAAK,EAAE,uBAAwB;QAAAV,QAAA,EAAC;MAElC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEPjC,OAAA,CAAAE,SAAA;QAAAuB,QAAA,EACGpB,OAAO,CAAC0C,GAAG,CAAEC,MAAM,IAAK;UACvB,oBACEhD,OAAA,CAACL,UAAU;YAA8BuC,OAAO,EAAC,MAAM;YAACR,EAAE,EAAE,WAAW,GAAGsB,MAAM,CAACC,EAAG;YAACC,UAAU;YAC7Fd,YAAY,EAAC,KAAK;YAClBM,KAAK,EAAE;cAACC,SAAS,EAAE;YAAK,CAAE;YAAAlB,QAAA,eAC1BzB,OAAA,CAACJ,eAAe;cAAA6B,QAAA,eACdzB,OAAA,CAACH,aAAa;gBAACwC,QAAQ,EAAC,MAAM;gBAACF,KAAK,EAAC,MAAM;gBAACgB,UAAU,EAAC,KAAK;gBAAA1B,QAAA,EACzDuB,MAAM,CAACI;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAPH,UAAU,GAAGe,MAAM,CAACC,EAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQ3B,CAAC;QAEjB,CAAC;MAAC,gBACF;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEvB;AAAC7B,EAAA,CA9EuBD,OAAO;EAAA,QAIXhB,WAAW,EACTA,WAAW,EAEdF,WAAW,EAEXC,WAAW;AAAA;AAAAmE,EAAA,GATNlD,OAAO;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}