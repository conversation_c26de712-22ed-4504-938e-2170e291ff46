# is-regexp [![Build Status](https://travis-ci.org/sindresorhus/is-regexp.svg?branch=master)](https://travis-ci.org/sindresorhus/is-regexp)

> Check whether a variable is a regular expression


## Install

```sh
$ npm install --save is-regexp
```


## Usage

```js
var isRegexp = require('is-regexp');

isRegexp('unicorn');
//=> false

isRegexp(/unicorn/);
//=> true

isRegexp(new RegExp('unicorn'));
//=> true
```


## License

MIT © [Sindre Sorhus](http://sindresorhus.com)
