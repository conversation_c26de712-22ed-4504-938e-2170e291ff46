{"version": 3, "file": "batchedSingleLineTests.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/batchedSingleLineTests.ts"], "names": [], "mappings": ";;;AAiCA,SAAS,sBAAsB,CAI7B,OAAyE;IAEzE,gCAAgC;IAChC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM,MAAM,GACV,QAAQ,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM;QACnC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QACnC,CAAC,CAAC,IAAI,CAAC;IACX,OAAO,OAAO,CAAC,IAAI;SAChB,IAAI,EAAE;SACN,KAAK,CAAC,IAAI,CAAC;SACX,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;QACf,MAAM,OAAO,GAAG,CAAC,GAAG,UAAU,CAAC;QAC/B,MAAM,MAAM,GACV,QAAQ,IAAI,OAAO;YACjB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC;YAChD,CAAC,CAAC,EAAE,CAAC;QACT,MAAM,SAAS,mCACV,OAAO,KACV,IAAI,EACJ,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,iCACnB,CAAC,KACJ,IAAI,EAAE,CAAC,IACP,CAAC,GACJ,CAAC;QACF,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,CAAC,CAAC,EAAE;YACf,uCACK,SAAS,KACZ,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,IACjB;SACH;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC,CAAC;AACP,CAAC;AAEQ,wDAAsB"}