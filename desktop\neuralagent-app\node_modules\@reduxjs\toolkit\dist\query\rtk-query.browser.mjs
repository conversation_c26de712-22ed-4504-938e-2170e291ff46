var Ce=(l=>(l.uninitialized="uninitialized",l.pending="pending",l.fulfilled="fulfilled",l.rejected="rejected",l))(Ce||{});function ve(t){return{status:t,isUninitialized:t==="uninitialized",isLoading:t==="pending",isSuccess:t==="fulfilled",isError:t==="rejected"}}import{createAction as $,createSlice as Z,createSelector as je,createAsyncThunk as Fe,combineReducers as He,createNextState as ce,isAnyOf as ee,isAllOf as Qe,isAction as _e,isPending as Te,isRejected as re,isFulfilled as H,isRejectedWithValue as ie,isAsyncThunkAction as we,prepareAutoBatched as ae,SHOULD_AUTOBATCH as he,isPlainObject as G,nanoid as Ae}from"@reduxjs/toolkit";var Ve=G;function xe(t,n){if(t===n||!(Ve(t)&&Ve(n)||Array.isArray(t)&&Array.isArray(n)))return n;let p=Object.keys(n),T=Object.keys(t),l=p.length===T.length,D=Array.isArray(n)?[]:{};for(let h of p)D[h]=xe(t[h],n[h]),l&&(l=t[h]===D[h]);return l?t:D}function V(t){let n=0;for(let p in t)n++;return n}var Oe=t=>[].concat(...t);function ze(t){return new RegExp("(^|:)//").test(t)}function We(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function te(t){return t!=null}function $e(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var bt=t=>t.replace(/\/$/,""),Pt=t=>t.replace(/^\//,"");function Je(t,n){if(!t)return n;if(!n)return t;if(ze(n))return n;let p=t.endsWith("/")||!n.startsWith("?")?"/":"";return t=bt(t),n=Pt(n),`${t}${p}${n}`}function Ge(t,n,p){return t.has(n)?t.get(n):t.set(n,p).get(n)}var Ye=(...t)=>fetch(...t),Et=t=>t.status>=200&&t.status<=299,It=t=>/ion\/(vnd\.api\+)?json/.test(t.get("content-type")||"");function Xe(t){if(!G(t))return t;let n={...t};for(let[p,T]of Object.entries(n))T===void 0&&delete n[p];return n}function kt({baseUrl:t,prepareHeaders:n=Q=>Q,fetchFn:p=Ye,paramsSerializer:T,isJsonContentType:l=It,jsonContentType:D="application/json",jsonReplacer:h,timeout:E,responseHandler:B,validateStatus:x,...b}={}){return typeof fetch>"u"&&p===Ye&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(A,i,f)=>{let{getState:P,extra:c,endpoint:o,forced:R,type:d}=i,s,{url:m,headers:S=new Headers(b.headers),params:y=void 0,responseHandler:e=B??"json",validateStatus:r=x??Et,timeout:u=E,...a}=typeof A=="string"?{url:A}:A,g,k=i.signal;u&&(g=new AbortController,i.signal.addEventListener("abort",g.abort),k=g.signal);let M={...b,signal:k,...a};S=new Headers(Xe(S)),M.headers=await n(S,{getState:P,arg:A,extra:c,endpoint:o,forced:R,type:d,extraOptions:f})||S;let I=F=>typeof F=="object"&&(G(F)||Array.isArray(F)||typeof F.toJSON=="function");if(!M.headers.has("content-type")&&I(M.body)&&M.headers.set("content-type",D),I(M.body)&&l(M.headers)&&(M.body=JSON.stringify(M.body,h)),y){let F=~m.indexOf("?")?"&":"?",O=T?T(y):new URLSearchParams(Xe(y));m+=F+O}m=Je(t,m);let C=new Request(m,M);s={request:new Request(m,M)};let v,U=!1,L=g&&setTimeout(()=>{U=!0,g.abort()},u);try{v=await p(C)}catch(F){return{error:{status:U?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(F)},meta:s}}finally{L&&clearTimeout(L),g?.signal.removeEventListener("abort",g.abort)}let j=v.clone();s.response=j;let q,N="";try{let F;if(await Promise.all([Q(v,e).then(O=>q=O,O=>F=O),j.text().then(O=>N=O,()=>{})]),F)throw F}catch(F){return{error:{status:"PARSING_ERROR",originalStatus:v.status,data:N,error:String(F)},meta:s}}return r(v,q)?{data:q,meta:s}:{error:{status:v.status,data:q},meta:s}};async function Q(A,i){if(typeof i=="function")return i(A);if(i==="content-type"&&(i=l(A.headers)?"json":"text"),i==="json"){let f=await A.text();return f.length?JSON.parse(f):null}return A.text()}}var z=class{constructor(n,p=void 0){this.value=n;this.meta=p}};async function Bt(t=0,n=5){let p=Math.min(t,n),T=~~((Math.random()+.4)*(300<<p));await new Promise(l=>setTimeout(D=>l(D),T))}function Mt(t,n){throw Object.assign(new z({error:t,meta:n}),{throwImmediately:!0})}var Ze={},Ct=(t,n)=>async(p,T,l)=>{let D=[5,(n||Ze).maxRetries,(l||Ze).maxRetries].filter(b=>b!==void 0),[h]=D.slice(-1),B={maxRetries:h,backoff:Bt,retryCondition:(b,Q,{attempt:A})=>A<=h,...n,...l},x=0;for(;;)try{let b=await t(p,T,l);if(b.error)throw new z(b);return b}catch(b){if(x++,b.throwImmediately){if(b instanceof z)return b.value;throw b}if(b instanceof z&&!B.retryCondition(b.value.error,p,{attempt:x,baseQueryApi:T,extraOptions:l}))return b.value;await B.backoff(x,B.maxRetries)}},vt=Object.assign(Ct,{fail:Mt});var Y=$("__rtkq/focused"),oe=$("__rtkq/unfocused"),X=$("__rtkq/online"),se=$("__rtkq/offline"),Ne=!1;function Ft(t,n){function p(){let T=()=>t(Y()),l=()=>t(oe()),D=()=>t(X()),h=()=>t(se()),E=()=>{window.document.visibilityState==="visible"?T():l()};return Ne||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",E,!1),window.addEventListener("focus",T,!1),window.addEventListener("online",D,!1),window.addEventListener("offline",h,!1),Ne=!0),()=>{window.removeEventListener("focus",T),window.removeEventListener("visibilitychange",E),window.removeEventListener("online",D),window.removeEventListener("offline",h),Ne=!1}}return n?n(t,{onFocus:Y,onFocusLost:oe,onOffline:se,onOnline:X}):p()}function ue(t){return t.type==="query"}function et(t){return t.type==="mutation"}function ye(t){return t.type==="infinitequery"}function le(t,n,p,T,l,D){return wt(t)?t(n,p,T,l).filter(te).map(Re).map(D):Array.isArray(t)?t.map(Re).map(D):[]}function wt(t){return typeof t=="function"}function Re(t){return typeof t=="string"?{type:t}:t}import{isDraftable as Ot,produceWithPatches as Nt}from"immer";import"@reduxjs/toolkit";function tt(t,n){return t.catch(n)}var de=Symbol("forceQueryFn"),fe=t=>typeof t[de]=="function";function nt({serializeQueryArgs:t,queryThunk:n,infiniteQueryThunk:p,mutationThunk:T,api:l,context:D}){let h=new Map,E=new Map,{unsubscribeQueryResult:B,removeMutationResult:x,updateSubscriptionOptions:b}=l.internalActions;return{buildInitiateQuery:o,buildInitiateInfiniteQuery:R,buildInitiateMutation:d,getRunningQueryThunk:Q,getRunningMutationThunk:A,getRunningQueriesThunk:i,getRunningMutationsThunk:f};function Q(s,m){return S=>{let y=D.endpointDefinitions[s],e=t({queryArgs:m,endpointDefinition:y,endpointName:s});return h.get(S)?.[e]}}function A(s,m){return S=>E.get(S)?.[m]}function i(){return s=>Object.values(h.get(s)||{}).filter(te)}function f(){return s=>Object.values(E.get(s)||{}).filter(te)}function P(s){}function c(s,m){let S=(y,{subscribe:e=!0,forceRefetch:r,subscriptionOptions:u,[de]:a,...g}={})=>(k,M)=>{let I=t({queryArgs:y,endpointDefinition:m,endpointName:s}),C,w={...g,type:"query",subscribe:e,forceRefetch:r,subscriptionOptions:u,endpointName:s,originalArgs:y,queryCacheKey:I,[de]:a};if(ue(m))C=n(w);else{let{direction:K,initialPageParam:pe}=g;C=p({...w,direction:K,initialPageParam:pe})}let v=l.endpoints[s].select(y),U=k(C),L=v(M());let{requestId:j,abort:q}=U,N=L.requestId!==j,F=h.get(k)?.[I],O=()=>v(M()),_=Object.assign(a?U.then(O):N&&!F?Promise.resolve(L):Promise.all([F,U]).then(O),{arg:y,requestId:j,subscriptionOptions:u,queryCacheKey:I,abort:q,async unwrap(){let K=await _;if(K.isError)throw K.error;return K.data},refetch:()=>k(S(y,{subscribe:!1,forceRefetch:!0})),unsubscribe(){e&&k(B({queryCacheKey:I,requestId:j}))},updateSubscriptionOptions(K){_.subscriptionOptions=K,k(b({endpointName:s,requestId:j,queryCacheKey:I,options:K}))}});if(!F&&!N&&!a){let K=Ge(h,k,{});K[I]=_,_.then(()=>{delete K[I],V(K)||h.delete(k)})}return _};return S}function o(s,m){return c(s,m)}function R(s,m){return c(s,m)}function d(s){return(m,{track:S=!0,fixedCacheKey:y}={})=>(e,r)=>{let u=T({type:"mutation",endpointName:s,originalArgs:m,track:S,fixedCacheKey:y}),a=e(u);let{requestId:g,abort:k,unwrap:M}=a,I=tt(a.unwrap().then(U=>({data:U})),U=>({error:U})),C=()=>{e(x({requestId:g,fixedCacheKey:y}))},w=Object.assign(I,{arg:a.arg,requestId:g,abort:k,unwrap:M,reset:C}),v=E.get(e)||{};return E.set(e,v),v[g]=w,w.then(()=>{delete v[g],V(v)||E.delete(e)}),y&&(v[y]=w,w.then(()=>{v[y]===w&&(delete v[y],V(v)||E.delete(e))})),w}}}function qt(t){return t}var De=(t={})=>({...t,[he]:!0});function rt({reducerPath:t,baseQuery:n,context:{endpointDefinitions:p},serializeQueryArgs:T,api:l,assertTagType:D,selectors:h}){let E=(e,r,u,a)=>(g,k)=>{let M=p[e],I=T({queryArgs:r,endpointDefinition:M,endpointName:e});if(g(l.internalActions.queryResultPatched({queryCacheKey:I,patches:u})),!a)return;let C=l.endpoints[e].select(r)(k()),w=le(M.providesTags,C.data,void 0,r,{},D);g(l.internalActions.updateProvidedBy({queryCacheKey:I,providedTags:w}))};function B(e,r,u=0){let a=[r,...e];return u&&a.length>u?a.slice(0,-1):a}function x(e,r,u=0){let a=[...e,r];return u&&a.length>u?a.slice(1):a}let b=(e,r,u,a=!0)=>(g,k)=>{let I=l.endpoints[e].select(r)(k()),C={patches:[],inversePatches:[],undo:()=>g(l.util.patchQueryData(e,r,C.inversePatches,a))};if(I.status==="uninitialized")return C;let w;if("data"in I)if(Ot(I.data)){let[v,U,L]=Nt(I.data,u);C.patches.push(...U),C.inversePatches.push(...L),w=v}else w=u(I.data),C.patches.push({op:"replace",path:[],value:w}),C.inversePatches.push({op:"replace",path:[],value:I.data});return C.patches.length===0||g(l.util.patchQueryData(e,r,C.patches,a)),C},Q=(e,r,u)=>a=>a(l.endpoints[e].initiate(r,{subscribe:!1,forceRefetch:!0,[de]:()=>({data:u})})),A=(e,r)=>e.query&&e[r]?e[r]:qt,i=async(e,{signal:r,abort:u,rejectWithValue:a,fulfillWithValue:g,dispatch:k,getState:M,extra:I})=>{let C=p[e.endpointName];try{let w=A(C,"transformResponse"),v={signal:r,abort:u,dispatch:k,getState:M,extra:I,endpoint:e.endpointName,type:e.type,forced:e.type==="query"?f(e,M()):void 0,queryCacheKey:e.type==="query"?e.queryCacheKey:void 0},U=e.type==="query"?e[de]:void 0,L,j=async(N,F,O,_)=>{if(F==null&&N.pages.length)return Promise.resolve({data:N});let K={queryArg:e.originalArgs,pageParam:F},pe=await q(K),J=_?B:x;return{data:{pages:J(N.pages,pe.data,O),pageParams:J(N.pageParams,F,O)}}};async function q(N){let F,{extraOptions:O}=C;if(U?F=U():C.query?F=await n(C.query(N),v,O):F=await C.queryFn(N,v,O,K=>n(K,v,O)),typeof process<"u",F.error)throw new z(F.error,F.meta);let _=await w(F.data,F.meta,N);return{...F,data:_}}if(e.type==="query"&&"infiniteQueryOptions"in C){let{infiniteQueryOptions:N}=C,{maxPages:F=1/0}=N,O,_={pages:[],pageParams:[]},K=h.selectQueryEntry(M(),e.queryCacheKey)?.data,J=f(e,M())&&!e.direction||!K?_:K;if("direction"in e&&e.direction&&J.pages.length){let me=e.direction==="backward",Me=(me?qe:Se)(N,J);O=await j(J,Me,F,me)}else{let{initialPageParam:me=N.initialPageParam}=e,Be=K?.pageParams??[],Me=Be[0]??me,Dt=Be.length;O=await j(J,Me,F),U&&(O={data:O.data.pages[0]});for(let Le=1;Le<Dt;Le++){let St=Se(N,O.data);O=await j(O.data,St,F)}}L=O}else L=await q(e.originalArgs);return g(L.data,De({fulfilledTimeStamp:Date.now(),baseQueryMeta:L.meta}))}catch(w){let v=w;if(v instanceof z){let U=A(C,"transformErrorResponse");try{return a(await U(v.value,v.meta,e.originalArgs),De({baseQueryMeta:v.meta}))}catch(L){v=L}}throw typeof process<"u",console.error(v),v}};function f(e,r){let u=h.selectQueryEntry(r,e.queryCacheKey),a=h.selectConfig(r).refetchOnMountOrArgChange,g=u?.fulfilledTimeStamp,k=e.forceRefetch??(e.subscribe&&a);return k?k===!0||(Number(new Date)-Number(g))/1e3>=k:!1}let P=()=>Fe(`${t}/executeQuery`,i,{getPendingMeta({arg:r}){let u=p[r.endpointName];return De({startedTimeStamp:Date.now(),...ye(u)?{direction:r.direction}:{}})},condition(r,{getState:u}){let a=u(),g=h.selectQueryEntry(a,r.queryCacheKey),k=g?.fulfilledTimeStamp,M=r.originalArgs,I=g?.originalArgs,C=p[r.endpointName],w=r.direction;return fe(r)?!0:g?.status==="pending"?!1:f(r,a)||ue(C)&&C?.forceRefetch?.({currentArg:M,previousArg:I,endpointState:g,state:a})?!0:!(k&&!w)},dispatchConditionRejection:!0}),c=P(),o=P(),R=Fe(`${t}/executeMutation`,i,{getPendingMeta(){return De({startedTimeStamp:Date.now()})}}),d=e=>"force"in e,s=e=>"ifOlderThan"in e,m=(e,r,u)=>(a,g)=>{let k=d(u)&&u.force,M=s(u)&&u.ifOlderThan,I=(w=!0)=>{let v={forceRefetch:w,isPrefetch:!0};return l.endpoints[e].initiate(r,v)},C=l.endpoints[e].select(r)(g());if(k)a(I());else if(M){let w=C?.fulfilledTimeStamp;if(!w){a(I());return}(Number(new Date)-Number(new Date(w)))/1e3>=M&&a(I())}else a(I(!1))};function S(e){return r=>r?.meta?.arg?.endpointName===e}function y(e,r){return{matchPending:Qe(Te(e),S(r)),matchFulfilled:Qe(H(e),S(r)),matchRejected:Qe(re(e),S(r))}}return{queryThunk:c,mutationThunk:R,infiniteQueryThunk:o,prefetch:m,updateQueryData:b,upsertQueryData:Q,patchQueryData:E,buildMatchThunkActions:y}}function Se(t,{pages:n,pageParams:p}){let T=n.length-1;return t.getNextPageParam(n[T],n,p[T],p)}function qe(t,{pages:n,pageParams:p}){return t.getPreviousPageParam?.(n[0],n,p[0],p)}function be(t,n,p,T){return le(p[t.meta.arg.endpointName][n],H(t)?t.payload:void 0,ie(t)?t.payload:void 0,t.meta.arg.originalArgs,"baseQueryMeta"in t.meta?t.meta.baseQueryMeta:void 0,T)}import{isDraft as Kt}from"immer";import{applyPatches as it,original as Ut}from"immer";function Pe(t,n,p){let T=t[n];T&&p(T)}function ne(t){return("arg"in t?t.arg.fixedCacheKey:t.fixedCacheKey)??t.requestId}function at(t,n,p){let T=t[ne(n)];T&&p(T)}var ge={};function ot({reducerPath:t,queryThunk:n,mutationThunk:p,serializeQueryArgs:T,context:{endpointDefinitions:l,apiUid:D,extractRehydrationInfo:h,hasRehydrationInfo:E},assertTagType:B,config:x}){let b=$(`${t}/resetApiState`);function Q(y,e,r,u){y[e.queryCacheKey]??={status:"uninitialized",endpointName:e.endpointName},Pe(y,e.queryCacheKey,a=>{a.status="pending",a.requestId=r&&a.requestId?a.requestId:u.requestId,e.originalArgs!==void 0&&(a.originalArgs=e.originalArgs),a.startedTimeStamp=u.startedTimeStamp;let g=l[u.arg.endpointName];ye(g)&&"direction"in e&&(a.direction=e.direction)})}function A(y,e,r,u){Pe(y,e.arg.queryCacheKey,a=>{if(a.requestId!==e.requestId&&!u)return;let{merge:g}=l[e.arg.endpointName];if(a.status="fulfilled",g)if(a.data!==void 0){let{fulfilledTimeStamp:k,arg:M,baseQueryMeta:I,requestId:C}=e,w=ce(a.data,v=>g(v,r,{arg:M.originalArgs,baseQueryMeta:I,fulfilledTimeStamp:k,requestId:C}));a.data=w}else a.data=r;else a.data=l[e.arg.endpointName].structuralSharing??!0?xe(Kt(a.data)?Ut(a.data):a.data,r):r;delete a.error,a.fulfilledTimeStamp=e.fulfilledTimeStamp})}let i=Z({name:`${t}/queries`,initialState:ge,reducers:{removeQueryResult:{reducer(y,{payload:{queryCacheKey:e}}){delete y[e]},prepare:ae()},cacheEntriesUpserted:{reducer(y,e){for(let r of e.payload){let{queryDescription:u,value:a}=r;Q(y,u,!0,{arg:u,requestId:e.meta.requestId,startedTimeStamp:e.meta.timestamp}),A(y,{arg:u,requestId:e.meta.requestId,fulfilledTimeStamp:e.meta.timestamp,baseQueryMeta:{}},a,!0)}},prepare:y=>({payload:y.map(u=>{let{endpointName:a,arg:g,value:k}=u,M=l[a];return{queryDescription:{type:"query",endpointName:a,originalArgs:u.arg,queryCacheKey:T({queryArgs:g,endpointDefinition:M,endpointName:a})},value:k}}),meta:{[he]:!0,requestId:Ae(),timestamp:Date.now()}})},queryResultPatched:{reducer(y,{payload:{queryCacheKey:e,patches:r}}){Pe(y,e,u=>{u.data=it(u.data,r.concat())})},prepare:ae()}},extraReducers(y){y.addCase(n.pending,(e,{meta:r,meta:{arg:u}})=>{let a=fe(u);Q(e,u,a,r)}).addCase(n.fulfilled,(e,{meta:r,payload:u})=>{let a=fe(r.arg);A(e,r,u,a)}).addCase(n.rejected,(e,{meta:{condition:r,arg:u,requestId:a},error:g,payload:k})=>{Pe(e,u.queryCacheKey,M=>{if(!r){if(M.requestId!==a)return;M.status="rejected",M.error=k??g}})}).addMatcher(E,(e,r)=>{let{queries:u}=h(r);for(let[a,g]of Object.entries(u))(g?.status==="fulfilled"||g?.status==="rejected")&&(e[a]=g)})}}),f=Z({name:`${t}/mutations`,initialState:ge,reducers:{removeMutationResult:{reducer(y,{payload:e}){let r=ne(e);r in y&&delete y[r]},prepare:ae()}},extraReducers(y){y.addCase(p.pending,(e,{meta:r,meta:{requestId:u,arg:a,startedTimeStamp:g}})=>{a.track&&(e[ne(r)]={requestId:u,status:"pending",endpointName:a.endpointName,startedTimeStamp:g})}).addCase(p.fulfilled,(e,{payload:r,meta:u})=>{u.arg.track&&at(e,u,a=>{a.requestId===u.requestId&&(a.status="fulfilled",a.data=r,a.fulfilledTimeStamp=u.fulfilledTimeStamp)})}).addCase(p.rejected,(e,{payload:r,error:u,meta:a})=>{a.arg.track&&at(e,a,g=>{g.requestId===a.requestId&&(g.status="rejected",g.error=r??u)})}).addMatcher(E,(e,r)=>{let{mutations:u}=h(r);for(let[a,g]of Object.entries(u))(g?.status==="fulfilled"||g?.status==="rejected")&&a!==g?.requestId&&(e[a]=g)})}}),P=Z({name:`${t}/invalidation`,initialState:ge,reducers:{updateProvidedBy:{reducer(y,e){let{queryCacheKey:r,providedTags:u}=e.payload;for(let a of Object.values(y))for(let g of Object.values(a)){let k=g.indexOf(r);k!==-1&&g.splice(k,1)}for(let{type:a,id:g}of u){let k=(y[a]??={})[g||"__internal_without_id"]??=[];k.includes(r)||k.push(r)}},prepare:ae()}},extraReducers(y){y.addCase(i.actions.removeQueryResult,(e,{payload:{queryCacheKey:r}})=>{for(let u of Object.values(e))for(let a of Object.values(u)){let g=a.indexOf(r);g!==-1&&a.splice(g,1)}}).addMatcher(E,(e,r)=>{let{provided:u}=h(r);for(let[a,g]of Object.entries(u))for(let[k,M]of Object.entries(g)){let I=(e[a]??={})[k||"__internal_without_id"]??=[];for(let C of M)I.includes(C)||I.push(C)}}).addMatcher(ee(H(n),ie(n)),(e,r)=>{c(e,r)}).addMatcher(i.actions.cacheEntriesUpserted.match,(e,r)=>{for(let{queryDescription:u,value:a}of r.payload)c(e,{type:"UNKNOWN",payload:a,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:u}})})}});function c(y,e){let r=be(e,"providesTags",l,B),{queryCacheKey:u}=e.meta.arg;P.caseReducers.updateProvidedBy(y,P.actions.updateProvidedBy({queryCacheKey:u,providedTags:r}))}let o=Z({name:`${t}/subscriptions`,initialState:ge,reducers:{updateSubscriptionOptions(y,e){},unsubscribeQueryResult(y,e){},internal_getRTKQSubscriptions(){}}}),R=Z({name:`${t}/internalSubscriptions`,initialState:ge,reducers:{subscriptionsUpdated:{reducer(y,e){return it(y,e.payload)},prepare:ae()}}}),d=Z({name:`${t}/config`,initialState:{online:$e(),focused:We(),middlewareRegistered:!1,...x},reducers:{middlewareRegistered(y,{payload:e}){y.middlewareRegistered=y.middlewareRegistered==="conflict"||D!==e?"conflict":!0}},extraReducers:y=>{y.addCase(X,e=>{e.online=!0}).addCase(se,e=>{e.online=!1}).addCase(Y,e=>{e.focused=!0}).addCase(oe,e=>{e.focused=!1}).addMatcher(E,e=>({...e}))}}),s=He({queries:i.reducer,mutations:f.reducer,provided:P.reducer,subscriptions:R.reducer,config:d.reducer}),m=(y,e)=>s(b.match(e)?void 0:y,e),S={...d.actions,...i.actions,...o.actions,...R.actions,...f.actions,...P.actions,resetApiState:b};return{reducer:m,actions:S}}var Ee=Symbol.for("RTKQ/skipToken"),yt={status:"uninitialized"},st=ce(yt,()=>{}),ut=ce(yt,()=>{});function dt({serializeQueryArgs:t,reducerPath:n,createSelector:p}){let T=d=>st,l=d=>ut;return{buildQuerySelector:A,buildInfiniteQuerySelector:i,buildMutationSelector:f,selectInvalidatedBy:P,selectCachedArgsForQuery:c,selectApiState:h,selectQueries:E,selectMutations:x,selectQueryEntry:B,selectConfig:b};function D(d){return{...d,...ve(d.status)}}function h(d){return d[n]}function E(d){return h(d)?.queries}function B(d,s){return E(d)?.[s]}function x(d){return h(d)?.mutations}function b(d){return h(d)?.config}function Q(d,s,m){return S=>{if(S===Ee)return p(T,m);let y=t({queryArgs:S,endpointDefinition:s,endpointName:d});return p(r=>B(r,y)??st,m)}}function A(d,s){return Q(d,s,D)}function i(d,s){let{infiniteQueryOptions:m}=s;function S(y){let e={...y,...ve(y.status)},{isLoading:r,isError:u,direction:a}=e,g=a==="forward",k=a==="backward";return{...e,hasNextPage:o(m,e.data),hasPreviousPage:R(m,e.data),isFetchingNextPage:r&&g,isFetchingPreviousPage:r&&k,isFetchNextPageError:u&&g,isFetchPreviousPageError:u&&k}}return Q(d,s,S)}function f(){return d=>{let s;return typeof d=="object"?s=ne(d)??Ee:s=d,p(s===Ee?l:y=>h(y)?.mutations?.[s]??ut,D)}}function P(d,s){let m=d[n],S=new Set;for(let y of s.filter(te).map(Re)){let e=m.provided[y.type];if(!e)continue;let r=(y.id!==void 0?e[y.id]:Oe(Object.values(e)))??[];for(let u of r)S.add(u)}return Oe(Array.from(S.values()).map(y=>{let e=m.queries[y];return e?[{queryCacheKey:y,endpointName:e.endpointName,originalArgs:e.originalArgs}]:[]}))}function c(d,s){return Object.values(E(d)).filter(m=>m?.endpointName===s&&m.status!=="uninitialized").map(m=>m.originalArgs)}function o(d,s){return s?Se(d,s)!=null:!1}function R(d,s){return!s||!d.getPreviousPageParam?!1:qe(d,s)!=null}}import{formatProdErrorMessage as Lt}from"@reduxjs/toolkit";var pt=WeakMap?new WeakMap:void 0,Ie=({endpointName:t,queryArgs:n})=>{let p="",T=pt?.get(n);if(typeof T=="string")p=T;else{let l=JSON.stringify(n,(D,h)=>(h=typeof h=="bigint"?{$bigint:h.toString()}:h,h=G(h)?Object.keys(h).sort().reduce((E,B)=>(E[B]=h[B],E),{}):h,h));G(n)&&pt?.set(n,l),p=l}return`${t}(${p})`};import{weakMapMemoize as ct}from"reselect";function Ke(...t){return function(p){let T=ct(x=>p.extractRehydrationInfo?.(x,{reducerPath:p.reducerPath??"api"})),l={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...p,extractRehydrationInfo:T,serializeQueryArgs(x){let b=Ie;if("serializeQueryArgs"in x.endpointDefinition){let Q=x.endpointDefinition.serializeQueryArgs;b=A=>{let i=Q(A);return typeof i=="string"?i:Ie({...A,queryArgs:i})}}else p.serializeQueryArgs&&(b=p.serializeQueryArgs);return b(x)},tagTypes:[...p.tagTypes||[]]},D={endpointDefinitions:{},batch(x){x()},apiUid:Ae(),extractRehydrationInfo:T,hasRehydrationInfo:ct(x=>T(x)!=null)},h={injectEndpoints:B,enhanceEndpoints({addTagTypes:x,endpoints:b}){if(x)for(let Q of x)l.tagTypes.includes(Q)||l.tagTypes.push(Q);if(b)for(let[Q,A]of Object.entries(b))typeof A=="function"?A(D.endpointDefinitions[Q]):Object.assign(D.endpointDefinitions[Q]||{},A);return h}},E=t.map(x=>x.init(h,l,D));function B(x){let b=x.endpoints({query:Q=>({...Q,type:"query"}),mutation:Q=>({...Q,type:"mutation"}),infiniteQuery:Q=>({...Q,type:"infinitequery"})});for(let[Q,A]of Object.entries(b)){if(x.overrideExisting!==!0&&Q in D.endpointDefinitions){if(x.overrideExisting==="throw")throw new Error(Lt(39));typeof process<"u";continue}typeof process<"u",D.endpointDefinitions[Q]=A;for(let i of E)i.injectEndpoint(Q,A)}return h}return h.injectEndpoints({endpoints:p.endpoints})}}import{formatProdErrorMessage as jt}from"@reduxjs/toolkit";var Ht=Symbol();function _t(){return function(){throw new Error(jt(33))}}import{enablePatches as $t}from"immer";function W(t,...n){return Object.assign(t,...n)}import{produceWithPatches as Vt}from"immer";var lt=({api:t,queryThunk:n,internalState:p})=>{let T=`${t.reducerPath}/subscriptions`,l=null,D=null,{updateSubscriptionOptions:h,unsubscribeQueryResult:E}=t.internalActions,B=(i,f)=>{if(h.match(f)){let{queryCacheKey:c,requestId:o,options:R}=f.payload;return i?.[c]?.[o]&&(i[c][o]=R),!0}if(E.match(f)){let{queryCacheKey:c,requestId:o}=f.payload;return i[c]&&delete i[c][o],!0}if(t.internalActions.removeQueryResult.match(f))return delete i[f.payload.queryCacheKey],!0;if(n.pending.match(f)){let{meta:{arg:c,requestId:o}}=f,R=i[c.queryCacheKey]??={};return R[`${o}_running`]={},c.subscribe&&(R[o]=c.subscriptionOptions??R[o]??{}),!0}let P=!1;if(n.fulfilled.match(f)||n.rejected.match(f)){let c=i[f.meta.arg.queryCacheKey]||{},o=`${f.meta.requestId}_running`;P||=!!c[o],delete c[o]}if(n.rejected.match(f)){let{meta:{condition:c,arg:o,requestId:R}}=f;if(c&&o.subscribe){let d=i[o.queryCacheKey]??={};d[R]=o.subscriptionOptions??d[R]??{},P=!0}}return P},x=()=>p.currentSubscriptions,A={getSubscriptions:x,getSubscriptionCount:i=>{let P=x()[i]??{};return V(P)},isRequestSubscribed:(i,f)=>!!x()?.[i]?.[f]};return(i,f)=>{if(l||(l=JSON.parse(JSON.stringify(p.currentSubscriptions))),t.util.resetApiState.match(i))return l=p.currentSubscriptions={},D=null,[!0,!1];if(t.internalActions.internal_getRTKQSubscriptions.match(i))return[!1,A];let P=B(p.currentSubscriptions,i),c=!0;if(P){D||(D=setTimeout(()=>{let d=JSON.parse(JSON.stringify(p.currentSubscriptions)),[,s]=Vt(l,()=>d);f.next(t.internalActions.subscriptionsUpdated(s)),l=d,D=null},500));let o=typeof i.type=="string"&&!!i.type.startsWith(T),R=n.rejected.match(i)&&i.meta.condition&&!!i.meta.arg.subscribe;c=!o&&!R}return[c,!1]}};function zt(t){for(let n in t)return!1;return!0}var Wt=2147483647/1e3-1,ft=({reducerPath:t,api:n,queryThunk:p,context:T,internalState:l,selectors:{selectQueryEntry:D,selectConfig:h}})=>{let{removeQueryResult:E,unsubscribeQueryResult:B,cacheEntriesUpserted:x}=n.internalActions,b=ee(B.match,p.fulfilled,p.rejected,x.match);function Q(c){let o=l.currentSubscriptions[c];return!!o&&!zt(o)}let A={},i=(c,o,R)=>{let d=o.getState(),s=h(d);if(b(c)){let m;if(x.match(c))m=c.payload.map(S=>S.queryDescription.queryCacheKey);else{let{queryCacheKey:S}=B.match(c)?c.payload:c.meta.arg;m=[S]}f(m,o,s)}if(n.util.resetApiState.match(c))for(let[m,S]of Object.entries(A))S&&clearTimeout(S),delete A[m];if(T.hasRehydrationInfo(c)){let{queries:m}=T.extractRehydrationInfo(c);f(Object.keys(m),o,s)}};function f(c,o,R){let d=o.getState();for(let s of c){let m=D(d,s);P(s,m?.endpointName,o,R)}}function P(c,o,R,d){let m=T.endpointDefinitions[o]?.keepUnusedDataFor??d.keepUnusedDataFor;if(m===1/0)return;let S=Math.max(0,Math.min(m,Wt));if(!Q(c)){let y=A[c];y&&clearTimeout(y),A[c]=setTimeout(()=>{Q(c)||R.dispatch(E({queryCacheKey:c})),delete A[c]},S*1e3)}}return i};var gt=new Error("Promise never resolved before cacheEntryRemoved."),mt=({api:t,reducerPath:n,context:p,queryThunk:T,mutationThunk:l,internalState:D,selectors:{selectQueryEntry:h,selectApiState:E}})=>{let B=we(T),x=we(l),b=H(T,l),Q={};function A(o,R,d){let s=Q[o];s?.valueResolved&&(s.valueResolved({data:R,meta:d}),delete s.valueResolved)}function i(o){let R=Q[o];R&&(delete Q[o],R.cacheEntryRemoved())}let f=(o,R,d)=>{let s=P(o);function m(S,y,e,r){let u=h(d,y),a=h(R.getState(),y);!u&&a&&c(S,r,y,R,e)}if(T.pending.match(o))m(o.meta.arg.endpointName,s,o.meta.requestId,o.meta.arg.originalArgs);else if(t.internalActions.cacheEntriesUpserted.match(o))for(let{queryDescription:S,value:y}of o.payload){let{endpointName:e,originalArgs:r,queryCacheKey:u}=S;m(e,u,o.meta.requestId,r),A(u,y,{})}else if(l.pending.match(o))R.getState()[n].mutations[s]&&c(o.meta.arg.endpointName,o.meta.arg.originalArgs,s,R,o.meta.requestId);else if(b(o))A(s,o.payload,o.meta.baseQueryMeta);else if(t.internalActions.removeQueryResult.match(o)||t.internalActions.removeMutationResult.match(o))i(s);else if(t.util.resetApiState.match(o))for(let S of Object.keys(Q))i(S)};function P(o){return B(o)?o.meta.arg.queryCacheKey:x(o)?o.meta.arg.fixedCacheKey??o.meta.requestId:t.internalActions.removeQueryResult.match(o)?o.payload.queryCacheKey:t.internalActions.removeMutationResult.match(o)?ne(o.payload):""}function c(o,R,d,s,m){let S=p.endpointDefinitions[o],y=S?.onCacheEntryAdded;if(!y)return;let e={},r=new Promise(I=>{e.cacheEntryRemoved=I}),u=Promise.race([new Promise(I=>{e.valueResolved=I}),r.then(()=>{throw gt})]);u.catch(()=>{}),Q[d]=e;let a=t.endpoints[o].select(S.type==="query"?R:d),g=s.dispatch((I,C,w)=>w),k={...s,getCacheEntry:()=>a(s.getState()),requestId:m,extra:g,updateCachedData:S.type==="query"?I=>s.dispatch(t.util.updateQueryData(o,R,I)):void 0,cacheDataLoaded:u,cacheEntryRemoved:r},M=y(R,k);Promise.resolve(M).catch(I=>{if(I!==gt)throw I})}return f};var Qt=({api:t,context:{apiUid:n},reducerPath:p})=>(T,l)=>{t.util.resetApiState.match(T)&&l.dispatch(t.internalActions.middlewareRegistered(n)),typeof process<"u"};var Tt=({reducerPath:t,context:n,context:{endpointDefinitions:p},mutationThunk:T,queryThunk:l,api:D,assertTagType:h,refetchQuery:E,internalState:B})=>{let{removeQueryResult:x}=D.internalActions,b=ee(H(T),ie(T)),Q=ee(H(T,l),re(T,l)),A=[],i=(c,o)=>{b(c)?P(be(c,"invalidatesTags",p,h),o):Q(c)?P([],o):D.util.invalidateTags.match(c)&&P(le(c.payload,void 0,void 0,void 0,void 0,h),o)};function f(c){let{queries:o,mutations:R}=c;for(let d of[o,R])for(let s in d)if(d[s]?.status==="pending")return!0;return!1}function P(c,o){let R=o.getState(),d=R[t];if(A.push(...c),d.config.invalidationBehavior==="delayed"&&f(d))return;let s=A;if(A=[],s.length===0)return;let m=D.util.selectInvalidatedBy(R,s);n.batch(()=>{let S=Array.from(m.values());for(let{queryCacheKey:y}of S){let e=d.queries[y],r=B.currentSubscriptions[y]??{};e&&(V(r)===0?o.dispatch(x({queryCacheKey:y})):e.status!=="uninitialized"&&o.dispatch(E(e)))}})}return i};var ht=({reducerPath:t,queryThunk:n,api:p,refetchQuery:T,internalState:l})=>{let D={},h=(i,f)=>{(p.internalActions.updateSubscriptionOptions.match(i)||p.internalActions.unsubscribeQueryResult.match(i))&&x(i.payload,f),(n.pending.match(i)||n.rejected.match(i)&&i.meta.condition)&&x(i.meta.arg,f),(n.fulfilled.match(i)||n.rejected.match(i)&&!i.meta.condition)&&B(i.meta.arg,f),p.util.resetApiState.match(i)&&Q()};function E(i,f){let c=f.getState()[t].queries[i],o=l.currentSubscriptions[i];if(!(!c||c.status==="uninitialized"))return o}function B({queryCacheKey:i},f){let P=f.getState()[t],c=P.queries[i],o=l.currentSubscriptions[i];if(!c||c.status==="uninitialized")return;let{lowestPollingInterval:R,skipPollingIfUnfocused:d}=A(o);if(!Number.isFinite(R))return;let s=D[i];s?.timeout&&(clearTimeout(s.timeout),s.timeout=void 0);let m=Date.now()+R;D[i]={nextPollTimestamp:m,pollingInterval:R,timeout:setTimeout(()=>{(P.config.focused||!d)&&f.dispatch(T(c)),B({queryCacheKey:i},f)},R)}}function x({queryCacheKey:i},f){let c=f.getState()[t].queries[i],o=l.currentSubscriptions[i];if(!c||c.status==="uninitialized")return;let{lowestPollingInterval:R}=A(o);if(!Number.isFinite(R)){b(i);return}let d=D[i],s=Date.now()+R;(!d||s<d.nextPollTimestamp)&&B({queryCacheKey:i},f)}function b(i){let f=D[i];f?.timeout&&clearTimeout(f.timeout),delete D[i]}function Q(){for(let i of Object.keys(D))b(i)}function A(i={}){let f=!1,P=Number.POSITIVE_INFINITY;for(let c in i)i[c].pollingInterval&&(P=Math.min(i[c].pollingInterval,P),f=i[c].skipPollingIfUnfocused||f);return{lowestPollingInterval:P,skipPollingIfUnfocused:f}}return h};var At=({api:t,context:n,queryThunk:p,mutationThunk:T})=>{let l=Te(p,T),D=re(p,T),h=H(p,T),E={};return(x,b)=>{if(l(x)){let{requestId:Q,arg:{endpointName:A,originalArgs:i}}=x.meta,f=n.endpointDefinitions[A],P=f?.onQueryStarted;if(P){let c={},o=new Promise((m,S)=>{c.resolve=m,c.reject=S});o.catch(()=>{}),E[Q]=c;let R=t.endpoints[A].select(f.type==="query"?i:Q),d=b.dispatch((m,S,y)=>y),s={...b,getCacheEntry:()=>R(b.getState()),requestId:Q,extra:d,updateCachedData:f.type==="query"?m=>b.dispatch(t.util.updateQueryData(A,i,m)):void 0,queryFulfilled:o};P(i,s)}}else if(h(x)){let{requestId:Q,baseQueryMeta:A}=x.meta;E[Q]?.resolve({data:x.payload,meta:A}),delete E[Q]}else if(D(x)){let{requestId:Q,rejectedWithValue:A,baseQueryMeta:i}=x.meta;E[Q]?.reject({error:x.payload??x.error,isUnhandledError:!A,meta:i}),delete E[Q]}}};var xt=({reducerPath:t,context:n,api:p,refetchQuery:T,internalState:l})=>{let{removeQueryResult:D}=p.internalActions,h=(B,x)=>{Y.match(B)&&E(x,"refetchOnFocus"),X.match(B)&&E(x,"refetchOnReconnect")};function E(B,x){let b=B.getState()[t],Q=b.queries,A=l.currentSubscriptions;n.batch(()=>{for(let i of Object.keys(A)){let f=Q[i],P=A[i];if(!P||!f)continue;(Object.values(P).some(o=>o[x]===!0)||Object.values(P).every(o=>o[x]===void 0)&&b.config[x])&&(V(P)===0?B.dispatch(D({queryCacheKey:i})):f.status!=="uninitialized"&&B.dispatch(T(f)))}})}return h};function Rt(t){let{reducerPath:n,queryThunk:p,api:T,context:l}=t,{apiUid:D}=l,h={invalidateTags:$(`${n}/invalidateTags`)},E=Q=>Q.type.startsWith(`${n}/`),B=[Qt,ft,Tt,ht,mt,At];return{middleware:Q=>{let A=!1,f={...t,internalState:{currentSubscriptions:{}},refetchQuery:b,isThisApiSliceAction:E},P=B.map(R=>R(f)),c=lt(f),o=xt(f);return R=>d=>{if(!_e(d))return R(d);A||(A=!0,Q.dispatch(T.internalActions.middlewareRegistered(D)));let s={...Q,next:R},m=Q.getState(),[S,y]=c(d,s,m),e;if(S?e=R(d):e=y,Q.getState()[n]&&(o(d,s,m),E(d)||l.hasRehydrationInfo(d)))for(let r of P)r(d,s,m);return e}},actions:h};function b(Q){return t.api.endpoints[Q.endpointName].initiate(Q.originalArgs,{subscribe:!1,forceRefetch:!0})}}var ke=Symbol(),Ue=({createSelector:t=je}={})=>({name:ke,init(n,{baseQuery:p,tagTypes:T,reducerPath:l,serializeQueryArgs:D,keepUnusedDataFor:h,refetchOnMountOrArgChange:E,refetchOnFocus:B,refetchOnReconnect:x,invalidationBehavior:b},Q){$t();let A=q=>(typeof process<"u",q);Object.assign(n,{reducerPath:l,endpoints:{},internalActions:{onOnline:X,onOffline:se,onFocus:Y,onFocusLost:oe},util:{}});let i=dt({serializeQueryArgs:D,reducerPath:l,createSelector:t}),{selectInvalidatedBy:f,selectCachedArgsForQuery:P,buildQuerySelector:c,buildInfiniteQuerySelector:o,buildMutationSelector:R}=i;W(n.util,{selectInvalidatedBy:f,selectCachedArgsForQuery:P});let{queryThunk:d,infiniteQueryThunk:s,mutationThunk:m,patchQueryData:S,updateQueryData:y,upsertQueryData:e,prefetch:r,buildMatchThunkActions:u}=rt({baseQuery:p,reducerPath:l,context:Q,api:n,serializeQueryArgs:D,assertTagType:A,selectors:i}),{reducer:a,actions:g}=ot({context:Q,queryThunk:d,infiniteQueryThunk:s,mutationThunk:m,serializeQueryArgs:D,reducerPath:l,assertTagType:A,config:{refetchOnFocus:B,refetchOnReconnect:x,refetchOnMountOrArgChange:E,keepUnusedDataFor:h,reducerPath:l,invalidationBehavior:b}});W(n.util,{patchQueryData:S,updateQueryData:y,upsertQueryData:e,prefetch:r,resetApiState:g.resetApiState,upsertQueryEntries:g.cacheEntriesUpserted}),W(n.internalActions,g);let{middleware:k,actions:M}=Rt({reducerPath:l,context:Q,queryThunk:d,mutationThunk:m,infiniteQueryThunk:s,api:n,assertTagType:A,selectors:i});W(n.util,M),W(n,{reducer:a,middleware:k});let{buildInitiateQuery:I,buildInitiateInfiniteQuery:C,buildInitiateMutation:w,getRunningMutationThunk:v,getRunningMutationsThunk:U,getRunningQueriesThunk:L,getRunningQueryThunk:j}=nt({queryThunk:d,mutationThunk:m,infiniteQueryThunk:s,api:n,serializeQueryArgs:D,context:Q});return W(n.util,{getRunningMutationThunk:v,getRunningMutationsThunk:U,getRunningQueryThunk:j,getRunningQueriesThunk:L}),{name:ke,injectEndpoint(q,N){let F=n,O=F.endpoints[q]??={};ue(N)&&W(O,{name:q,select:c(q,N),initiate:I(q,N)},u(d,q)),et(N)&&W(O,{name:q,select:R(),initiate:w(q)},u(m,q)),ye(N)&&W(O,{name:q,select:o(q,N),initiate:C(q,N)},u(d,q))}}}});var Jt=Ke(Ue());export{Ce as QueryStatus,Ht as _NEVER,Ke as buildCreateApi,xe as copyWithStructuralSharing,Ue as coreModule,ke as coreModuleName,Jt as createApi,Ie as defaultSerializeQueryArgs,_t as fakeBaseQuery,kt as fetchBaseQuery,vt as retry,Ft as setupListeners,Ee as skipToken};
//# sourceMappingURL=rtk-query.browser.mjs.map