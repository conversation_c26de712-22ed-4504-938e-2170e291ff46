{"version": 3, "file": "ScopeBase.js", "sourceRoot": "", "sources": ["../../src/scope/ScopeBase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,sCAAmC;AAEnC,8CAA+C;AAC/C,8BAA0C;AAE1C,uDAIiC;AAEjC,0CAAuC;AAKvC,2CAAwC;AAGxC;;GAEG;AACH,SAAS,aAAa,CACpB,KAAY,EACZ,KAAoB,EACpB,kBAA2B;;IAE3B,IAAI,IAAmE,CAAC;IAExE,qEAAqE;IACrE,IAAI,MAAA,KAAK,CAAC,KAAK,0CAAE,QAAQ,EAAE;QACzB,OAAO,IAAI,CAAC;KACb;IAED,IAAI,kBAAkB,EAAE;QACtB,OAAO,IAAI,CAAC;KACb;IAED,IACE,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,KAAK;QAC9B,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,eAAe;QACxC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,YAAY;QACrC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,UAAU;QACnC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM;QAC/B,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM;QAC/B,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,QAAQ;QACjC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,IAAI,EAC7B;QACA,OAAO,IAAI,CAAC;KACb;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM,EAAE;QACrE,OAAO,KAAK,CAAC;KACd;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,QAAQ,EAAE;QACrC,MAAM,YAAY,GAAG,KAA+B,CAAC;QACrD,QAAQ,YAAY,CAAC,IAAI,EAAE;YACzB,KAAK,sBAAc,CAAC,uBAAuB;gBACzC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;oBAC5D,OAAO,KAAK,CAAC;iBACd;gBACD,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;gBACzB,MAAM;YAER,KAAK,sBAAc,CAAC,OAAO;gBACzB,IAAI,GAAG,YAAY,CAAC;gBACpB,MAAM;YAER;gBACE,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;SAC5B;QAED,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,KAAK,CAAC;SACd;KACF;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM,EAAE;QAC1C,IAAI,GAAG,KAA6B,CAAC;KACtC;SAAM;QACL,OAAO,KAAK,CAAC;KACd;IAED,iCAAiC;IACjC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;QAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;YACpD,MAAM;SACP;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,EAAE;YACnC,OAAO,IAAI,CAAC;SACb;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;YACxC,MAAM;SACP;QACD,IAAI,IAAI,CAAC,GAAG,KAAK,cAAc,IAAI,IAAI,CAAC,GAAG,KAAK,cAAc,EAAE;YAC9D,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE;YAC/B,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,YAA0B,EAAE,KAAY;IAC7D,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEhC,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEzD,IAAI,MAAM,EAAE;QACV,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACpB;SAAM;QACL,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;KACpD;AACH,CAAC;AAED,MAAM,SAAS,GAAG,IAAA,sBAAiB,GAAE,CAAC;AAGtC,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC;IACnC,qBAAS,CAAC,qBAAqB;IAC/B,qBAAS,CAAC,gBAAgB;IAC1B,qBAAS,CAAC,QAAQ;IAClB,qBAAS,CAAC,MAAM;IAChB,qBAAS,CAAC,MAAM;IAChB,qBAAS,CAAC,QAAQ;CACnB,CAAC,CAAC;AAGH,MAAe,SAAS;IA0FtB,YACE,YAA0B,EAC1B,IAAW,EACX,UAAkB,EAClB,KAAa,EACb,kBAA2B;QA1F7B;;WAEG;QACa,QAAG,GAAW,SAAS,EAAE,CAAC;QAO1C;;;WAGG;QACa,gBAAW,GAAY,EAAE,CAAC;QAC1C;;;WAGG;QACM,+CAAuD;QAChE;;;;;WAKG;QACH,qCAAkB;QAClB;;;WAGG;QACa,4BAAuB,GAAY,KAAK,CAAC;QAMzD;;;WAGG;QACO,kBAAa,GAAuB,EAAE,CAAC;QACjD;;;;;;WAMG;QACa,eAAU,GAAgB,EAAE,CAAC;QAC7C;;;WAGG;QACa,QAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;QAClD;;;WAGG;QACa,YAAO,GAAgB,EAAE,CAAC;QAW1C;;;;;;WAMG;QACa,cAAS,GAAe,EAAE,CAAC;QAwF3C,oCAAkB,CAAC,GAAc,EAAQ,EAAE;YACzC,MAAM,OAAO,GAAG,GAAY,EAAE;gBAC5B,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;gBACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAEpC,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO,KAAK,CAAC;iBACd;gBAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;oBAC1C,OAAO,KAAK,CAAC;iBACd;gBAED,gEAAgE;gBAChE,MAAM,oBAAoB,GACxB,GAAG,CAAC,eAAe,IAAI,QAAQ,CAAC,cAAc,CAAC;gBACjD,MAAM,qBAAqB,GACzB,GAAG,CAAC,gBAAgB,IAAI,QAAQ,CAAC,eAAe,CAAC;gBACnD,IAAI,CAAC,oBAAoB,IAAI,CAAC,qBAAqB,EAAE;oBACnD,OAAO,KAAK,CAAC;iBACd;gBAED,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAExB,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;YAEF,IAAI,CAAC,OAAO,EAAE,EAAE;gBACd,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;aAChC;QACH,CAAC,EAAC;QAEF,qCAAmB,CAAC,GAAc,EAAQ,EAAE;YAC1C,yCAAyC;YACzC,IAAI,OAAO,GAAG,IAAoB,CAAC;YAEnC,GAAG;gBACD,OAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,OAAO,GAAG,OAAQ,CAAC,KAAK,CAAC;aAC1B,QAAQ,OAAO,EAAE;QACpB,CAAC,EAAC;QAEF,oCAAkB,CAAC,GAAc,EAAE,YAA0B,EAAQ,EAAE;YACrE,8DAA8D;YAC9D,yCAAyC;YACzC,IAAI,IAAI,CAAC,8BAA8B,CAAC,GAAG,EAAE,YAAY,CAAC,EAAE;gBAC1D,uBAAA,IAAI,iCAAgB,MAApB,IAAI,EAAiB,GAAG,CAAC,CAAC;aAC3B;iBAAM;gBACL,uBAAA,IAAI,kCAAiB,MAArB,IAAI,EAAkB,GAAG,CAAC,CAAC;aAC5B;QACH,CAAC,EAAC;QA5HA,MAAM,qBAAqB,GAAG,UAAmB,CAAC;QAElD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,uBAAA,IAAI,sBACF,IAAI,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAS,CAAC,IAAI,MAAA,CAAC;QACjE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,EAAE;YACzC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,qBAAqB,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QAExB;;;WAGG;QACH,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAa,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;QAExE,IAAI,qBAAqB,EAAE;YACzB,8CAA8C;YAC9C,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,IAAa,CAAC,CAAC;SACvD;QAED,uBAAA,IAAI,gCAAsB,YAAY,CAAC,iBAAiB,MAAA,CAAC;QAEzD,aAAa,CAAC,YAAY,EAAE,IAAa,CAAC,CAAC;IAC7C,CAAC;IAEO,eAAe;QACrB,OAAO,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,qBAAqB;QAC1B,OAAO,CAAC,uBAAA,IAAI,0BAAS,CAAC;IACxB,CAAC;IAEO,8BAA8B,CACpC,GAAc,EACd,YAA0B;QAE1B,+EAA+E;QAC/E,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,KAAK,CAAC;SACd;QACD,+BAA+B;QAE/B,oFAAoF;QACpF,IAAI,YAAY,CAAC,QAAQ,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAC;SACb;QAED,mEAAmE;QACnE,WAAW;QACX,0EAA0E;QAC1E,sEAAsE;QACtE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,OAAO,CACL,IAAI,CAAC,MAAM,GAAG,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;;gBACf,IACE,GAAG,CAAC,IAAI,KAAK,2BAAc,CAAC,QAAQ;oBACpC,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,mBAAmB;oBACvD,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EACzB;oBACA,OAAO,KAAK,CAAC;iBACd;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAuDM,KAAK,CAAC,YAA0B;QACrC,IAAI,QAA8D,CAAC;QAEnE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;YAChC,QAAQ,GAAG,uBAAA,IAAI,iCAAgB,CAAC;SACjC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YACjC,QAAQ,GAAG,uBAAA,IAAI,kCAAiB,CAAC;SAClC;aAAM;YACL,QAAQ,GAAG,uBAAA,IAAI,iCAAgB,CAAC;SACjC;QAED,8CAA8C;QAC9C,IAAA,eAAM,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;OAGG;IACO,iBAAiB,CAAC,IAAe,EAAE,SAAmB;QAC9D,OAAO,IAAI,CAAC;IACd,CAAC;IAES,oBAAoB,CAAC,GAAc;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,KAA0B,CAAC;QAC9C,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,aAAa,EAAE;YACxB,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,0BAA0B,CAChC,QAAkB,EAClB,IAAsC;QAEtC,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO;SACR;QAED,IAAI,SAAS,GAAG,uBAAA,IAAI,oCAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,SAAS,GAAG,EAAE,CAAC;YACf,uBAAA,IAAI,oCAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACjC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC1B;IACH,CAAC;IAES,cAAc,CACtB,cAAiC,EACjC,GAA0B,EAC1B,SAAqB,EACrB,IAAgC,EAChC,GAAsB;QAEtB,MAAM,IAAI,GACR,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;QAC5E,IAAI,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ;gBACN,OAAO,cAAc,KAAK,QAAQ;oBAChC,CAAC,CAAC,IAAI,mBAAQ,CAAC,IAAI,EAAE,IAAa,CAAC;oBACnC,CAAC,CAAC,cAAc,CAAC;YACrB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACxB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC1B;QAED,IAAI,GAAG,EAAE;YACP,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;SACvD;QACD,IAAI,IAAI,EAAE;YACR,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjC;IACH,CAAC;IAEM,gBAAgB,CAAC,IAAyB,EAAE,GAAe;QAChE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAEM,uBAAuB,CAC5B,IAA4B,EAC5B,GAAe;QAEf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAEM,cAAc,CACnB,IAAkD,EAClD,SAAwB,yBAAa,CAAC,IAAI,EAC1C,SAAsC,EACtC,mBAAoD,EACpD,IAAI,GAAG,KAAK;;QAEZ,MAAM,GAAG,GAAG,IAAI,qBAAS,CACvB,IAAI,EACJ,IAAa,EACb,MAAM,EACN,SAAS,EACT,mBAAmB,EACnB,IAAI,EACJ,6BAAiB,CAAC,KAAK,CACxB,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAA,IAAI,CAAC,aAAa,0CAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,aAAa,CAAC,IAAyB;;QAC5C,MAAM,GAAG,GAAG,IAAI,qBAAS,CACvB,IAAI,EACJ,IAAa,EACb,yBAAa,CAAC,IAAI,EAClB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,6BAAiB,CAAC,IAAI,CACvB,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAA,IAAI,CAAC,aAAa,0CAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,sBAAsB,CAAC,IAAyB;;QACrD,MAAM,GAAG,GAAG,IAAI,qBAAS,CACvB,IAAI,EACJ,IAAa,EACb,yBAAa,CAAC,IAAI,EAClB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,6BAAiB,CAAC,IAAI,GAAG,6BAAiB,CAAC,KAAK,CACjD,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAA,IAAI,CAAC,aAAa,0CAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;CACF;AAEQ,8BAAS"}