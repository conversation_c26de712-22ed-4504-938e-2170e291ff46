import{j as e}from"./jsx-runtime-QvZ8i92b.js";import{c as x,a as r}from"./animation-CmKoZPRv.js";import"./index-uubelm5h.js";const v=x("ScaleLoader","0% {transform: scaley(1.0)} 50% {transform: scaley(0.4)} 100% {transform: scaley(1.0)}","scale");function s({loading:t=!0,color:l="#000000",speedMultiplier:d=1,cssOverride:p={},height:c=35,width:m=4,radius:y=2,margin:b=2,...g}){const h={display:"inherit",...p},a=f=>({backgroundColor:l,width:r(m),height:r(c),margin:r(b),borderRadius:r(y),display:"inline-block",animation:`${v} ${1/d}s ${f*.1}s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08)`,animationFillMode:"both"});return t?e.jsxs("span",{style:h,...g,children:[e.jsx("span",{style:a(1)}),e.jsx("span",{style:a(2)}),e.jsx("span",{style:a(3)}),e.jsx("span",{style:a(4)}),e.jsx("span",{style:a(5)})]}):null}try{s.displayName="ScaleLoader",s.__docgenInfo={description:"",displayName:"ScaleLoader",props:{height:{defaultValue:{value:"35"},description:"",name:"height",required:!1,type:{name:"LengthType"}},width:{defaultValue:{value:"4"},description:"",name:"width",required:!1,type:{name:"LengthType"}},radius:{defaultValue:{value:"2"},description:"",name:"radius",required:!1,type:{name:"LengthType"}},margin:{defaultValue:{value:"2"},description:"",name:"margin",required:!1,type:{name:"LengthType"}},color:{defaultValue:{value:"#000000"},description:"",name:"color",required:!1,type:{name:"string"}},loading:{defaultValue:{value:"true"},description:"",name:"loading",required:!1,type:{name:"boolean"}},cssOverride:{defaultValue:{value:"{}"},description:"",name:"cssOverride",required:!1,type:{name:"CSSProperties"}},speedMultiplier:{defaultValue:{value:"1"},description:"",name:"speedMultiplier",required:!1,type:{name:"number"}}}}}catch{}const q={component:s,argTypes:{height:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}},width:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}},margin:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}},radius:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}}}},n={};var i,o,u;n.parameters={...n.parameters,docs:{...(i=n.parameters)==null?void 0:i.docs,source:{originalSource:"{}",...(u=(o=n.parameters)==null?void 0:o.docs)==null?void 0:u.source}}};const L=["Primary"];export{n as Primary,L as __namedExportsOrder,q as default};
