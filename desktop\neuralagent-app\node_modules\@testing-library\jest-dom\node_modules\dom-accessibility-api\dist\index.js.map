{"version": 3, "file": "index.js", "names": ["_accessibleDescription", "require", "exports", "computeAccessibleDescription", "_accessibleName", "computeAccessibleName", "_getRole", "_interopRequireDefault", "getRole", "default", "_isInaccessible", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "_isDisabled", "isDisabled", "obj", "__esModule"], "sources": ["../sources/index.ts"], "sourcesContent": ["export { computeAccessibleDescription } from \"./accessible-description\";\nexport { computeAccessibleName } from \"./accessible-name\";\nexport { default as getRole } from \"./getRole\";\nexport * from \"./is-inaccessible\";\nexport { isDisabled } from \"./is-disabled\";\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAwEC,OAAA,CAAAC,4BAAA,GAAAH,sBAAA,CAAAG,4BAAA;AACxE,IAAAC,eAAA,GAAAH,OAAA;AAA0DC,OAAA,CAAAG,qBAAA,GAAAD,eAAA,CAAAC,qBAAA;AAC1D,IAAAC,QAAA,GAAAC,sBAAA,CAAAN,OAAA;AAA+CC,OAAA,CAAAM,OAAA,GAAAF,QAAA,CAAAG,OAAA;AAC/C,IAAAC,eAAA,GAAAT,OAAA;AAAAU,MAAA,CAAAC,IAAA,CAAAF,eAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAZ,OAAA,IAAAA,OAAA,CAAAY,GAAA,MAAAJ,eAAA,CAAAI,GAAA;EAAAZ,OAAA,CAAAY,GAAA,IAAAJ,eAAA,CAAAI,GAAA;AAAA;AACA,IAAAK,WAAA,GAAAlB,OAAA;AAA2CC,OAAA,CAAAkB,UAAA,GAAAD,WAAA,CAAAC,UAAA;AAAA,SAAAb,uBAAAc,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAZ,OAAA,EAAAY,GAAA"}