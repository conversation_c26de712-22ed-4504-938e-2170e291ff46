/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { DiffOptions } from 'jest-matcher-utils';
import type { AssertionErrorWithStack } from './types';
declare function assertionErrorMessage(error: AssertionErrorWithStack, options: DiffOptions): string;
export default assertionErrorMessage;
