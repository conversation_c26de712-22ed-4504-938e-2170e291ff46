{"ast": null, "code": "import styled from 'styled-components';\nimport { NavLink as LinkRR } from 'react-router-dom';\nexport const List = styled.div`\n  display: flex;\n  flex-direction: column;\n  padding: ${props => props.padding ? props.padding : '0px'};\n`;\nexport const ListItem = styled.div`\n  display: flex;\n  align-items: center;\n  cursor: ${props => props.clickable ? 'pointer' : 'auto'};\n  color: var(--primary-color);\n  text-decoration: none;\n  padding: ${props => props.padding ? props.padding : '15px'};\n  border-radius: ${props => props.borderRadius ? props.borderRadius : '0px'};\n  background: ${props => props.active && !props.disableHover ? props.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)' : 'unset'};\n  user-select: none;\n\n  &:hover {\n    background: ${props => !props.disableHover ? props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)' : 'unset'};\n  }\n`;\nexport const ListItemRR = styled(LinkRR)`\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  color: var(--primary-color);\n  text-decoration: none;\n  padding: ${props => props.padding ? props.padding : '15px'};\n  border-radius: ${props => props.borderRadius ? props.borderRadius : '0px'};\n  user-select: none;\n\n  &:hover {\n    background: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};\n  }\n\n  &.active {\n    background: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};\n  }\n`;\nexport const ListItemIcon = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nexport const ListItemImg = styled.img`\n  width: ${props => props.size ? props.size : '50px'};\n  height: ${props => props.size ? props.size : '50px'};\n  object-fit: cover;\n  -o-object-fit: cover;\n  object-position: center;\n  background: #fff;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  user-select: none;\n  pointer-events: none;\n`;\nexport const ListItemContent = styled.div`\n  display: flex;\n  flex-direction: column;\n  padding: 0px 15px;\n`;\nexport const ListItemTitle = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '600'};\n  color: ${props => props.color ? props.color : '#000'};\n`;\nexport const ListItemSubtitle = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '14px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '400'};\n  color: ${props => props.color ? props.color : 'rgba(0, 0, 0, 0.5)'};\n`;\nexport const ListItemThirdTxt = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '14px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '400'};\n  color: ${props => props.color ? props.color : 'rgba(0, 0, 0, 0.5)'};\n`;\nexport const ListItemEnd = styled.div`\n  margin-left: ${props => props.isRTL ? '0' : 'auto'};\n  margin-right: ${props => props.isRTL ? 'auto' : '0'};\n`;", "map": {"version": 3, "names": ["styled", "NavLink", "LinkRR", "List", "div", "props", "padding", "ListItem", "clickable", "borderRadius", "active", "disableHover", "isDarkMode", "ListItemRR", "ListItemIcon", "iconSize", "color", "ListItemImg", "img", "size", "ListItemContent", "ListItemTitle", "fontSize", "fontWeight", "ListItemSubtitle", "ListItemThirdTxt", "ListItemEnd", "isRTL"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/List.js"], "sourcesContent": ["import styled from 'styled-components';\nimport { NavLink as LinkRR } from 'react-router-dom';\n\nexport const List = styled.div`\n  display: flex;\n  flex-direction: column;\n  padding: ${props => props.padding ? props.padding : '0px'};\n`\n\nexport const ListItem = styled.div`\n  display: flex;\n  align-items: center;\n  cursor: ${props => props.clickable ? 'pointer' : 'auto'};\n  color: var(--primary-color);\n  text-decoration: none;\n  padding: ${props => props.padding ? props.padding : '15px'};\n  border-radius: ${props => props.borderRadius ? props.borderRadius : '0px'};\n  background: ${props => (props.active && !props.disableHover) ? (props.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)') : 'unset'};\n  user-select: none;\n\n  &:hover {\n    background: ${props => !props.disableHover ? (props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)') : 'unset'};\n  }\n`\n\nexport const ListItemRR = styled(LinkRR)`\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  color: var(--primary-color);\n  text-decoration: none;\n  padding: ${props => props.padding ? props.padding : '15px'};\n  border-radius: ${props => props.borderRadius ? props.borderRadius : '0px'};\n  user-select: none;\n\n  &:hover {\n    background: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};\n  }\n\n  &.active {\n    background: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};\n  }\n`\n\nexport const ListItemIcon = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`\n\nexport const ListItemImg = styled.img`\n  width: ${props => props.size ? props.size : '50px'};\n  height: ${props => props.size ? props.size : '50px'};\n  object-fit: cover;\n  -o-object-fit: cover;\n  object-position: center;\n  background: #fff;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  user-select: none;\n  pointer-events: none;\n`\n\nexport const ListItemContent = styled.div`\n  display: flex;\n  flex-direction: column;\n  padding: 0px 15px;\n`\n\nexport const ListItemTitle = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '600'};\n  color: ${props => props.color ? props.color : '#000'};\n`\n\nexport const ListItemSubtitle = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '14px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '400'};\n  color: ${props => props.color ? props.color : 'rgba(0, 0, 0, 0.5)'};\n`\n\nexport const ListItemThirdTxt = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '14px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '400'};\n  color: ${props => props.color ? props.color : 'rgba(0, 0, 0, 0.5)'};\n`\n\nexport const ListItemEnd = styled.div`\n  margin-left: ${props => props.isRTL ? '0' : 'auto'};\n  margin-right: ${props => props.isRTL ? 'auto' : '0'};\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,IAAIC,MAAM,QAAQ,kBAAkB;AAEpD,OAAO,MAAMC,IAAI,GAAGH,MAAM,CAACI,GAAG;AAC9B;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAGD,KAAK,CAACC,OAAO,GAAG,KAAK;AAC3D,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAGP,MAAM,CAACI,GAAG;AAClC;AACA;AACA,YAAYC,KAAK,IAAIA,KAAK,CAACG,SAAS,GAAG,SAAS,GAAG,MAAM;AACzD;AACA;AACA,aAAaH,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAGD,KAAK,CAACC,OAAO,GAAG,MAAM;AAC5D,mBAAmBD,KAAK,IAAIA,KAAK,CAACI,YAAY,GAAGJ,KAAK,CAACI,YAAY,GAAG,KAAK;AAC3E,gBAAgBJ,KAAK,IAAKA,KAAK,CAACK,MAAM,IAAI,CAACL,KAAK,CAACM,YAAY,GAAKN,KAAK,CAACO,UAAU,GAAG,0BAA0B,GAAG,oBAAoB,GAAI,OAAO;AACjJ;AACA;AACA;AACA,kBAAkBP,KAAK,IAAI,CAACA,KAAK,CAACM,YAAY,GAAIN,KAAK,CAACO,UAAU,GAAG,2BAA2B,GAAG,qBAAqB,GAAI,OAAO;AACnI;AACA,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGb,MAAM,CAACE,MAAM,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,aAAaG,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAGD,KAAK,CAACC,OAAO,GAAG,MAAM;AAC5D,mBAAmBD,KAAK,IAAIA,KAAK,CAACI,YAAY,GAAGJ,KAAK,CAACI,YAAY,GAAG,KAAK;AAC3E;AACA;AACA;AACA,kBAAkBJ,KAAK,IAAIA,KAAK,CAACO,UAAU,GAAG,2BAA2B,GAAG,qBAAqB;AACjG;AACA;AACA;AACA,kBAAkBP,KAAK,IAAIA,KAAK,CAACO,UAAU,GAAG,2BAA2B,GAAG,qBAAqB;AACjG;AACA,CAAC;AAED,OAAO,MAAME,YAAY,GAAGd,MAAM,CAACI,GAAG;AACtC,eAAeC,KAAK,IAAIA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,GAAG,MAAM;AAChE,YAAYV,KAAK,IAAIA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,GAAG,MAAM;AAC7D,WAAWV,KAAK,IAAIA,KAAK,CAACW,KAAK,GAAGX,KAAK,CAACW,KAAK,GAAG,MAAM;AACtD;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGjB,MAAM,CAACkB,GAAG;AACrC,WAAWb,KAAK,IAAIA,KAAK,CAACc,IAAI,GAAGd,KAAK,CAACc,IAAI,GAAG,MAAM;AACpD,YAAYd,KAAK,IAAIA,KAAK,CAACc,IAAI,GAAGd,KAAK,CAACc,IAAI,GAAG,MAAM;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,eAAe,GAAGpB,MAAM,CAACI,GAAG;AACzC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMiB,aAAa,GAAGrB,MAAM,CAACI,GAAG;AACvC,eAAeC,KAAK,IAAIA,KAAK,CAACiB,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ,GAAG,MAAM;AAChE,iBAAiBjB,KAAK,IAAIA,KAAK,CAACkB,UAAU,GAAGlB,KAAK,CAACkB,UAAU,GAAG,KAAK;AACrE,WAAWlB,KAAK,IAAIA,KAAK,CAACW,KAAK,GAAGX,KAAK,CAACW,KAAK,GAAG,MAAM;AACtD,CAAC;AAED,OAAO,MAAMQ,gBAAgB,GAAGxB,MAAM,CAACI,GAAG;AAC1C,eAAeC,KAAK,IAAIA,KAAK,CAACiB,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ,GAAG,MAAM;AAChE,iBAAiBjB,KAAK,IAAIA,KAAK,CAACkB,UAAU,GAAGlB,KAAK,CAACkB,UAAU,GAAG,KAAK;AACrE,WAAWlB,KAAK,IAAIA,KAAK,CAACW,KAAK,GAAGX,KAAK,CAACW,KAAK,GAAG,oBAAoB;AACpE,CAAC;AAED,OAAO,MAAMS,gBAAgB,GAAGzB,MAAM,CAACI,GAAG;AAC1C,eAAeC,KAAK,IAAIA,KAAK,CAACiB,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ,GAAG,MAAM;AAChE,iBAAiBjB,KAAK,IAAIA,KAAK,CAACkB,UAAU,GAAGlB,KAAK,CAACkB,UAAU,GAAG,KAAK;AACrE,WAAWlB,KAAK,IAAIA,KAAK,CAACW,KAAK,GAAGX,KAAK,CAACW,KAAK,GAAG,oBAAoB;AACpE,CAAC;AAED,OAAO,MAAMU,WAAW,GAAG1B,MAAM,CAACI,GAAG;AACrC,iBAAiBC,KAAK,IAAIA,KAAK,CAACsB,KAAK,GAAG,GAAG,GAAG,MAAM;AACpD,kBAAkBtB,KAAK,IAAIA,KAAK,CAACsB,KAAK,GAAG,MAAM,GAAG,GAAG;AACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}