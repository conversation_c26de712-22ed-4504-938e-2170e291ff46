/**
 * Checks if a node represents a DOM element according to React.
 * @param {object} node - JSXOpeningElement to check.
 * @returns {boolean} Whether or not the node corresponds to a DOM element.
 */
export function isDOMComponent(node: object): boolean;
/**
 * Test whether a JSXElement is a fragment
 * @param {JSXElement} node
 * @param {string} reactPragma
 * @param {string} fragmentPragma
 * @returns {boolean}
 */
export function isFragment(node: JSXElement, reactPragma: string, fragmentPragma: string): boolean;
/**
 * Checks if a node represents a JSX element or fragment.
 * @param {object} node - node to check.
 * @returns {boolean} Whether or not the node if a JSX element or fragment.
 */
export function isJSX(node: object): boolean;
/**
 * Check if node is like `key={...}` as in `<Foo key={...} />`
 * @param {ASTNode} node
 * @returns {boolean}
 */
export function isJSXAttributeKey(node: ASTNode): boolean;
/**
 * Check if value has only whitespaces
 * @param {unknown} value
 * @returns {boolean}
 */
export function isWhiteSpaces(value: unknown): boolean;
/**
 * Check if the node is returning JSX or null
 *
 * @param {Context} context The context of `ASTNode`.
 * @param {ASTNode} ASTnode The AST node being checked
 * @param {boolean} [strict] If true, in a ternary condition the node must return JSX in both cases
 * @param {boolean} [ignoreNull] If true, null return values will be ignored
 * @returns {boolean} True if the node is returning JSX or null, false if not
 */
export function isReturningJSX(context: Context, ASTnode: ASTNode, strict?: boolean, ignoreNull?: boolean): boolean;
/**
 * Check if the node is returning only null values
 *
 * @param {ASTNode} ASTnode The AST node being checked
 * @param {Context} context The context of `ASTNode`.
 * @returns {boolean} True if the node is returning only null values
 */
export function isReturningOnlyNull(ASTnode: ASTNode, context: Context): boolean;
//# sourceMappingURL=jsx.d.ts.map