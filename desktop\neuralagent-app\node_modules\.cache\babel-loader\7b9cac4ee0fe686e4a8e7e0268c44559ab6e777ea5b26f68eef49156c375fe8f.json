{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\LoadingDialog\\\\index.js\";\nimport React from 'react';\nimport { LoadingDialogContainer, LoadingDialogOverlay } from './LoadingDialogElements';\nimport ClipLoader from \"react-spinners/ClipLoader\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction LoadingDialog() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(LoadingDialogOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingDialogContainer, {\n      children: /*#__PURE__*/_jsxDEV(ClipLoader, {\n        color: \"var(--secondary-color)\",\n        size: 100\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_c = LoadingDialog;\nexport default LoadingDialog;\nvar _c;\n$RefreshReg$(_c, \"LoadingDialog\");", "map": {"version": 3, "names": ["React", "LoadingDialogContainer", "LoadingDialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LoadingDialog", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "size", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/LoadingDialog/index.js"], "sourcesContent": ["import React from 'react';\nimport { LoadingDialogContainer, LoadingDialogOverlay } from './LoadingDialogElements';\nimport ClipLoader from \"react-spinners/ClipLoader\";\n\nfunction LoadingDialog() {\n  return (\n    <>\n      <LoadingDialogOverlay />\n      <LoadingDialogContainer>\n        <ClipLoader\n          color=\"var(--secondary-color)\"\n          size={100}\n        />\n      </LoadingDialogContainer>\n    </>\n  );\n}\n\nexport default LoadingDialog;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,sBAAsB,EAAEC,oBAAoB,QAAQ,yBAAyB;AACtF,OAAOC,UAAU,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,SAASC,aAAaA,CAAA,EAAG;EACvB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACH,oBAAoB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxBR,OAAA,CAACJ,sBAAsB;MAAAQ,QAAA,eACrBJ,OAAA,CAACF,UAAU;QACTW,KAAK,EAAC,wBAAwB;QAC9BC,IAAI,EAAE;MAAI;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACoB,CAAC;EAAA,eACzB,CAAC;AAEP;AAACG,EAAA,GAZQR,aAAa;AActB,eAAeA,aAAa;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}