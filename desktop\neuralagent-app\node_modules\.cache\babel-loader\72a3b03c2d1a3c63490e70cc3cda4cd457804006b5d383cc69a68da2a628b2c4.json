{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createAnimation = void 0;\nvar createAnimation = function (loaderName, frames, suffix) {\n  var animationName = \"react-spinners-\".concat(loaderName, \"-\").concat(suffix);\n  if (typeof window == \"undefined\" || !window.document) {\n    return animationName;\n  }\n  var styleEl = document.createElement(\"style\");\n  document.head.appendChild(styleEl);\n  var styleSheet = styleEl.sheet;\n  var keyFrames = \"\\n    @keyframes \".concat(animationName, \" {\\n      \").concat(frames, \"\\n    }\\n  \");\n  if (styleSheet) {\n    styleSheet.insertRule(keyFrames, 0);\n  }\n  return animationName;\n};\nexports.createAnimation = createAnimation;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createAnimation", "loaderName", "frames", "suffix", "animationName", "concat", "window", "document", "styleEl", "createElement", "head", "append<PERSON><PERSON><PERSON>", "styleSheet", "sheet", "keyFrames", "insertRule"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/react-spinners/helpers/animation.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createAnimation = void 0;\nvar createAnimation = function (loaderName, frames, suffix) {\n    var animationName = \"react-spinners-\".concat(loaderName, \"-\").concat(suffix);\n    if (typeof window == \"undefined\" || !window.document) {\n        return animationName;\n    }\n    var styleEl = document.createElement(\"style\");\n    document.head.appendChild(styleEl);\n    var styleSheet = styleEl.sheet;\n    var keyFrames = \"\\n    @keyframes \".concat(animationName, \" {\\n      \").concat(frames, \"\\n    }\\n  \");\n    if (styleSheet) {\n        styleSheet.insertRule(keyFrames, 0);\n    }\n    return animationName;\n};\nexports.createAnimation = createAnimation;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,IAAIA,eAAe,GAAG,SAAAA,CAAUC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACxD,IAAIC,aAAa,GAAG,iBAAiB,CAACC,MAAM,CAACJ,UAAU,EAAE,GAAG,CAAC,CAACI,MAAM,CAACF,MAAM,CAAC;EAC5E,IAAI,OAAOG,MAAM,IAAI,WAAW,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;IAClD,OAAOH,aAAa;EACxB;EACA,IAAII,OAAO,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EAC7CF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;EAClC,IAAII,UAAU,GAAGJ,OAAO,CAACK,KAAK;EAC9B,IAAIC,SAAS,GAAG,mBAAmB,CAACT,MAAM,CAACD,aAAa,EAAE,YAAY,CAAC,CAACC,MAAM,CAACH,MAAM,EAAE,aAAa,CAAC;EACrG,IAAIU,UAAU,EAAE;IACZA,UAAU,CAACG,UAAU,CAACD,SAAS,EAAE,CAAC,CAAC;EACvC;EACA,OAAOV,aAAa;AACxB,CAAC;AACDN,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}