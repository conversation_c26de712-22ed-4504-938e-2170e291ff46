import DERObject from './DERObject.js';
export declare function makeDERLength(length: number): number[];
export declare function makeDERIA5String(text: string): number[];
export declare function makeDERBMPString(text: string): number[];
export declare function makeDEROctetString(bin: number[] | Uint8Array): number[];
export declare function makeDERTaggedData(tag: number, body: number[]): number[];
export declare function makeDERSequence(body: number[]): number[];
export declare function arrayToDERSet(items: Array<DERObject | number[]>): number[];
