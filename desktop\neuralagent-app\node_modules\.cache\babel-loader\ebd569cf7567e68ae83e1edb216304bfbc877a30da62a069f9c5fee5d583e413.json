{"ast": null, "code": "import styled from 'styled-components';\nexport const LoadingDialogContainer = styled.div`\n  position: fixed;\n  z-index: 1500;\n  height: 200px;\n  width: 200px;\n  background: var(--card-color);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border-radius: 10px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n\n  :before {\n    background-color: red;\n  }\n`;\nexport const LoadingDialogOverlay = styled.div`\n  position: fixed;\n  z-index: 1000;\n  height: 100%;\n  width: 100%;\n  background-color: rgba(0, 0, 0, 0.6);\n  left: 0%;\n  top: 0%;\n`;", "map": {"version": 3, "names": ["styled", "LoadingDialogContainer", "div", "LoadingDialogOverlay"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/LoadingDialog/LoadingDialogElements.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const LoadingDialogContainer = styled.div`\n  position: fixed;\n  z-index: 1500;\n  height: 200px;\n  width: 200px;\n  background: var(--card-color);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border-radius: 10px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n\n  :before {\n    background-color: red;\n  }\n`\n\nexport const LoadingDialogOverlay = styled.div`\n  position: fixed;\n  z-index: 1000;\n  height: 100%;\n  width: 100%;\n  background-color: rgba(0, 0, 0, 0.6);\n  left: 0%;\n  top: 0%;\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,sBAAsB,GAAGD,MAAM,CAACE,GAAG;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAGH,MAAM,CAACE,GAAG;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}