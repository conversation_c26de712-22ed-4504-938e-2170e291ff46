export*from"redux";import{produce as Po,current as bo,freeze as Io,original as vo,isDraft as Do}from"immer";import{createSelector as No,createSelectorCreator as jo,lruMemoize as Fo,weakMapMemoize as Vo}from"reselect";import{current as at,isDraft as it}from"immer";import{createSelectorCreator as st,weakMapMemoize as ct}from"reselect";var xe=(...e)=>{let t=st(...e),r=Object.assign((...n)=>{let o=t(...n),a=(s,...u)=>o(it(s)?at(s):s,...u);return Object.assign(a,o),a},{withTypes:()=>r});return r},ie=xe(ct);import{applyMiddleware as xt,createStore as Ct,compose as Et,combineReducers as Rt,isPlainObject as wt}from"redux";import{compose as Ce}from"redux";var Ee=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Ce:Ce.apply(null,arguments)},En=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__?window.__REDUX_DEVTOOLS_EXTENSION__:function(){return function(e){return e}};import{thunk as mt,withExtraArgument as gt}from"redux-thunk";import{isAction as Re}from"redux";var q=e=>e&&typeof e.match=="function";function M(e,t){function r(...n){if(t){let o=t(...n);if(!o)throw new Error(x(0));return{type:e,payload:o.payload,..."meta"in o&&{meta:o.meta},..."error"in o&&{error:o.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>Re(n)&&n.type===e,r}function se(e){return typeof e=="function"&&"type"in e&&q(e)}function ce(e){return Re(e)&&Object.keys(e).every(dt)}function dt(e){return["type","payload","error","meta"].indexOf(e)>-1}function ut(e){let t=e?`${e}`.split("/"):[],r=t[t.length-1]||"actionCreator";return`Detected an action creator with type "${e||"unknown"}" being dispatched. 
Make sure you're calling the action creator before dispatching, i.e. \`dispatch(${r}())\` instead of \`dispatch(${r})\`. This is necessary even if the action has no payload.`}function lt(e={}){return()=>r=>n=>r(n)}import{produce as pt,isDraftable as ft}from"immer";var j=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function de(e){return ft(e)?pt(e,()=>{}):e}function v(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function yt(e){return typeof e!="object"||e==null||Object.isFrozen(e)}function ht(e={}){if(1)return()=>n=>o=>n(o);var t,r}import{isPlainObject as At}from"redux";function we(e){let t=typeof e;return e==null||t==="string"||t==="boolean"||t==="number"||Array.isArray(e)||At(e)}function Me(e,t="",r=we,n,o=[],a){let s;if(!r(e))return{keyPath:t||"<root>",value:e};if(typeof e!="object"||e===null||a?.has(e))return!1;let u=n!=null?n(e):Object.entries(e),c=o.length>0;for(let[h,i]of u){let d=t?t+"."+h:h;if(!(c&&o.some(g=>g instanceof RegExp?g.test(d):d===g))){if(!r(i))return{keyPath:d,value:i};if(typeof i=="object"&&(s=Me(i,d,r,n,o,a),s))return s}}return a&&Pe(e)&&a.add(e),!1}function Pe(e){if(!Object.isFrozen(e))return!1;for(let t of Object.values(e))if(!(typeof t!="object"||t===null)&&!Pe(t))return!1;return!0}function Tt(e={}){return()=>t=>r=>t(r)}function St(e){return typeof e=="boolean"}var be=()=>function(t){let{thunk:r=!0,immutableCheck:n=!0,serializableCheck:o=!0,actionCreatorCheck:a=!0}=t??{},s=new j;return r&&(St(r)?s.push(mt):s.push(gt(r.extraArgument))),s};var ue="RTK_autoBatch",kt=()=>e=>({payload:e,meta:{[ue]:!0}}),Ie=e=>t=>{setTimeout(t,e)},le=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),o=!0,a=!1,s=!1,u=new Set,c=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:Ie(10):e.type==="callback"?e.queueNotification:Ie(e.timeout),h=()=>{s=!1,a&&(a=!1,u.forEach(i=>i()))};return Object.assign({},n,{subscribe(i){let d=()=>o&&i(),m=n.subscribe(d);return u.add(i),()=>{m(),u.delete(i)}},dispatch(i){try{return o=!i?.meta?.[ue],a=!o,a&&(s||(s=!0,c(h))),n.dispatch(i)}finally{o=!0}}})};var ve=e=>function(r){let{autoBatch:n=!0}=r??{},o=new j(e);return n&&o.push(le(typeof n=="object"?n:void 0)),o};function Mt(e){let t=be(),{reducer:r=void 0,middleware:n,devTools:o=!0,preloadedState:a=void 0,enhancers:s=void 0}=e||{},u;if(typeof r=="function")u=r;else if(wt(r))u=Rt(r);else throw new Error(x(1));let c;typeof n=="function"?c=n(t):c=t();let h=Et;o&&(h=Ee({trace:!1,...typeof o=="object"&&o}));let i=xt(...c),d=ve(i),m=typeof s=="function"?s(d):d(),g=h(...m);return Ct(u,a,g)}import{produce as Pt,isDraft as bt,isDraftable as It}from"immer";function $(e){let t={},r=[],n,o={addCase(a,s){let u=typeof a=="string"?a:a.type;if(!u)throw new Error(x(28));if(u in t)throw new Error(x(29));return t[u]=s,o},addMatcher(a,s){return r.push({matcher:a,reducer:s}),o},addDefaultCase(a){return n=a,o}};return e(o),[t,r,n]}function vt(e){return typeof e=="function"}function pe(e,t){let[r,n,o]=$(t),a;if(vt(e))a=()=>de(e());else{let u=de(e);a=()=>u}function s(u=a(),c){let h=[r[c.type],...n.filter(({matcher:i})=>i(c)).map(({reducer:i})=>i)];return h.filter(i=>!!i).length===0&&(h=[o]),h.reduce((i,d)=>{if(d)if(bt(i)){let g=d(i,c);return g===void 0?i:g}else{if(It(i))return Pt(i,m=>d(m,c));{let m=d(i,c);if(m===void 0){if(i===null)return i;throw Error("A case reducer on a non-draftable value must not return undefined")}return m}}return i},u)}return s.getInitialState=a,s}var De=(e,t)=>q(e)?e.match(t):e(t);function F(...e){return t=>e.some(r=>De(r,t))}function U(...e){return t=>e.every(r=>De(r,t))}function J(e,t){if(!e||!e.meta)return!1;let r=typeof e.meta.requestId=="string",n=t.indexOf(e.meta.requestStatus)>-1;return r&&n}function W(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function Oe(...e){return e.length===0?t=>J(t,["pending"]):W(e)?F(...e.map(t=>t.pending)):Oe()(e[0])}function X(...e){return e.length===0?t=>J(t,["rejected"]):W(e)?F(...e.map(t=>t.rejected)):X()(e[0])}function Ne(...e){let t=r=>r&&r.meta&&r.meta.rejectedWithValue;return e.length===0?U(X(...e),t):W(e)?U(X(...e),t):Ne()(e[0])}function je(...e){return e.length===0?t=>J(t,["fulfilled"]):W(e)?F(...e.map(t=>t.fulfilled)):je()(e[0])}function Fe(...e){return e.length===0?t=>J(t,["pending","fulfilled","rejected"]):W(e)?F(...e.flatMap(t=>[t.pending,t.rejected,t.fulfilled])):Fe()(e[0])}var Dt="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",b=(e=21)=>{let t="",r=e;for(;r--;)t+=Dt[Math.random()*64|0];return t};var Ot=["name","message","stack","code"],z=class{constructor(t,r){this.payload=t;this.meta=r}_type},Q=class{constructor(t,r){this.payload=t;this.meta=r}_type},_e=e=>{if(typeof e=="object"&&e!==null){let t={};for(let r of Ot)typeof e[r]=="string"&&(t[r]=e[r]);return t}return{message:String(e)}},Ve="External signal was aborted",fe=(()=>{function e(t,r,n){let o=M(t+"/fulfilled",(c,h,i,d)=>({payload:c,meta:{...d||{},arg:i,requestId:h,requestStatus:"fulfilled"}})),a=M(t+"/pending",(c,h,i)=>({payload:void 0,meta:{...i||{},arg:h,requestId:c,requestStatus:"pending"}})),s=M(t+"/rejected",(c,h,i,d,m)=>({payload:d,error:(n&&n.serializeError||_e)(c||"Rejected"),meta:{...m||{},arg:i,requestId:h,rejectedWithValue:!!d,requestStatus:"rejected",aborted:c?.name==="AbortError",condition:c?.name==="ConditionError"}}));function u(c,{signal:h}={}){return(i,d,m)=>{let g=n?.idGenerator?n.idGenerator(c):b(),f=new AbortController,y,l;function p(A){l=A,f.abort()}h&&(h.aborted?p(Ve):h.addEventListener("abort",()=>p(Ve),{once:!0}));let T=async function(){let A;try{let C=n?.condition?.(c,{getState:d,extra:m});if(Nt(C)&&(C=await C),C===!1||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};let R=new Promise((k,w)=>{y=()=>{w({name:"AbortError",message:l||"Aborted"})},f.signal.addEventListener("abort",y)});i(a(g,c,n?.getPendingMeta?.({requestId:g,arg:c},{getState:d,extra:m}))),A=await Promise.race([R,Promise.resolve(r(c,{dispatch:i,getState:d,extra:m,requestId:g,signal:f.signal,abort:p,rejectWithValue:(k,w)=>new z(k,w),fulfillWithValue:(k,w)=>new Q(k,w)})).then(k=>{if(k instanceof z)throw k;return k instanceof Q?o(k.payload,g,c,k.meta):o(k,g,c)})])}catch(C){A=C instanceof z?s(null,g,c,C.payload,C.meta):s(C,g,c)}finally{y&&f.signal.removeEventListener("abort",y)}return n&&!n.dispatchConditionRejection&&s.match(A)&&A.meta.condition||i(A),A}();return Object.assign(T,{abort:p,requestId:g,arg:c,unwrap(){return T.then(Le)}})}}return Object.assign(u,{pending:a,rejected:s,fulfilled:o,settled:F(s,o),typePrefix:t})}return e.withTypes=()=>e,e})();function Le(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function Nt(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var Ue=Symbol.for("rtk-slice-createasyncthunk"),jt={[Ue]:fe},We=(n=>(n.reducer="reducer",n.reducerWithPrepare="reducerWithPrepare",n.asyncThunk="asyncThunk",n))(We||{});function Ft(e,t){return`${e}/${t}`}function ze({creators:e}={}){let t=e?.asyncThunk?.[Ue];return function(n){let{name:o,reducerPath:a=o}=n;if(!o)throw new Error(x(11));typeof process<"u";let s=(typeof n.reducers=="function"?n.reducers(Lt()):n.reducers)||{},u=Object.keys(s),c={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},h={addCase(T,A){let S=typeof T=="string"?T:T.type;if(!S)throw new Error(x(12));if(S in c.sliceCaseReducersByType)throw new Error(x(13));return c.sliceCaseReducersByType[S]=A,h},addMatcher(T,A){return c.sliceMatchers.push({matcher:T,reducer:A}),h},exposeAction(T,A){return c.actionCreators[T]=A,h},exposeCaseReducer(T,A){return c.sliceCaseReducersByName[T]=A,h}};u.forEach(T=>{let A=s[T],S={reducerName:T,type:Ft(o,T),createNotation:typeof n.reducers=="function"};Wt(A)?Gt(S,A,h,t):Ut(S,A,h)});function i(){let[T={},A=[],S=void 0]=typeof n.extraReducers=="function"?$(n.extraReducers):[n.extraReducers],C={...T,...c.sliceCaseReducersByType};return pe(n.initialState,R=>{for(let k in C)R.addCase(k,C[k]);for(let k of c.sliceMatchers)R.addMatcher(k.matcher,k.reducer);for(let k of A)R.addMatcher(k.matcher,k.reducer);S&&R.addDefaultCase(S)})}let d=T=>T,m=new Map,g;function f(T,A){return g||(g=i()),g(T,A)}function y(){return g||(g=i()),g.getInitialState()}function l(T,A=!1){function S(R){let k=R[T];return typeof k>"u"&&A&&(k=y()),k}function C(R=d){let k=v(m,A,()=>new WeakMap);return v(k,R,()=>{let w={};for(let[K,H]of Object.entries(n.selectors??{}))w[K]=Vt(H,R,y,A);return w})}return{reducerPath:T,getSelectors:C,get selectors(){return C(S)},selectSlice:S}}let p={name:o,reducer:f,actions:c.actionCreators,caseReducers:c.sliceCaseReducersByName,getInitialState:y,...l(a),injectInto(T,{reducerPath:A,...S}={}){let C=A??a;return T.inject({reducerPath:C,reducer:f},S),{...p,...l(C,!0)}}};return p}}function Vt(e,t,r,n){function o(a,...s){let u=t(a);return typeof u>"u"&&n&&(u=r()),e(u,...s)}return o.unwrapped=e,o}var _t=ze();function Lt(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function Ut({type:e,reducerName:t,createNotation:r},n,o){let a,s;if("reducer"in n){if(r&&!zt(n))throw new Error(x(17));a=n.reducer,s=n.prepare}else a=n;o.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,s?M(e,s):M(e))}function Wt(e){return e._reducerDefinitionType==="asyncThunk"}function zt(e){return e._reducerDefinitionType==="reducerWithPrepare"}function Gt({type:e,reducerName:t},r,n,o){if(!o)throw new Error(x(18));let{payloadCreator:a,fulfilled:s,pending:u,rejected:c,settled:h,options:i}=r,d=o(e,a,i);n.exposeAction(t,d),s&&n.addCase(d.fulfilled,s),u&&n.addCase(d.pending,u),c&&n.addCase(d.rejected,c),h&&n.addMatcher(d.settled,h),n.exposeCaseReducer(t,{fulfilled:s||Y,pending:u||Y,rejected:c||Y,settled:h||Y})}function Y(){}function Bt(){return{ids:[],entities:{}}}function Ge(e){function t(r={},n){let o=Object.assign(Bt(),r);return n?e.setAll(o,n):o}return{getInitialState:t}}function Be(){function e(t,r={}){let{createSelector:n=ie}=r,o=d=>d.ids,a=d=>d.entities,s=n(o,a,(d,m)=>d.map(g=>m[g])),u=(d,m)=>m,c=(d,m)=>d[m],h=n(o,d=>d.length);if(!t)return{selectIds:o,selectEntities:a,selectAll:s,selectTotal:h,selectById:n(a,u,c)};let i=n(t,a);return{selectIds:n(t,o),selectEntities:i,selectAll:n(t,s),selectTotal:n(t,h),selectById:n(i,u,c)}}return{getSelectors:e}}import{produce as Kt,isDraft as Ht}from"immer";var qt=Ht;function Ke(e){let t=E((r,n)=>e(n));return function(n){return t(n,void 0)}}function E(e){return function(r,n){function o(s){return ce(s)}let a=s=>{o(n)?e(n.payload,s):e(n,s)};return qt(r)?(a(r),r):Kt(r,a)}}import{current as $t,isDraft as Xt}from"immer";function D(e,t){return t(e)}function I(e){return Array.isArray(e)||(e=Object.values(e)),e}function G(e){return Xt(e)?$t(e):e}function Z(e,t,r){e=I(e);let n=G(r.ids),o=new Set(n),a=[],s=[];for(let u of e){let c=D(u,t);o.has(c)?s.push({id:c,changes:u}):a.push(u)}return[a,s,n]}function ee(e){function t(f,y){let l=D(f,e);l in y.entities||(y.ids.push(l),y.entities[l]=f)}function r(f,y){f=I(f);for(let l of f)t(l,y)}function n(f,y){let l=D(f,e);l in y.entities||y.ids.push(l),y.entities[l]=f}function o(f,y){f=I(f);for(let l of f)n(l,y)}function a(f,y){f=I(f),y.ids=[],y.entities={},r(f,y)}function s(f,y){return u([f],y)}function u(f,y){let l=!1;f.forEach(p=>{p in y.entities&&(delete y.entities[p],l=!0)}),l&&(y.ids=y.ids.filter(p=>p in y.entities))}function c(f){Object.assign(f,{ids:[],entities:{}})}function h(f,y,l){let p=l.entities[y.id];if(p===void 0)return!1;let T=Object.assign({},p,y.changes),A=D(T,e),S=A!==y.id;return S&&(f[y.id]=A,delete l.entities[y.id]),l.entities[A]=T,S}function i(f,y){return d([f],y)}function d(f,y){let l={},p={};f.forEach(A=>{A.id in y.entities&&(p[A.id]={id:A.id,changes:{...p[A.id]?.changes,...A.changes}})}),f=Object.values(p),f.length>0&&f.filter(S=>h(l,S,y)).length>0&&(y.ids=Object.values(y.entities).map(S=>D(S,e)))}function m(f,y){return g([f],y)}function g(f,y){let[l,p]=Z(f,e,y);d(p,y),r(l,y)}return{removeAll:Ke(c),addOne:E(t),addMany:E(r),setOne:E(n),setMany:E(o),setAll:E(a),updateOne:E(i),updateMany:E(d),upsertOne:E(m),upsertMany:E(g),removeOne:E(s),removeMany:E(u)}}function Jt(e,t,r){let n=0,o=e.length;for(;n<o;){let a=n+o>>>1,s=e[a];r(t,s)>=0?n=a+1:o=a}return n}function Qt(e,t,r){let n=Jt(e,t,r);return e.splice(n,0,t),e}function He(e,t){let{removeOne:r,removeMany:n,removeAll:o}=ee(e);function a(l,p){return s([l],p)}function s(l,p,T){l=I(l);let A=new Set(T??G(p.ids)),S=l.filter(C=>!A.has(D(C,e)));S.length!==0&&y(p,S)}function u(l,p){return c([l],p)}function c(l,p){if(l=I(l),l.length!==0){for(let T of l)delete p.entities[e(T)];y(p,l)}}function h(l,p){l=I(l),p.entities={},p.ids=[],s(l,p,[])}function i(l,p){return d([l],p)}function d(l,p){let T=!1,A=!1;for(let S of l){let C=p.entities[S.id];if(!C)continue;T=!0,Object.assign(C,S.changes);let R=e(C);if(S.id!==R){A=!0,delete p.entities[S.id];let k=p.ids.indexOf(S.id);p.ids[k]=R,p.entities[R]=C}}T&&y(p,[],T,A)}function m(l,p){return g([l],p)}function g(l,p){let[T,A,S]=Z(l,e,p);A.length&&d(A,p),T.length&&s(T,p,S)}function f(l,p){if(l.length!==p.length)return!1;for(let T=0;T<l.length;T++)if(l[T]!==p[T])return!1;return!0}let y=(l,p,T,A)=>{let S=G(l.entities),C=G(l.ids),R=l.entities,k=C;A&&(k=new Set(C));let w=[];for(let L of k){let ke=S[L];ke&&w.push(ke)}let K=w.length===0;for(let L of p)R[e(L)]=L,K||Qt(w,L,t);K?w=p.slice().sort(t):T&&w.sort(t);let H=w.map(e);f(C,H)||(l.ids=H)};return{removeOne:r,removeMany:n,removeAll:o,addOne:E(a),updateOne:E(i),upsertOne:E(m),setOne:E(u),setMany:E(c),setAll:E(h),addMany:E(s),updateMany:E(d),upsertMany:E(g)}}function Yt(e={}){let{selectId:t,sortComparer:r}={sortComparer:!1,selectId:s=>s.id,...e},n=r?He(t,r):ee(t),o=Ge(n),a=Be();return{selectId:t,sortComparer:r,...o,...a,...n}}import{isAction as en}from"redux";var Zt="task",qe="listener",$e="completed",ye="cancelled",Xe=`task-${ye}`,Je=`task-${$e}`,te=`${qe}-${ye}`,Qe=`${qe}-${$e}`,P=class{constructor(t){this.code=t;this.message=`${Zt} ${ye} (reason: ${t})`}name="TaskAbortError";message};var ne=(e,t)=>{if(typeof e!="function")throw new TypeError(x(32))},V=()=>{},re=(e,t=V)=>(e.catch(t),e),oe=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),O=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))};var N=e=>{if(e.aborted){let{reason:t}=e;throw new P(t)}};function he(e,t){let r=V;return new Promise((n,o)=>{let a=()=>o(new P(e.reason));if(e.aborted){a();return}r=oe(e,a),t.finally(()=>r()).then(n,o)}).finally(()=>{r=V})}var Ye=async(e,t)=>{try{return await Promise.resolve(),{status:"ok",value:await e()}}catch(r){return{status:r instanceof P?"cancelled":"rejected",error:r}}finally{t?.()}},B=e=>t=>re(he(e,t).then(r=>(N(e),r))),Ae=e=>{let t=B(e);return r=>t(new Promise(n=>setTimeout(n,r)))};var{assign:_}=Object,Ze={},ae="listenerMiddleware",tn=(e,t)=>{let r=n=>oe(e,()=>O(n,e.reason));return(n,o)=>{ne(n,"taskExecutor");let a=new AbortController;r(a);let s=Ye(async()=>{N(e),N(a.signal);let u=await n({pause:B(a.signal),delay:Ae(a.signal),signal:a.signal});return N(a.signal),u},()=>O(a,Je));return o?.autoJoin&&t.push(s.catch(V)),{result:B(e)(s),cancel(){O(a,Xe)}}}},nn=(e,t)=>{let r=async(n,o)=>{N(t);let a=()=>{},u=[new Promise((c,h)=>{let i=e({predicate:n,effect:(d,m)=>{m.unsubscribe(),c([d,m.getState(),m.getOriginalState()])}});a=()=>{i(),h()}})];o!=null&&u.push(new Promise(c=>setTimeout(c,o,null)));try{let c=await he(t,Promise.race(u));return N(t),c}finally{a()}};return(n,o)=>re(r(n,o))},nt=e=>{let{type:t,actionCreator:r,matcher:n,predicate:o,effect:a}=e;if(t)o=M(t).match;else if(r)t=r.type,o=r.match;else if(n)o=n;else if(!o)throw new Error(x(21));return ne(a,"options.listener"),{predicate:o,type:t,effect:a}},rt=_(e=>{let{type:t,predicate:r,effect:n}=nt(e);return{id:b(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw new Error(x(22))}}},{withTypes:()=>rt}),et=(e,t)=>{let{type:r,effect:n,predicate:o}=nt(t);return Array.from(e.values()).find(a=>(typeof r=="string"?a.type===r:a.predicate===o)&&a.effect===n)},Te=e=>{e.pending.forEach(t=>{O(t,te)})},rn=e=>()=>{e.forEach(Te),e.clear()},tt=(e,t,r)=>{try{e(t,r)}catch(n){setTimeout(()=>{throw n},0)}},me=_(M(`${ae}/add`),{withTypes:()=>me}),ot=M(`${ae}/removeAll`),ge=_(M(`${ae}/remove`),{withTypes:()=>ge}),on=(...e)=>{console.error(`${ae}/error`,...e)},an=(e={})=>{let t=new Map,{extra:r,onError:n=on}=e;ne(n,"onError");let o=i=>(i.unsubscribe=()=>t.delete(i.id),t.set(i.id,i),d=>{i.unsubscribe(),d?.cancelActive&&Te(i)}),a=i=>{let d=et(t,i)??rt(i);return o(d)};_(a,{withTypes:()=>a});let s=i=>{let d=et(t,i);return d&&(d.unsubscribe(),i.cancelActive&&Te(d)),!!d};_(s,{withTypes:()=>s});let u=async(i,d,m,g)=>{let f=new AbortController,y=nn(a,f.signal),l=[];try{i.pending.add(f),await Promise.resolve(i.effect(d,_({},m,{getOriginalState:g,condition:(p,T)=>y(p,T).then(Boolean),take:y,delay:Ae(f.signal),pause:B(f.signal),extra:r,signal:f.signal,fork:tn(f.signal,l),unsubscribe:i.unsubscribe,subscribe:()=>{t.set(i.id,i)},cancelActiveListeners:()=>{i.pending.forEach((p,T,A)=>{p!==f&&(O(p,te),A.delete(p))})},cancel:()=>{O(f,te),i.pending.delete(f)},throwIfCancelled:()=>{N(f.signal)}})))}catch(p){p instanceof P||tt(n,p,{raisedBy:"effect"})}finally{await Promise.all(l),O(f,Qe),i.pending.delete(f)}},c=rn(t);return{middleware:i=>d=>m=>{if(!en(m))return d(m);if(me.match(m))return a(m.payload);if(ot.match(m)){c();return}if(ge.match(m))return s(m.payload);let g=i.getState(),f=()=>{if(g===Ze)throw new Error(x(23));return g},y;try{if(y=d(m),t.size>0){let l=i.getState(),p=Array.from(t.values());for(let T of p){let A=!1;try{A=T.predicate(m,l,g)}catch(S){A=!1,tt(n,S,{raisedBy:"predicate"})}A&&u(T,m,i,f)}}}finally{g=Ze}return y},startListening:a,stopListening:s,clearListeners:c}};import{compose as sn}from"redux";var cn=e=>({middleware:e,applied:new Map}),dn=e=>t=>t?.meta?.instanceId===e,un=()=>{let e=b(),t=new Map,r=Object.assign(M("dynamicMiddleware/add",(...u)=>({payload:u,meta:{instanceId:e}})),{withTypes:()=>r}),n=Object.assign(function(...c){c.forEach(h=>{v(t,h,cn)})},{withTypes:()=>n}),o=u=>{let c=Array.from(t.values()).map(h=>v(h.applied,u,h.middleware));return sn(...c)},a=U(r,dn(e));return{middleware:u=>c=>h=>a(h)?(n(...h.payload),u.dispatch):o(u)(c)(h),addMiddleware:n,withMiddleware:r,instanceId:e}};import{combineReducers as ln}from"redux";var pn=e=>"reducerPath"in e&&typeof e.reducerPath=="string",fn=e=>e.flatMap(t=>pn(t)?[[t.reducerPath,t.reducer]]:Object.entries(t)),Se=Symbol.for("rtk-state-proxy-original"),yn=e=>!!e&&!!e[Se],hn=new WeakMap,An=(e,t)=>v(hn,e,()=>new Proxy(e,{get:(r,n,o)=>{if(n===Se)return r;let a=Reflect.get(r,n,o);if(typeof a>"u"){let s=t[n.toString()];if(s){let u=s(void 0,{type:b()});if(typeof u>"u")throw new Error(x(24));return u}}return a}})),Tn=e=>{if(!yn(e))throw new Error(x(25));return e[Se]},mn=(e={})=>e;function gn(...e){let t=Object.fromEntries(fn(e)),r=()=>Object.keys(t).length?ln(t):mn,n=r();function o(u,c){return n(u,c)}o.withLazyLoadedSlices=()=>o;let a=(u,c={})=>{let{reducerPath:h,reducer:i}=u,d=t[h];return!c.overrideExisting&&d&&d!==i?(typeof process<"u",o):(t[h]=i,n=r(),o)},s=Object.assign(function(c,h){return function(d,...m){return c(An(h?h(d,...m):d,t),...m)}},{original:Tn});return Object.assign(o,{inject:a,selector:s})}function x(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{We as ReducerType,ue as SHOULD_AUTOBATCH,P as TaskAbortError,j as Tuple,me as addListener,jt as asyncThunkCreator,le as autoBatchEnhancer,ze as buildCreateSlice,ot as clearAllListeners,gn as combineSlices,Mt as configureStore,M as createAction,lt as createActionCreatorInvariantMiddleware,fe as createAsyncThunk,ie as createDraftSafeSelector,xe as createDraftSafeSelectorCreator,un as createDynamicMiddleware,Yt as createEntityAdapter,ht as createImmutableStateInvariantMiddleware,an as createListenerMiddleware,Po as createNextState,pe as createReducer,No as createSelector,jo as createSelectorCreator,Tt as createSerializableStateInvariantMiddleware,_t as createSlice,bo as current,Me as findNonSerializableValue,x as formatProdErrorMessage,Io as freeze,se as isActionCreator,U as isAllOf,F as isAnyOf,Fe as isAsyncThunkAction,Do as isDraft,ce as isFluxStandardAction,je as isFulfilled,yt as isImmutableDefault,Oe as isPending,we as isPlain,X as isRejected,Ne as isRejectedWithValue,Fo as lruMemoize,_e as miniSerializeError,b as nanoid,vo as original,kt as prepareAutoBatched,ge as removeListener,Le as unwrapResult,Vo as weakMapMemoize};
//# sourceMappingURL=redux-toolkit.browser.mjs.map