{"ast": null, "code": "import constants from './constants';\nimport moment from 'moment';\n// import 'moment/locale/ar';\nimport axios, { API_KEY_HEADER } from './axios';\nimport { setLoadingDialog } from '../store';\nexport function openInNewTab(url) {\n  var win = window.open(url, '_blank');\n  win.focus();\n}\nexport const logoutUserLocally = removeCookie => {\n  window.electronAPI.deleteToken();\n  window.electronAPI.deleteRefreshToken();\n  window.location.reload();\n};\nexport const logoutUser = (accessToken, dispatch) => {\n  console.log(accessToken);\n  dispatch(setLoadingDialog(true));\n  axios.post('/auth/logout', {\n    access_token: accessToken\n  }, API_KEY_HEADER).then(response => {\n    dispatch(setLoadingDialog(false));\n    logoutUserLocally();\n  }).catch(error => {\n    dispatch(setLoadingDialog(false));\n    if (error.response.status === constants.status.UNAUTHORIZED) {\n      logoutUserLocally();\n    }\n  });\n};\nexport const refreshToken = async () => {\n  axios.post('/auth/refresh_token', {\n    refresh_token: await window.electronAPI.getRefreshToken()\n  }, API_KEY_HEADER).then(response => {\n    const data = response.data;\n    window.electronAPI.setToken(data.new_token);\n    if (data.new_refresh !== null) {\n      window.electronAPI.setRefreshToken(data.new_refresh);\n    }\n    window.location.reload();\n  }).catch(error => {\n    if (error.response.status === constants.status.UNAUTHORIZED) {\n      logoutUserLocally();\n    }\n  });\n};\nexport const getBadRequestErrorMessage = data => {\n  if (data === null || data.message === undefined) {\n    return constants.GENERAL_ERROR;\n  }\n  if (data.message === 'Limited_2_Projects') {\n    return 'You are currently limited to two projects';\n  }\n  return constants.GENERAL_ERROR;\n};\nexport const formatDateTime = (date_str, lang = 'en', format = 'LLLL') => {\n  moment.locale(lang);\n  let date = moment(date_str);\n  return date.format(format);\n};\nexport const localDateTimeToUtc = time => {\n  moment.locale('en');\n  let date = new moment(time, 'YYYY-MM-DDTHH:mm').utc();\n  return date.format('YYYY-MM-DDTHH:mm');\n};\nexport const utcDateTimeToLocal = time => {\n  moment.locale('en');\n  let date = moment.utc(time, 'YYYY-MM-DDTHH:mm').local();\n  return date.format('YYYY-MM-DDTHH:mm');\n};\nexport const formatTime = (time, format, lang = 'en') => {\n  moment.locale(lang);\n  let date = moment(time, 'HH:mm:ss');\n  return date.format(format);\n};\nexport const formatDate = (date_str, format) => {\n  moment.locale('en');\n  let date = moment(date_str);\n  return date.format(format);\n};\nexport const localTimeToUtc = time => {\n  moment.locale('en');\n  let date = new moment(time, 'HH:mm:ss').utc();\n  return date.format('HH:mm:ss');\n};\nexport const utcToLocalTime = time => {\n  moment.locale('en');\n  let date = moment.utc(time, 'HH:mm:ss').local();\n  return date.format('HH:mm:ss');\n};\nexport const isLessThanNow = date => {\n  let current_time = moment();\n  let shown_at = moment(utcDateTimeToLocal(date));\n  let dif = current_time.diff(shown_at);\n  return dif > 0;\n};", "map": {"version": 3, "names": ["constants", "moment", "axios", "API_KEY_HEADER", "setLoadingDialog", "openInNewTab", "url", "win", "window", "open", "focus", "logoutUserLocally", "<PERSON><PERSON><PERSON><PERSON>", "electronAPI", "deleteToken", "deleteRefreshToken", "location", "reload", "logoutUser", "accessToken", "dispatch", "console", "log", "post", "access_token", "then", "response", "catch", "error", "status", "UNAUTHORIZED", "refreshToken", "refresh_token", "getRefreshToken", "data", "setToken", "new_token", "new_refresh", "setRefreshToken", "getBadRequestErrorMessage", "message", "undefined", "GENERAL_ERROR", "formatDateTime", "date_str", "lang", "format", "locale", "date", "localDateTimeToUtc", "time", "utc", "utcDateTimeToLocal", "local", "formatTime", "formatDate", "localTimeToUtc", "utcToLocalTime", "isLessThanNow", "current_time", "shown_at", "dif", "diff"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/utils/helpers.js"], "sourcesContent": ["import constants from './constants';\nimport moment from 'moment';\n// import 'moment/locale/ar';\nimport axios, { API_KEY_HEADER } from './axios';\nimport { setLoadingDialog } from '../store';\n\n\nexport function openInNewTab(url) {\n  var win = window.open(url, '_blank')\n  win.focus()\n}\n\nexport const logoutUserLocally = (removeCookie) => {\n  window.electronAPI.deleteToken();\n  window.electronAPI.deleteRefreshToken();\n  window.location.reload();\n};\n\nexport const logoutUser = (accessToken, dispatch) => {\n  console.log(accessToken);\n  dispatch(setLoadingDialog(true));\n  axios.post('/auth/logout', {access_token: accessToken}, API_KEY_HEADER)\n    .then((response) => {\n      dispatch(setLoadingDialog(false));\n      logoutUserLocally();\n    })\n    .catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        logoutUserLocally();\n      }\n    });\n}\n\nexport const refreshToken = async () => {\n  axios.post('/auth/refresh_token', {\n    refresh_token: await window.electronAPI.getRefreshToken(),\n  }, API_KEY_HEADER).then((response) => {\n    const data = response.data;\n    window.electronAPI.setToken(data.new_token);\n    if (data.new_refresh !== null) {\n      window.electronAPI.setRefreshToken(data.new_refresh);\n    }\n    window.location.reload();\n  }).catch((error) => {\n    if (error.response.status === constants.status.UNAUTHORIZED) {\n      logoutUserLocally();\n    }\n  });\n};\n\nexport const getBadRequestErrorMessage = (data) => {\n\n  if (data === null || data.message === undefined) {\n    return constants.GENERAL_ERROR;\n  }\n\n  if (data.message === 'Limited_2_Projects') {\n    return 'You are currently limited to two projects';\n  }\n\n  return constants.GENERAL_ERROR;\n};\n\nexport const formatDateTime = (date_str, lang = 'en', format = 'LLLL') => {\n  moment.locale(lang);\n  let date = moment(date_str);\n  return date.format(format);\n};\n\nexport const localDateTimeToUtc = (time) => {\n  moment.locale('en');\n  let date = new moment(time, 'YYYY-MM-DDTHH:mm').utc();\n  return date.format('YYYY-MM-DDTHH:mm');\n};\n\nexport const utcDateTimeToLocal = (time) => {\n  moment.locale('en');\n  let date = moment.utc(time, 'YYYY-MM-DDTHH:mm').local();\n  return date.format('YYYY-MM-DDTHH:mm');\n};\n\nexport const formatTime = (time, format, lang = 'en') => {\n  moment.locale(lang);\n  let date = moment(time, 'HH:mm:ss');\n  return date.format(format);\n};\n\nexport const formatDate = (date_str, format) => {\n  moment.locale('en');\n  let date = moment(date_str);\n  return date.format(format);\n};\n\nexport const localTimeToUtc = (time) => {\n  moment.locale('en');\n  let date = new moment(time, 'HH:mm:ss').utc();\n  return date.format('HH:mm:ss');\n};\n\nexport const utcToLocalTime = (time) => {\n  moment.locale('en');\n  let date = moment.utc(time, 'HH:mm:ss').local();\n  return date.format('HH:mm:ss');\n};\n\nexport const isLessThanNow = (date) => {\n  let current_time = moment();\n  let shown_at = moment(utcDateTimeToLocal(date));\n  let dif = current_time.diff(shown_at);\n  return dif > 0;\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,MAAM,MAAM,QAAQ;AAC3B;AACA,OAAOC,KAAK,IAAIC,cAAc,QAAQ,SAAS;AAC/C,SAASC,gBAAgB,QAAQ,UAAU;AAG3C,OAAO,SAASC,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAIC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,EAAE,QAAQ,CAAC;EACpCC,GAAG,CAACG,KAAK,CAAC,CAAC;AACb;AAEA,OAAO,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;EACjDJ,MAAM,CAACK,WAAW,CAACC,WAAW,CAAC,CAAC;EAChCN,MAAM,CAACK,WAAW,CAACE,kBAAkB,CAAC,CAAC;EACvCP,MAAM,CAACQ,QAAQ,CAACC,MAAM,CAAC,CAAC;AAC1B,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EACnDC,OAAO,CAACC,GAAG,CAACH,WAAW,CAAC;EACxBC,QAAQ,CAAChB,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAChCF,KAAK,CAACqB,IAAI,CAAC,cAAc,EAAE;IAACC,YAAY,EAAEL;EAAW,CAAC,EAAEhB,cAAc,CAAC,CACpEsB,IAAI,CAAEC,QAAQ,IAAK;IAClBN,QAAQ,CAAChB,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACjCO,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC,CACDgB,KAAK,CAAEC,KAAK,IAAK;IAChBR,QAAQ,CAAChB,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACjC,IAAIwB,KAAK,CAACF,QAAQ,CAACG,MAAM,KAAK7B,SAAS,CAAC6B,MAAM,CAACC,YAAY,EAAE;MAC3DnB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMoB,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC7B,KAAK,CAACqB,IAAI,CAAC,qBAAqB,EAAE;IAChCS,aAAa,EAAE,MAAMxB,MAAM,CAACK,WAAW,CAACoB,eAAe,CAAC;EAC1D,CAAC,EAAE9B,cAAc,CAAC,CAACsB,IAAI,CAAEC,QAAQ,IAAK;IACpC,MAAMQ,IAAI,GAAGR,QAAQ,CAACQ,IAAI;IAC1B1B,MAAM,CAACK,WAAW,CAACsB,QAAQ,CAACD,IAAI,CAACE,SAAS,CAAC;IAC3C,IAAIF,IAAI,CAACG,WAAW,KAAK,IAAI,EAAE;MAC7B7B,MAAM,CAACK,WAAW,CAACyB,eAAe,CAACJ,IAAI,CAACG,WAAW,CAAC;IACtD;IACA7B,MAAM,CAACQ,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC,CAAC,CAACU,KAAK,CAAEC,KAAK,IAAK;IAClB,IAAIA,KAAK,CAACF,QAAQ,CAACG,MAAM,KAAK7B,SAAS,CAAC6B,MAAM,CAACC,YAAY,EAAE;MAC3DnB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM4B,yBAAyB,GAAIL,IAAI,IAAK;EAEjD,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACM,OAAO,KAAKC,SAAS,EAAE;IAC/C,OAAOzC,SAAS,CAAC0C,aAAa;EAChC;EAEA,IAAIR,IAAI,CAACM,OAAO,KAAK,oBAAoB,EAAE;IACzC,OAAO,2CAA2C;EACpD;EAEA,OAAOxC,SAAS,CAAC0C,aAAa;AAChC,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,IAAI,GAAG,IAAI,EAAEC,MAAM,GAAG,MAAM,KAAK;EACxE7C,MAAM,CAAC8C,MAAM,CAACF,IAAI,CAAC;EACnB,IAAIG,IAAI,GAAG/C,MAAM,CAAC2C,QAAQ,CAAC;EAC3B,OAAOI,IAAI,CAACF,MAAM,CAACA,MAAM,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,kBAAkB,GAAIC,IAAI,IAAK;EAC1CjD,MAAM,CAAC8C,MAAM,CAAC,IAAI,CAAC;EACnB,IAAIC,IAAI,GAAG,IAAI/C,MAAM,CAACiD,IAAI,EAAE,kBAAkB,CAAC,CAACC,GAAG,CAAC,CAAC;EACrD,OAAOH,IAAI,CAACF,MAAM,CAAC,kBAAkB,CAAC;AACxC,CAAC;AAED,OAAO,MAAMM,kBAAkB,GAAIF,IAAI,IAAK;EAC1CjD,MAAM,CAAC8C,MAAM,CAAC,IAAI,CAAC;EACnB,IAAIC,IAAI,GAAG/C,MAAM,CAACkD,GAAG,CAACD,IAAI,EAAE,kBAAkB,CAAC,CAACG,KAAK,CAAC,CAAC;EACvD,OAAOL,IAAI,CAACF,MAAM,CAAC,kBAAkB,CAAC;AACxC,CAAC;AAED,OAAO,MAAMQ,UAAU,GAAGA,CAACJ,IAAI,EAAEJ,MAAM,EAAED,IAAI,GAAG,IAAI,KAAK;EACvD5C,MAAM,CAAC8C,MAAM,CAACF,IAAI,CAAC;EACnB,IAAIG,IAAI,GAAG/C,MAAM,CAACiD,IAAI,EAAE,UAAU,CAAC;EACnC,OAAOF,IAAI,CAACF,MAAM,CAACA,MAAM,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMS,UAAU,GAAGA,CAACX,QAAQ,EAAEE,MAAM,KAAK;EAC9C7C,MAAM,CAAC8C,MAAM,CAAC,IAAI,CAAC;EACnB,IAAIC,IAAI,GAAG/C,MAAM,CAAC2C,QAAQ,CAAC;EAC3B,OAAOI,IAAI,CAACF,MAAM,CAACA,MAAM,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMU,cAAc,GAAIN,IAAI,IAAK;EACtCjD,MAAM,CAAC8C,MAAM,CAAC,IAAI,CAAC;EACnB,IAAIC,IAAI,GAAG,IAAI/C,MAAM,CAACiD,IAAI,EAAE,UAAU,CAAC,CAACC,GAAG,CAAC,CAAC;EAC7C,OAAOH,IAAI,CAACF,MAAM,CAAC,UAAU,CAAC;AAChC,CAAC;AAED,OAAO,MAAMW,cAAc,GAAIP,IAAI,IAAK;EACtCjD,MAAM,CAAC8C,MAAM,CAAC,IAAI,CAAC;EACnB,IAAIC,IAAI,GAAG/C,MAAM,CAACkD,GAAG,CAACD,IAAI,EAAE,UAAU,CAAC,CAACG,KAAK,CAAC,CAAC;EAC/C,OAAOL,IAAI,CAACF,MAAM,CAAC,UAAU,CAAC;AAChC,CAAC;AAED,OAAO,MAAMY,aAAa,GAAIV,IAAI,IAAK;EACrC,IAAIW,YAAY,GAAG1D,MAAM,CAAC,CAAC;EAC3B,IAAI2D,QAAQ,GAAG3D,MAAM,CAACmD,kBAAkB,CAACJ,IAAI,CAAC,CAAC;EAC/C,IAAIa,GAAG,GAAGF,YAAY,CAACG,IAAI,CAACF,QAAQ,CAAC;EACrC,OAAOC,GAAG,GAAG,CAAC;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}