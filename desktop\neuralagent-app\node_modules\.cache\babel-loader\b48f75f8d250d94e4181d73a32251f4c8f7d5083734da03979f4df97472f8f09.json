{"ast": null, "code": "import styled from 'styled-components';\nimport breakpoint from '../../utils/breakpoint';\nexport const AppMainContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  color: white;\n`;\nexport const OverlayContainer = styled.div`\n  color: white;\n  background: transparent;\n  height: 100vh;\n  width: 100vw;\n  overflow: hidden;\n`;\nexport const Row = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  -webkit-box-flex: 1;\n  flex: 1 1 auto;\n  width: 100%;\n  justify-content: ${props => props.justifyContent ? props.justifyContent : 'flex-start'};\n  align-items: ${props => props.alignItems ? props.alignItems : 'stretch'};\n`;\nexport const Column = styled.div`\n  color: #000;\n  -webkit-box-flex: 0;\n  padding: 10px;\n  flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.cols ? props.cols : 12)};\n  max-width: ${props => breakpoint.checkers.getFlexWidth(props.cols ? props.cols : 12)};\n\n  @media screen and (${breakpoint.devices_max.lg}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.lg ? props.lg : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.lg ? props.lg : props.cols)};\n  }\n\n  @media screen and (${breakpoint.devices_max.md}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.md ? props.md : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.md ? props.md : props.cols)};\n  }\n\n  @media screen and (${breakpoint.devices_max.sm}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.sm ? props.sm : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.sm ? props.sm : props.cols)};\n  }\n\n  @media screen and (${breakpoint.devices_max.xs}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.xs ? props.xs : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.xs ? props.xs : props.cols)};\n  }\n`;", "map": {"version": 3, "names": ["styled", "breakpoint", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "OverlayContainer", "Row", "props", "justifyContent", "alignItems", "Column", "checkers", "getFlexWidth", "cols", "devices_max", "lg", "md", "sm", "xs"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/layouts/Containers/index.js"], "sourcesContent": ["import styled from 'styled-components';\nimport breakpoint from '../../utils/breakpoint';\n\nexport const AppMainContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  color: white;\n`;\n\nexport const OverlayContainer = styled.div`\n  color: white;\n  background: transparent;\n  height: 100vh;\n  width: 100vw;\n  overflow: hidden;\n`\n\nexport const Row = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  -webkit-box-flex: 1;\n  flex: 1 1 auto;\n  width: 100%;\n  justify-content: ${props => props.justifyContent ? props.justifyContent : 'flex-start'};\n  align-items: ${props => props.alignItems ? props.alignItems : 'stretch'};\n`\n\nexport const Column = styled.div`\n  color: #000;\n  -webkit-box-flex: 0;\n  padding: 10px;\n  flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.cols ? props.cols : 12)};\n  max-width: ${props => breakpoint.checkers.getFlexWidth(props.cols ? props.cols : 12)};\n\n  @media screen and (${breakpoint.devices_max.lg}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.lg ? props.lg : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.lg ? props.lg : props.cols)};\n  }\n\n  @media screen and (${breakpoint.devices_max.md}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.md ? props.md : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.md ? props.md : props.cols)};\n  }\n\n  @media screen and (${breakpoint.devices_max.sm}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.sm ? props.sm : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.sm ? props.sm : props.cols)};\n  }\n\n  @media screen and (${breakpoint.devices_max.xs}) {\n    flex: 0 0 ${props => breakpoint.checkers.getFlexWidth(props.xs ? props.xs : props.cols)};\n    max-width: ${props => breakpoint.checkers.getFlexWidth(props.xs ? props.xs : props.cols)};\n  }\n`"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,OAAOC,UAAU,MAAM,wBAAwB;AAE/C,OAAO,MAAMC,gBAAgB,GAAGF,MAAM,CAACG,GAAG;AAC1C;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGJ,MAAM,CAACG,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAME,GAAG,GAAGL,MAAM,CAACG,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,qBAAqBG,KAAK,IAAIA,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,GAAG,YAAY;AACxF,iBAAiBD,KAAK,IAAIA,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU,GAAG,SAAS;AACzE,CAAC;AAED,OAAO,MAAMC,MAAM,GAAGT,MAAM,CAACG,GAAG;AAChC;AACA;AACA;AACA,cAAcG,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACM,IAAI,GAAG,EAAE,CAAC;AACrF,eAAeN,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACM,IAAI,GAAG,EAAE,CAAC;AACtF;AACA,uBAAuBX,UAAU,CAACY,WAAW,CAACC,EAAE;AAChD,gBAAgBR,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACQ,EAAE,GAAGR,KAAK,CAACQ,EAAE,GAAGR,KAAK,CAACM,IAAI,CAAC;AAC3F,iBAAiBN,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACQ,EAAE,GAAGR,KAAK,CAACQ,EAAE,GAAGR,KAAK,CAACM,IAAI,CAAC;AAC5F;AACA;AACA,uBAAuBX,UAAU,CAACY,WAAW,CAACE,EAAE;AAChD,gBAAgBT,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACS,EAAE,GAAGT,KAAK,CAACS,EAAE,GAAGT,KAAK,CAACM,IAAI,CAAC;AAC3F,iBAAiBN,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACS,EAAE,GAAGT,KAAK,CAACS,EAAE,GAAGT,KAAK,CAACM,IAAI,CAAC;AAC5F;AACA;AACA,uBAAuBX,UAAU,CAACY,WAAW,CAACG,EAAE;AAChD,gBAAgBV,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACU,EAAE,GAAGV,KAAK,CAACU,EAAE,GAAGV,KAAK,CAACM,IAAI,CAAC;AAC3F,iBAAiBN,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACU,EAAE,GAAGV,KAAK,CAACU,EAAE,GAAGV,KAAK,CAACM,IAAI,CAAC;AAC5F;AACA;AACA,uBAAuBX,UAAU,CAACY,WAAW,CAACI,EAAE;AAChD,gBAAgBX,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACW,EAAE,GAAGX,KAAK,CAACW,EAAE,GAAGX,KAAK,CAACM,IAAI,CAAC;AAC3F,iBAAiBN,KAAK,IAAIL,UAAU,CAACS,QAAQ,CAACC,YAAY,CAACL,KAAK,CAACW,EAAE,GAAGX,KAAK,CAACW,EAAE,GAAGX,KAAK,CAACM,IAAI,CAAC;AAC5F;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}