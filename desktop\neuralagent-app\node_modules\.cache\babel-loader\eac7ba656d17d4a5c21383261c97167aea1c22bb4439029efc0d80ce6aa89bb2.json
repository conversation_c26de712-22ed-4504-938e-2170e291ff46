{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { HashRouter as Router,\n// BrowserRouter\nRoutes, Route } from \"react-router-dom\";\nimport { useSelector, useDispatch } from 'react-redux';\nimport LoadingDialog from './components/LoadingDialog';\nimport FullLoading from './components/FullLoading';\nimport constants from './utils/constants';\nimport MessageBar from './components/Elements/MessageBar';\nimport { setAppLoading, setUser, setAccessToken, setLoadingDialog } from './store';\nimport RedirectTo from './components/RedirectTo';\nimport axios from './utils/axios';\nimport { logoutUser, refreshToken } from './utils/helpers';\nimport { AppMainContainer, OverlayContainer } from './layouts/Containers';\nimport Sidebar from './layouts/Sidebar';\nimport { useLocation } from 'react-router-dom';\nimport Login from './views/Login';\nimport SignUp from './views/SignUp';\nimport Home from './views/Home';\nimport Thread from './views/Thread';\nimport Overlay from './views/Overlay';\nimport BackgroundAuth from './views/BackgroundAuth';\nimport BackgroundTask from './views/BackgroundTask';\nimport BackgroundSetup from './views/BackgroundSetup';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppRoutes() {\n  _s();\n  const location = useLocation();\n  const isOverlayRoute = location.pathname === '/overlay';\n  const isBackgroundModeRoutes = location.pathname === '/background-auth' || location.pathname === '/background-task' || location.pathname === '/background-setup';\n  const accessToken = useSelector(state => state.accessToken);\n  const isError = useSelector(state => state.isError);\n  const errorMessage = useSelector(state => state.errorMessage);\n  const isSuccess = useSelector(state => state.isSuccess);\n  const successMsg = useSelector(state => state.successMsg);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isError && /*#__PURE__*/_jsxDEV(MessageBar, {\n      message: errorMessage,\n      backgroundColor: \"var(--danger-color)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 19\n    }, this), isSuccess && /*#__PURE__*/_jsxDEV(MessageBar, {\n      message: successMsg,\n      backgroundColor: \"var(--success-color)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 21\n    }, this), accessToken !== null ? isOverlayRoute || isBackgroundModeRoutes ? isOverlayRoute ? /*#__PURE__*/_jsxDEV(OverlayContainer, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/overlay\",\n          element: /*#__PURE__*/_jsxDEV(Overlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 15\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this) : /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/background-auth\",\n        element: /*#__PURE__*/_jsxDEV(BackgroundAuth, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/background-task\",\n        element: /*#__PURE__*/_jsxDEV(BackgroundTask, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/background-setup\",\n        element: /*#__PURE__*/_jsxDEV(BackgroundSetup, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 15\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this) : /*#__PURE__*/_jsxDEV(AppMainContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/threads/:tid\",\n          element: /*#__PURE__*/_jsxDEV(Thread, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(RedirectTo, {\n            linkType: \"router\",\n            to: \"/\",\n            redirectType: \"replace\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"signup\",\n        element: /*#__PURE__*/_jsxDEV(SignUp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(RedirectTo, {\n          linkType: \"router\",\n          to: \"/login\",\n          redirectType: \"replace\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(AppRoutes, \"uHR+WQMGooPUx+U/XeUluBjl2SE=\", false, function () {\n  return [useLocation, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = AppRoutes;\nfunction App() {\n  _s2();\n  const isAppLoading = useSelector(state => state.isAppLoading);\n  const isFullLoading = useSelector(state => state.isFullLoading);\n  const isLoadingDialog = useSelector(state => state.isLoadingDialog);\n  const dispatch = useDispatch();\n  const [_windowDims, setWindowDims] = useState();\n  const [isMobileBarOpen, setMobileBarOpen] = useState(false);\n  const handleResize = () => {\n    setWindowDims({\n      height: window.innerHeight,\n      width: window.innerWidth\n    });\n  };\n  useEffect(() => {\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const storedAccessToken = await window.electronAPI.getToken();\n      console.log(storedAccessToken);\n      if (storedAccessToken !== undefined && storedAccessToken !== null) {\n        dispatch(setAccessToken(storedAccessToken));\n        getUserInfo(storedAccessToken);\n      } else {\n        dispatch(setAppLoading(false));\n      }\n    };\n    asyncTask();\n  }, []);\n  const getUserInfo = accessToken => {\n    dispatch(setAppLoading(true));\n    axios.get('/auth/user_info', {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      dispatch(setUser(response.data));\n      dispatch(setAppLoading(false));\n    }).catch(error => {\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        refreshToken();\n      } else {\n        dispatch(setAppLoading(false));\n      }\n    });\n  };\n  useEffect(() => {\n    var _window$electronAPI;\n    if ((_window$electronAPI = window.electronAPI) !== null && _window$electronAPI !== void 0 && _window$electronAPI.onLogout) {\n      window.electronAPI.onLogout(async () => {\n        const token = await window.electronAPI.getToken();\n        logoutUser(token, dispatch);\n      });\n    }\n  }, []);\n  const cancelAllRunningTasks = async () => {\n    const token = await window.electronAPI.getToken();\n    if (token === null) {\n      return;\n    }\n    dispatch(setLoadingDialog(true));\n    try {\n      await axios.post(`/threads/cancel_all_running_tasks`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      window.electronAPI.stopAIAgent();\n    } catch (error) {} finally {\n      dispatch(setLoadingDialog(false));\n    }\n  };\n  useEffect(() => {\n    var _window$electronAPI2;\n    if ((_window$electronAPI2 = window.electronAPI) !== null && _window$electronAPI2 !== void 0 && _window$electronAPI2.onCancelAllTasksTrigger) {\n      window.electronAPI.onCancelAllTasksTrigger(async () => {\n        await cancelAllRunningTasks();\n        window.electronAPI.cancelAllTasksDone();\n      });\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isFullLoading ? /*#__PURE__*/_jsxDEV(FullLoading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 25\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), isLoadingDialog ? /*#__PURE__*/_jsxDEV(LoadingDialog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 27\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), isAppLoading ? /*#__PURE__*/_jsxDEV(FullLoading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 24\n    }, this) : /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s2(App, \"5emUwgJCvi170RHT89XwY64fTKQ=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch];\n});\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRoutes\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useSelector", "useDispatch", "LoadingDialog", "FullLoading", "constants", "MessageBar", "setAppLoading", "setUser", "setAccessToken", "setLoadingDialog", "RedirectTo", "axios", "logoutUser", "refreshToken", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OverlayContainer", "Sidebar", "useLocation", "<PERSON><PERSON>", "SignUp", "Home", "<PERSON><PERSON><PERSON>", "Overlay", "Background<PERSON>uth", "BackgroundTask", "BackgroundSetup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppRoutes", "_s", "location", "isOverlayRoute", "pathname", "isBackgroundModeRoutes", "accessToken", "state", "isError", "errorMessage", "isSuccess", "successMsg", "children", "message", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "linkType", "to", "redirectType", "_c", "App", "_s2", "isAppLoading", "isFullLoading", "isLoadingDialog", "dispatch", "_windowDims", "setWindowDims", "isMobileBarOpen", "setMobileBarOpen", "handleResize", "height", "window", "innerHeight", "width", "innerWidth", "addEventListener", "removeEventListener", "asyncTask", "storedAccessToken", "electronAPI", "getToken", "console", "log", "undefined", "getUserInfo", "get", "headers", "then", "response", "data", "catch", "error", "status", "UNAUTHORIZED", "_window$electronAPI", "onLogout", "token", "cancelAllRunningTasks", "post", "stopAIAgent", "_window$electronAPI2", "onCancelAllTasksTrigger", "cancelAllTasksDone", "_c2", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './App.css';\nimport {\n  HashRouter as Router, // BrowserRouter\n  Routes,\n  Route\n} from \"react-router-dom\";\nimport { useSelector, useDispatch } from 'react-redux';\nimport LoadingDialog from './components/LoadingDialog';\nimport FullLoading from './components/FullLoading';\nimport constants from './utils/constants';\nimport MessageBar from './components/Elements/MessageBar';\nimport { setAppLoading, setUser, setAccessToken, setLoadingDialog } from './store';\nimport RedirectTo from './components/RedirectTo';\nimport axios from './utils/axios';\nimport { logoutUser, refreshToken } from './utils/helpers';\nimport { AppMainContainer, OverlayContainer } from './layouts/Containers';\nimport Sidebar from './layouts/Sidebar';\nimport { useLocation } from 'react-router-dom';\n\nimport Login from './views/Login';\nimport SignUp from './views/SignUp';\nimport Home from './views/Home';\nimport Thread from './views/Thread';\nimport Overlay from './views/Overlay';\nimport BackgroundAuth from './views/BackgroundAuth';\nimport BackgroundTask from './views/BackgroundTask';\nimport BackgroundSetup from './views/BackgroundSetup';\n\nfunction AppRoutes() {\n  const location = useLocation();\n  const isOverlayRoute = location.pathname === '/overlay';\n  const isBackgroundModeRoutes = location.pathname === '/background-auth' || location.pathname === '/background-task' || location.pathname === '/background-setup';\n\n  const accessToken = useSelector(state => state.accessToken);\n  const isError = useSelector(state => state.isError);\n  const errorMessage = useSelector(state => state.errorMessage);\n  const isSuccess = useSelector(state => state.isSuccess);\n  const successMsg = useSelector(state => state.successMsg);\n\n  return (\n    <>\n      {isError && <MessageBar message={errorMessage} backgroundColor='var(--danger-color)' />}\n      {isSuccess && <MessageBar message={successMsg} backgroundColor='var(--success-color)' />}\n\n      {accessToken !== null ? (\n        isOverlayRoute || isBackgroundModeRoutes ? (\n          isOverlayRoute ? (\n            <OverlayContainer>\n              <Routes>\n                <Route path=\"/overlay\" element={<Overlay />} />\n              </Routes>\n            </OverlayContainer>\n          ) : (\n            <Routes>\n              <Route path=\"/background-auth\" element={<BackgroundAuth />} />\n              <Route path=\"/background-task\" element={<BackgroundTask />} />\n              <Route path=\"/background-setup\" element={<BackgroundSetup />} />\n            </Routes>\n          )\n        ) : (\n          <AppMainContainer>\n            <Sidebar />\n            <Routes>\n              <Route path='/' element={<Home />} />\n              <Route path='/threads/:tid' element={<Thread />} />\n              <Route path=\"*\" element={<RedirectTo linkType=\"router\" to=\"/\" redirectType=\"replace\" />} />\n            </Routes>\n          </AppMainContainer>\n        )\n      ) : (\n        <Routes>\n          <Route path=\"login\" element={<Login />} />\n          <Route path=\"signup\" element={<SignUp />} />\n          <Route path=\"*\" element={<RedirectTo linkType=\"router\" to=\"/login\" redirectType=\"replace\" />} />\n        </Routes>\n      )}\n    </>\n  );\n}\n\n\nfunction App() {\n\n  const isAppLoading = useSelector(state => state.isAppLoading);\n  const isFullLoading = useSelector(state => state.isFullLoading);\n  const isLoadingDialog = useSelector(state => state.isLoadingDialog);\n\n  const dispatch = useDispatch();\n  const [_windowDims, setWindowDims] = useState();\n\n  const [isMobileBarOpen, setMobileBarOpen] = useState(false);\n\n  const handleResize = () => {\n    setWindowDims({\n      height: window.innerHeight,\n      width: window.innerWidth\n    });\n  }\n\n  useEffect(() => {\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n        window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const storedAccessToken = await window.electronAPI.getToken();\n      console.log(storedAccessToken);\n      if (storedAccessToken !== undefined && storedAccessToken !== null) {\n        dispatch(setAccessToken(storedAccessToken));\n        getUserInfo(storedAccessToken);\n      } else {\n        dispatch(setAppLoading(false));\n      }\n    }\n    asyncTask();\n  }, []);\n\n  const getUserInfo = (accessToken) => {\n    dispatch(setAppLoading(true));\n    axios.get('/auth/user_info', {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      dispatch(setUser(response.data));\n      dispatch(setAppLoading(false));\n    }).catch((error) => {\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        refreshToken();\n      } else {\n        dispatch(setAppLoading(false));\n      }\n    });\n  };\n\n  useEffect(() => {\n    if (window.electronAPI?.onLogout) {\n      window.electronAPI.onLogout(async () => {\n        const token = await window.electronAPI.getToken();\n        logoutUser(token, dispatch);\n      });\n    }\n  }, []);\n\n  const cancelAllRunningTasks = async () => {\n    const token = await window.electronAPI.getToken();\n    if (token === null) {\n      return;\n    }\n    dispatch(setLoadingDialog(true));\n    try {\n      await axios.post(`/threads/cancel_all_running_tasks`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n      });\n      window.electronAPI.stopAIAgent();\n    } catch (error) {\n    } finally {\n      dispatch(setLoadingDialog(false));\n    }\n  };\n\n  useEffect(() => {\n    if (window.electronAPI?.onCancelAllTasksTrigger) {\n      window.electronAPI.onCancelAllTasksTrigger(async () => {\n        await cancelAllRunningTasks();\n        window.electronAPI.cancelAllTasksDone();\n      });\n    }\n  }, []);\n\n  return (\n    <>\n      {\n        isFullLoading ? <FullLoading /> : <></>\n      }\n      {\n        isLoadingDialog ? <LoadingDialog /> : <></>\n      }\n      {\n        isAppLoading ? <FullLoading /> :\n        <Router>\n          <AppRoutes />\n        </Router>\n      }\n    </>\n  );\n}\n\n\nexport default App;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAClB,SACEC,UAAU,IAAIC,MAAM;AAAE;AACtBC,MAAM,EACNC,KAAK,QACA,kBAAkB;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,kCAAkC;AACzD,SAASC,aAAa,EAAEC,OAAO,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,SAAS;AAClF,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AAC1D,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,sBAAsB;AACzE,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,eAAe,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,cAAc,GAAGD,QAAQ,CAACE,QAAQ,KAAK,UAAU;EACvD,MAAMC,sBAAsB,GAAGH,QAAQ,CAACE,QAAQ,KAAK,kBAAkB,IAAIF,QAAQ,CAACE,QAAQ,KAAK,kBAAkB,IAAIF,QAAQ,CAACE,QAAQ,KAAK,mBAAmB;EAEhK,MAAME,WAAW,GAAGpC,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACD,WAAW,CAAC;EAC3D,MAAME,OAAO,GAAGtC,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACC,OAAO,CAAC;EACnD,MAAMC,YAAY,GAAGvC,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACE,YAAY,CAAC;EAC7D,MAAMC,SAAS,GAAGxC,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACG,SAAS,CAAC;EACvD,MAAMC,UAAU,GAAGzC,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACI,UAAU,CAAC;EAEzD,oBACEd,OAAA,CAAAE,SAAA;IAAAa,QAAA,GACGJ,OAAO,iBAAIX,OAAA,CAACtB,UAAU;MAACsC,OAAO,EAAEJ,YAAa;MAACK,eAAe,EAAC;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACtFR,SAAS,iBAAIb,OAAA,CAACtB,UAAU;MAACsC,OAAO,EAAEF,UAAW;MAACG,eAAe,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEvFZ,WAAW,KAAK,IAAI,GACnBH,cAAc,IAAIE,sBAAsB,GACtCF,cAAc,gBACZN,OAAA,CAACZ,gBAAgB;MAAA2B,QAAA,eACff,OAAA,CAAC7B,MAAM;QAAA4C,QAAA,eACLf,OAAA,CAAC5B,KAAK;UAACkD,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEvB,OAAA,CAACL,OAAO;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,gBAEnBrB,OAAA,CAAC7B,MAAM;MAAA4C,QAAA,gBACLf,OAAA,CAAC5B,KAAK;QAACkD,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAEvB,OAAA,CAACJ,cAAc;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DrB,OAAA,CAAC5B,KAAK;QAACkD,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAEvB,OAAA,CAACH,cAAc;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DrB,OAAA,CAAC5B,KAAK;QAACkD,IAAI,EAAC,mBAAmB;QAACC,OAAO,eAAEvB,OAAA,CAACF,eAAe;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CACT,gBAEDrB,OAAA,CAACb,gBAAgB;MAAA4B,QAAA,gBACff,OAAA,CAACX,OAAO;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXrB,OAAA,CAAC7B,MAAM;QAAA4C,QAAA,gBACLf,OAAA,CAAC5B,KAAK;UAACkD,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEvB,OAAA,CAACP,IAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCrB,OAAA,CAAC5B,KAAK;UAACkD,IAAI,EAAC,eAAe;UAACC,OAAO,eAAEvB,OAAA,CAACN,MAAM;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDrB,OAAA,CAAC5B,KAAK;UAACkD,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEvB,OAAA,CAACjB,UAAU;YAACyC,QAAQ,EAAC,QAAQ;YAACC,EAAE,EAAC,GAAG;YAACC,YAAY,EAAC;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACnB,gBAEDrB,OAAA,CAAC7B,MAAM;MAAA4C,QAAA,gBACLf,OAAA,CAAC5B,KAAK;QAACkD,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEvB,OAAA,CAACT,KAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CrB,OAAA,CAAC5B,KAAK;QAACkD,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEvB,OAAA,CAACR,MAAM;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CrB,OAAA,CAAC5B,KAAK;QAACkD,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEvB,OAAA,CAACjB,UAAU;UAACyC,QAAQ,EAAC,QAAQ;UAACC,EAAE,EAAC,QAAQ;UAACC,YAAY,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CACT;EAAA,eACD,CAAC;AAEP;AAACjB,EAAA,CAlDQD,SAAS;EAAA,QACCb,WAAW,EAIRjB,WAAW,EACfA,WAAW,EACNA,WAAW,EACdA,WAAW,EACVA,WAAW;AAAA;AAAAsD,EAAA,GATvBxB,SAAS;AAqDlB,SAASyB,GAAGA,CAAA,EAAG;EAAAC,GAAA;EAEb,MAAMC,YAAY,GAAGzD,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACoB,YAAY,CAAC;EAC7D,MAAMC,aAAa,GAAG1D,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACqB,aAAa,CAAC;EAC/D,MAAMC,eAAe,GAAG3D,WAAW,CAACqC,KAAK,IAAIA,KAAK,CAACsB,eAAe,CAAC;EAEnE,MAAMC,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4D,WAAW,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,CAAC;EAE/C,MAAM,CAACqE,eAAe,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMuE,YAAY,GAAGA,CAAA,KAAM;IACzBH,aAAa,CAAC;MACZI,MAAM,EAAEC,MAAM,CAACC,WAAW;MAC1BC,KAAK,EAAEF,MAAM,CAACG;IAChB,CAAC,CAAC;EACJ,CAAC;EAED3E,SAAS,CAAC,MAAM;IACdwE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAE/C,OAAO,MAAM;MACTE,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENtE,SAAS,CAAC,MAAM;IACd,MAAM8E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,iBAAiB,GAAG,MAAMP,MAAM,CAACQ,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC7DC,OAAO,CAACC,GAAG,CAACJ,iBAAiB,CAAC;MAC9B,IAAIA,iBAAiB,KAAKK,SAAS,IAAIL,iBAAiB,KAAK,IAAI,EAAE;QACjEd,QAAQ,CAACpD,cAAc,CAACkE,iBAAiB,CAAC,CAAC;QAC3CM,WAAW,CAACN,iBAAiB,CAAC;MAChC,CAAC,MAAM;QACLd,QAAQ,CAACtD,aAAa,CAAC,KAAK,CAAC,CAAC;MAChC;IACF,CAAC;IACDmE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,WAAW,GAAI5C,WAAW,IAAK;IACnCwB,QAAQ,CAACtD,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7BK,KAAK,CAACsE,GAAG,CAAC,iBAAiB,EAAE;MAC3BC,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAG9C;MAC/B;IACF,CAAC,CAAC,CAAC+C,IAAI,CAAEC,QAAQ,IAAK;MACpBxB,QAAQ,CAACrD,OAAO,CAAC6E,QAAQ,CAACC,IAAI,CAAC,CAAC;MAChCzB,QAAQ,CAACtD,aAAa,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC,CAAC,CAACgF,KAAK,CAAEC,KAAK,IAAK;MAClB,IAAIA,KAAK,CAACH,QAAQ,CAACI,MAAM,KAAKpF,SAAS,CAACoF,MAAM,CAACC,YAAY,EAAE;QAC3D5E,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACL+C,QAAQ,CAACtD,aAAa,CAAC,KAAK,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;EACJ,CAAC;EAEDX,SAAS,CAAC,MAAM;IAAA,IAAA+F,mBAAA;IACd,KAAAA,mBAAA,GAAIvB,MAAM,CAACQ,WAAW,cAAAe,mBAAA,eAAlBA,mBAAA,CAAoBC,QAAQ,EAAE;MAChCxB,MAAM,CAACQ,WAAW,CAACgB,QAAQ,CAAC,YAAY;QACtC,MAAMC,KAAK,GAAG,MAAMzB,MAAM,CAACQ,WAAW,CAACC,QAAQ,CAAC,CAAC;QACjDhE,UAAU,CAACgF,KAAK,EAAEhC,QAAQ,CAAC;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,MAAMD,KAAK,GAAG,MAAMzB,MAAM,CAACQ,WAAW,CAACC,QAAQ,CAAC,CAAC;IACjD,IAAIgB,KAAK,KAAK,IAAI,EAAE;MAClB;IACF;IACAhC,QAAQ,CAACnD,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI;MACF,MAAME,KAAK,CAACmF,IAAI,CAAC,mCAAmC,EAAE,CAAC,CAAC,EAAE;QACxDZ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUU,KAAK;QAClC;MACF,CAAC,CAAC;MACFzB,MAAM,CAACQ,WAAW,CAACoB,WAAW,CAAC,CAAC;IAClC,CAAC,CAAC,OAAOR,KAAK,EAAE,CAChB,CAAC,SAAS;MACR3B,QAAQ,CAACnD,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACnC;EACF,CAAC;EAEDd,SAAS,CAAC,MAAM;IAAA,IAAAqG,oBAAA;IACd,KAAAA,oBAAA,GAAI7B,MAAM,CAACQ,WAAW,cAAAqB,oBAAA,eAAlBA,oBAAA,CAAoBC,uBAAuB,EAAE;MAC/C9B,MAAM,CAACQ,WAAW,CAACsB,uBAAuB,CAAC,YAAY;QACrD,MAAMJ,qBAAqB,CAAC,CAAC;QAC7B1B,MAAM,CAACQ,WAAW,CAACuB,kBAAkB,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvE,OAAA,CAAAE,SAAA;IAAAa,QAAA,GAEIgB,aAAa,gBAAG/B,OAAA,CAACxB,WAAW;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrB,OAAA,CAAAE,SAAA,mBAAI,CAAC,EAGvC8B,eAAe,gBAAGhC,OAAA,CAACzB,aAAa;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrB,OAAA,CAAAE,SAAA,mBAAI,CAAC,EAG3C4B,YAAY,gBAAG9B,OAAA,CAACxB,WAAW;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAC9BrB,OAAA,CAAC9B,MAAM;MAAA6C,QAAA,eACLf,OAAA,CAACG,SAAS;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA,eAEX,CAAC;AAEP;AAACQ,GAAA,CA/GQD,GAAG;EAAA,QAEWvD,WAAW,EACVA,WAAW,EACTA,WAAW,EAElBC,WAAW;AAAA;AAAAkG,GAAA,GANrB5C,GAAG;AAkHZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAA6C,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}