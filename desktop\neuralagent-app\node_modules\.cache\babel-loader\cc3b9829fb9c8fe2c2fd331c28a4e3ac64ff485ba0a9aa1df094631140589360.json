{"ast": null, "code": "/**\n * react-router v7.5.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { FrameworkContext, RemixErrorBoundary, RouterProvider, createBrowserHistory, createClientRoutes, createClientRoutesWithHMRRevalidationOptOut, createRouter, decodeViaTurboStream, deserializeErrors, getPatchRoutesOnNavigationFunction, getSingleFetchDataStrategy, invariant, mapRouteProperties, matchRoutes, shouldHydrateRouteLoader, useFogOFWarDiscovery } from \"./chunk-KNED5TY2.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */React.createElement(RouterProvider, {\n    flushSync: ReactDOM.flushSync,\n    ...props\n  });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap?.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(importMap.textContent).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\");\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then(value => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch(e => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(ssrInfo.manifest.routes, ssrInfo.routeModules, ssrInfo.context.state, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  let hydrationData = void 0;\n  let loaderData = ssrInfo.context.state.loaderData;\n  if (ssrInfo.context.isSpaMode) {\n    if (ssrInfo.manifest.routes.root?.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    hydrationData = {\n      ...ssrInfo.context.state,\n      loaderData: {\n        ...loaderData\n      }\n    };\n    let initialMatches = matchRoutes(routes, window.location, window.__reactRouterContext?.basename);\n    if (initialMatches) {\n      for (let match of initialMatches) {\n        let routeId = match.route.id;\n        let route = ssrInfo.routeModules[routeId];\n        let manifestRoute = ssrInfo.manifest.routes[routeId];\n        if (route && manifestRoute && shouldHydrateRouteLoader(manifestRoute, route, ssrInfo.context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n          delete hydrationData.loaderData[routeId];\n        } else if (manifestRoute && !manifestRoute.hasLoader) {\n          hydrationData.loaderData[routeId] = null;\n        }\n      }\n    }\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: getSingleFetchDataStrategy(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.basename, () => router2),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.isSpaMode, ssrInfo.context.basename)\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0);\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe(newState => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(router, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */\n    React2.createElement(React2.Fragment, null, /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: {\n        manifest: ssrInfo.manifest,\n        routeModules: ssrInfo.routeModules,\n        future: ssrInfo.context.future,\n        criticalCss,\n        ssr: ssrInfo.context.ssr,\n        isSpaMode: ssrInfo.context.isSpaMode\n      }\n    }, /* @__PURE__ */React2.createElement(RemixErrorBoundary, {\n      location\n    }, /* @__PURE__ */React2.createElement(RouterProvider2, {\n      router\n    }))), /* @__PURE__ */React2.createElement(React2.Fragment, null))\n  );\n}\nexport { HydratedRouter, RouterProvider2 as RouterProvider };", "map": {"version": 3, "names": ["FrameworkContext", "RemixErrorBoundary", "RouterProvider", "createBrowserHistory", "createClientRoutes", "createClientRoutesWithHMRRevalidationOptOut", "createRouter", "decodeViaTurboStream", "deserializeErrors", "getPatchRoutesOnNavigationFunction", "getSingleFetchDataStrategy", "invariant", "mapRouteProperties", "matchRoutes", "shouldHydrateRouteLoader", "useFogOFWarDiscovery", "React", "ReactDOM", "RouterProvider2", "props", "createElement", "flushSync", "React2", "ssrInfo", "router", "initSsrInfo", "window", "__reactRouterContext", "__reactRouterManifest", "__reactRouterRouteModules", "sri", "importMap", "document", "querySelector", "textContent", "JSON", "parse", "integrity", "err", "console", "error", "context", "manifest", "routeModules", "stateDecodingPromise", "routerInitialized", "createHydratedRouter", "unstable_getContext", "Error", "localSsrInfo", "stream", "then", "value", "state", "catch", "e", "routes", "ssr", "isSpaMode", "hydrationData", "loaderData", "root", "<PERSON><PERSON><PERSON><PERSON>", "initialMatches", "location", "basename", "match", "routeId", "route", "id", "manifestRoute", "HydrateFallback", "errors", "router2", "history", "future", "unstable_middleware", "dataStrategy", "patchRoutesOnNavigation", "initialized", "initialize", "createRoutesForHMR", "__reactRouterDataRouter", "HydratedRouter", "criticalCss", "setCriticalCss", "useState", "process", "env", "NODE_ENV", "__reactRouterClearCriticalCss", "setLocation", "useLayoutEffect", "subscribe", "newState", "Fragment", "Provider"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/react-router/dist/development/dom-export.mjs"], "sourcesContent": ["/**\n * react-router v7.5.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  FrameworkContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  createBrowserHistory,\n  createClientRoutes,\n  createClientRoutesWithHMRRevalidationOptOut,\n  createRouter,\n  decodeViaTurboStream,\n  deserializeErrors,\n  getPatchRoutesOnNavigationFunction,\n  getSingleFetchDataStrategy,\n  invariant,\n  mapRouteProperties,\n  matchRoutes,\n  shouldHydrateRouteLoader,\n  useFogOFWarDiscovery\n} from \"./chunk-KNED5TY2.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */ React.createElement(RouterProvider, { flushSync: ReactDOM.flushSync, ...props });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap?.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(\n            importMap.textContent\n          ).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\n      \"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\"\n    );\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then((value) => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch((e) => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(\n    ssrInfo.manifest.routes,\n    ssrInfo.routeModules,\n    ssrInfo.context.state,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  let hydrationData = void 0;\n  let loaderData = ssrInfo.context.state.loaderData;\n  if (ssrInfo.context.isSpaMode) {\n    if (ssrInfo.manifest.routes.root?.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    hydrationData = {\n      ...ssrInfo.context.state,\n      loaderData: { ...loaderData }\n    };\n    let initialMatches = matchRoutes(\n      routes,\n      window.location,\n      window.__reactRouterContext?.basename\n    );\n    if (initialMatches) {\n      for (let match of initialMatches) {\n        let routeId = match.route.id;\n        let route = ssrInfo.routeModules[routeId];\n        let manifestRoute = ssrInfo.manifest.routes[routeId];\n        if (route && manifestRoute && shouldHydrateRouteLoader(\n          manifestRoute,\n          route,\n          ssrInfo.context.isSpaMode\n        ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n          delete hydrationData.loaderData[routeId];\n        } else if (manifestRoute && !manifestRoute.hasLoader) {\n          hydrationData.loaderData[routeId] = null;\n        }\n      }\n    }\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: getSingleFetchDataStrategy(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.basename,\n      () => router2\n    ),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.isSpaMode,\n      ssrInfo.context.basename\n    )\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(\n    process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0\n  );\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe((newState) => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(\n    router,\n    ssrInfo.manifest,\n    ssrInfo.routeModules,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\n      FrameworkContext.Provider,\n      {\n        value: {\n          manifest: ssrInfo.manifest,\n          routeModules: ssrInfo.routeModules,\n          future: ssrInfo.context.future,\n          criticalCss,\n          ssr: ssrInfo.context.ssr,\n          isSpaMode: ssrInfo.context.isSpaMode\n        }\n      },\n      /* @__PURE__ */ React2.createElement(RemixErrorBoundary, { location }, /* @__PURE__ */ React2.createElement(RouterProvider2, { router }))\n    ), /* @__PURE__ */ React2.createElement(React2.Fragment, null))\n  );\n}\nexport {\n  HydratedRouter,\n  RouterProvider2 as RouterProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,kBAAkB,EAClBC,2CAA2C,EAC3CC,YAAY,EACZC,oBAAoB,EACpBC,iBAAiB,EACjBC,kCAAkC,EAClCC,0BAA0B,EAC1BC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,wBAAwB,EACxBC,oBAAoB,QACf,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,eAAgBH,KAAK,CAACI,aAAa,CAAClB,cAAc,EAAE;IAAEmB,SAAS,EAAEJ,QAAQ,CAACI,SAAS;IAAE,GAAGF;EAAM,CAAC,CAAC;AACzG;;AAEA;AACA,OAAO,KAAKG,MAAM,MAAM,OAAO;AAC/B,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,MAAM,GAAG,IAAI;AACjB,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACF,OAAO,IAAIG,MAAM,CAACC,oBAAoB,IAAID,MAAM,CAACE,qBAAqB,IAAIF,MAAM,CAACG,yBAAyB,EAAE;IAC/G,IAAIH,MAAM,CAACE,qBAAqB,CAACE,GAAG,KAAK,IAAI,EAAE;MAC7C,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;MAChE,IAAIF,SAAS,EAAEG,WAAW,EAAE;QAC1B,IAAI;UACFR,MAAM,CAACE,qBAAqB,CAACE,GAAG,GAAGK,IAAI,CAACC,KAAK,CAC3CL,SAAS,CAACG,WACZ,CAAC,CAACG,SAAS;QACb,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;QAClD;MACF;IACF;IACAf,OAAO,GAAG;MACRkB,OAAO,EAAEf,MAAM,CAACC,oBAAoB;MACpCe,QAAQ,EAAEhB,MAAM,CAACE,qBAAqB;MACtCe,YAAY,EAAEjB,MAAM,CAACG,yBAAyB;MAC9Ce,oBAAoB,EAAE,KAAK,CAAC;MAC5BpB,MAAM,EAAE,KAAK,CAAC;MACdqB,iBAAiB,EAAE;IACrB,CAAC;EACH;AACF;AACA,SAASC,oBAAoBA,CAAC;EAC5BC;AACF,CAAC,EAAE;EACDtB,WAAW,CAAC,CAAC;EACb,IAAI,CAACF,OAAO,EAAE;IACZ,MAAM,IAAIyB,KAAK,CACb,mHACF,CAAC;EACH;EACA,IAAIC,YAAY,GAAG1B,OAAO;EAC1B,IAAI,CAACA,OAAO,CAACqB,oBAAoB,EAAE;IACjC,IAAIM,MAAM,GAAG3B,OAAO,CAACkB,OAAO,CAACS,MAAM;IACnCvC,SAAS,CAACuC,MAAM,EAAE,2CAA2C,CAAC;IAC9D3B,OAAO,CAACkB,OAAO,CAACS,MAAM,GAAG,KAAK,CAAC;IAC/B3B,OAAO,CAACqB,oBAAoB,GAAGrC,oBAAoB,CAAC2C,MAAM,EAAExB,MAAM,CAAC,CAACyB,IAAI,CAAEC,KAAK,IAAK;MAClF7B,OAAO,CAACkB,OAAO,CAACY,KAAK,GAAGD,KAAK,CAACA,KAAK;MACnCH,YAAY,CAACL,oBAAoB,CAACQ,KAAK,GAAG,IAAI;IAChD,CAAC,CAAC,CAACE,KAAK,CAAEC,CAAC,IAAK;MACdN,YAAY,CAACL,oBAAoB,CAACJ,KAAK,GAAGe,CAAC;IAC7C,CAAC,CAAC;EACJ;EACA,IAAIhC,OAAO,CAACqB,oBAAoB,CAACJ,KAAK,EAAE;IACtC,MAAMjB,OAAO,CAACqB,oBAAoB,CAACJ,KAAK;EAC1C;EACA,IAAI,CAACjB,OAAO,CAACqB,oBAAoB,CAACQ,KAAK,EAAE;IACvC,MAAM7B,OAAO,CAACqB,oBAAoB;EACpC;EACA,IAAIY,MAAM,GAAGpD,kBAAkB,CAC7BmB,OAAO,CAACmB,QAAQ,CAACc,MAAM,EACvBjC,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACY,KAAK,EACrB9B,OAAO,CAACkB,OAAO,CAACgB,GAAG,EACnBlC,OAAO,CAACkB,OAAO,CAACiB,SAClB,CAAC;EACD,IAAIC,aAAa,GAAG,KAAK,CAAC;EAC1B,IAAIC,UAAU,GAAGrC,OAAO,CAACkB,OAAO,CAACY,KAAK,CAACO,UAAU;EACjD,IAAIrC,OAAO,CAACkB,OAAO,CAACiB,SAAS,EAAE;IAC7B,IAAInC,OAAO,CAACmB,QAAQ,CAACc,MAAM,CAACK,IAAI,EAAEC,SAAS,IAAIF,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;MACjFD,aAAa,GAAG;QACdC,UAAU,EAAE;UACVC,IAAI,EAAED,UAAU,CAACC;QACnB;MACF,CAAC;IACH;EACF,CAAC,MAAM;IACLF,aAAa,GAAG;MACd,GAAGpC,OAAO,CAACkB,OAAO,CAACY,KAAK;MACxBO,UAAU,EAAE;QAAE,GAAGA;MAAW;IAC9B,CAAC;IACD,IAAIG,cAAc,GAAGlD,WAAW,CAC9B2C,MAAM,EACN9B,MAAM,CAACsC,QAAQ,EACftC,MAAM,CAACC,oBAAoB,EAAEsC,QAC/B,CAAC;IACD,IAAIF,cAAc,EAAE;MAClB,KAAK,IAAIG,KAAK,IAAIH,cAAc,EAAE;QAChC,IAAII,OAAO,GAAGD,KAAK,CAACE,KAAK,CAACC,EAAE;QAC5B,IAAID,KAAK,GAAG7C,OAAO,CAACoB,YAAY,CAACwB,OAAO,CAAC;QACzC,IAAIG,aAAa,GAAG/C,OAAO,CAACmB,QAAQ,CAACc,MAAM,CAACW,OAAO,CAAC;QACpD,IAAIC,KAAK,IAAIE,aAAa,IAAIxD,wBAAwB,CACpDwD,aAAa,EACbF,KAAK,EACL7C,OAAO,CAACkB,OAAO,CAACiB,SAClB,CAAC,KAAKU,KAAK,CAACG,eAAe,IAAI,CAACD,aAAa,CAACR,SAAS,CAAC,EAAE;UACxD,OAAOH,aAAa,CAACC,UAAU,CAACO,OAAO,CAAC;QAC1C,CAAC,MAAM,IAAIG,aAAa,IAAI,CAACA,aAAa,CAACR,SAAS,EAAE;UACpDH,aAAa,CAACC,UAAU,CAACO,OAAO,CAAC,GAAG,IAAI;QAC1C;MACF;IACF;IACA,IAAIR,aAAa,IAAIA,aAAa,CAACa,MAAM,EAAE;MACzCb,aAAa,CAACa,MAAM,GAAGhE,iBAAiB,CAACmD,aAAa,CAACa,MAAM,CAAC;IAChE;EACF;EACA,IAAIC,OAAO,GAAGnE,YAAY,CAAC;IACzBkD,MAAM;IACNkB,OAAO,EAAEvE,oBAAoB,CAAC,CAAC;IAC/B8D,QAAQ,EAAE1C,OAAO,CAACkB,OAAO,CAACwB,QAAQ;IAClClB,mBAAmB;IACnBY,aAAa;IACb/C,kBAAkB;IAClB+D,MAAM,EAAE;MACNC,mBAAmB,EAAErD,OAAO,CAACkB,OAAO,CAACkC,MAAM,CAACC;IAC9C,CAAC;IACDC,YAAY,EAAEnE,0BAA0B,CACtCa,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACgB,GAAG,EACnBlC,OAAO,CAACkB,OAAO,CAACwB,QAAQ,EACxB,MAAMQ,OACR,CAAC;IACDK,uBAAuB,EAAErE,kCAAkC,CACzDc,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACgB,GAAG,EACnBlC,OAAO,CAACkB,OAAO,CAACiB,SAAS,EACzBnC,OAAO,CAACkB,OAAO,CAACwB,QAClB;EACF,CAAC,CAAC;EACF1C,OAAO,CAACC,MAAM,GAAGiD,OAAO;EACxB,IAAIA,OAAO,CAACpB,KAAK,CAAC0B,WAAW,EAAE;IAC7BxD,OAAO,CAACsB,iBAAiB,GAAG,IAAI;IAChC4B,OAAO,CAACO,UAAU,CAAC,CAAC;EACtB;EACAP,OAAO,CAACQ,kBAAkB,GAAG;EAC7B5E,2CAA2C;EAC3CqB,MAAM,CAACwD,uBAAuB,GAAGT,OAAO;EACxC,OAAOA,OAAO;AAChB;AACA,SAASU,cAAcA,CAAChE,KAAK,EAAE;EAC7B,IAAI,CAACK,MAAM,EAAE;IACXA,MAAM,GAAGsB,oBAAoB,CAAC;MAC5BC,mBAAmB,EAAE5B,KAAK,CAAC4B;IAC7B,CAAC,CAAC;EACJ;EACA,IAAI,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAG/D,MAAM,CAACgE,QAAQ,CACjDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAGlE,OAAO,EAAEkB,OAAO,CAAC2C,WAAW,GAAG,KAAK,CAC/E,CAAC;EACD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,IAAIlE,OAAO,EAAE;MACXG,MAAM,CAACgE,6BAA6B,GAAG,MAAML,cAAc,CAAC,KAAK,CAAC,CAAC;IACrE;EACF;EACA,IAAI,CAACrB,QAAQ,EAAE2B,WAAW,CAAC,GAAGrE,MAAM,CAACgE,QAAQ,CAAC9D,MAAM,CAAC6B,KAAK,CAACW,QAAQ,CAAC;EACpE1C,MAAM,CAACsE,eAAe,CAAC,MAAM;IAC3B,IAAIrE,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACD,OAAO,CAACsB,iBAAiB,EAAE;MAC3DtB,OAAO,CAACsB,iBAAiB,GAAG,IAAI;MAChCtB,OAAO,CAACC,MAAM,CAACwD,UAAU,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACN1D,MAAM,CAACsE,eAAe,CAAC,MAAM;IAC3B,IAAIrE,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC7B,OAAOD,OAAO,CAACC,MAAM,CAACqE,SAAS,CAAEC,QAAQ,IAAK;QAC5C,IAAIA,QAAQ,CAAC9B,QAAQ,KAAKA,QAAQ,EAAE;UAClC2B,WAAW,CAACG,QAAQ,CAAC9B,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACdrD,SAAS,CAACY,OAAO,EAAE,wCAAwC,CAAC;EAC5DR,oBAAoB,CAClBS,MAAM,EACND,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACgB,GAAG,EACnBlC,OAAO,CAACkB,OAAO,CAACiB,SAClB,CAAC;EACD;IACE;IACA;IACA;IAAgBpC,MAAM,CAACF,aAAa,CAACE,MAAM,CAACyE,QAAQ,EAAE,IAAI,EAAE,eAAgBzE,MAAM,CAACF,aAAa,CAC9FpB,gBAAgB,CAACgG,QAAQ,EACzB;MACE5C,KAAK,EAAE;QACLV,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ;QAC1BC,YAAY,EAAEpB,OAAO,CAACoB,YAAY;QAClCgC,MAAM,EAAEpD,OAAO,CAACkB,OAAO,CAACkC,MAAM;QAC9BS,WAAW;QACX3B,GAAG,EAAElC,OAAO,CAACkB,OAAO,CAACgB,GAAG;QACxBC,SAAS,EAAEnC,OAAO,CAACkB,OAAO,CAACiB;MAC7B;IACF,CAAC,EACD,eAAgBpC,MAAM,CAACF,aAAa,CAACnB,kBAAkB,EAAE;MAAE+D;IAAS,CAAC,EAAE,eAAgB1C,MAAM,CAACF,aAAa,CAACF,eAAe,EAAE;MAAEM;IAAO,CAAC,CAAC,CAC1I,CAAC,EAAE,eAAgBF,MAAM,CAACF,aAAa,CAACE,MAAM,CAACyE,QAAQ,EAAE,IAAI,CAAC;EAAC;AAEnE;AACA,SACEZ,cAAc,EACdjE,eAAe,IAAIhB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}