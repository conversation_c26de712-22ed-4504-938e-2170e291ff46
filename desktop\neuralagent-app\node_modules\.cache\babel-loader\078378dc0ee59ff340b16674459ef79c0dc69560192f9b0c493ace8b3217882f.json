{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\DataDialogs\\\\ThreadDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Dialog from '../Elements/Dialog';\nimport { setLoadingDialog, setSuccess, setError } from '../../store';\nimport { useDispatch, useSelector } from 'react-redux';\nimport axios from '../../utils/axios';\nimport constants from '../../utils/constants';\nimport { FlexSpacer } from '../Elements/SmallElements';\nimport { Button } from '../Elements/Button';\nimport NATextField from '../Elements/TextFields';\nimport { getBadRequestErrorMessage } from '../../utils/helpers';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ThreadDialog({\n  isOpen,\n  setOpen,\n  threadObj = null,\n  onSuccess\n}) {\n  _s();\n  const [title, setTitle] = useState('');\n  const accessToken = useSelector(state => state.accessToken);\n  const dispatch = useDispatch();\n  const clearData = () => {\n    setTitle('');\n  };\n  const setDialogOpen = open => {\n    if (!open) {\n      clearData();\n    }\n    setOpen(open);\n  };\n  const isFormValid = () => {\n    return title.length > 0;\n  };\n  const editThread = () => {\n    if (!isFormValid()) {\n      return;\n    }\n    let data = {\n      title: title\n    };\n    dispatch(setLoadingDialog(true));\n    axios.put('/threads/' + threadObj.id, data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      dispatch(setLoadingDialog(false));\n      dispatch(setSuccess(true, 'Action Executed Successfully!'));\n      setTimeout(() => {\n        dispatch(setSuccess(false, ''));\n      }, 3000);\n      setDialogOpen(false);\n      if (onSuccess !== null) {\n        onSuccess();\n      }\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        dispatch(setError(true, getBadRequestErrorMessage(error.response.data)));\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n  const onConfirmClick = () => {\n    editThread();\n  };\n  useEffect(() => {\n    if (threadObj !== null) {\n      setTitle(threadObj.title);\n    } else {\n      clearData();\n    }\n  }, [threadObj]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      isOpen: isOpen,\n      setOpen: setDialogOpen,\n      maxWidth: \"500px\",\n      title: 'Edit Thread',\n      padding: \"10px 15px\",\n      isDarkMode: true,\n      child: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(NATextField, {\n            type: \"text\",\n            label: \"Title\",\n            labelFontWeight: \"500\",\n            verticalLabel: true,\n            value: title,\n            background: \"rgba(0, 0, 0, 0.3)\",\n            isDarkMode: true,\n            onChange: e => setTitle(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, void 0, false),\n      actions: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(FlexSpacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          borderRadius: 7,\n          padding: \"10px 15px\",\n          dark: true,\n          disabled: !isFormValid(),\n          onClick: onConfirmClick,\n          children: \"Confirm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(ThreadDialog, \"XHEsCyc995cgriZX2hqyNZ2hJVg=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = ThreadDialog;\nexport default ThreadDialog;\nvar _c;\n$RefreshReg$(_c, \"ThreadDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "setLoadingDialog", "setSuccess", "setError", "useDispatch", "useSelector", "axios", "constants", "FlexSpacer", "<PERSON><PERSON>", "NATextField", "getBadRequestErrorMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ThreadDialog", "isOpen", "<PERSON><PERSON><PERSON>", "threadObj", "onSuccess", "_s", "title", "setTitle", "accessToken", "state", "dispatch", "clearData", "setDialogOpen", "open", "isFormValid", "length", "editThread", "data", "put", "id", "headers", "then", "response", "setTimeout", "catch", "error", "status", "BAD_REQUEST", "GENERAL_ERROR", "onConfirmClick", "children", "max<PERSON><PERSON><PERSON>", "padding", "isDarkMode", "child", "type", "label", "labelFontWeight", "verticalLabel", "value", "background", "onChange", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "actions", "borderRadius", "dark", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/DataDialogs/ThreadDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Dialog from '../Elements/Dialog';\nimport { setLoadingDialog, setSuccess, setError } from '../../store';\nimport { useDispatch, useSelector } from 'react-redux';\nimport axios from '../../utils/axios';\nimport constants from '../../utils/constants';\nimport { FlexSpacer } from '../Elements/SmallElements';\nimport { Button } from '../Elements/Button';\nimport NATextField from '../Elements/TextFields';\nimport { getBadRequestErrorMessage } from '../../utils/helpers';\n\n\nfunction ThreadDialog({ isOpen, setOpen, threadObj=null, onSuccess }) {\n\n  const [title, setTitle] = useState('');\n\n  const accessToken = useSelector(state => state.accessToken);\n  \n  const dispatch = useDispatch();\n\n  const clearData = () => {\n    setTitle('');\n  };\n\n  const setDialogOpen = (open) => {\n    if (!open) {\n      clearData();\n    }\n    setOpen(open);\n  };\n\n  const isFormValid = () => {\n    return title.length > 0;\n  };\n\n  const editThread = () => {\n    if (!isFormValid()) {\n      return;\n    }\n\n    let data = {\n      title: title,\n    };\n\n    dispatch(setLoadingDialog(true));\n    axios.put('/threads/' + threadObj.id, data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      dispatch(setLoadingDialog(false));\n      dispatch(setSuccess(true, 'Action Executed Successfully!'));\n      setTimeout(() => {\n        dispatch(setSuccess(false, ''));\n      }, 3000);\n      setDialogOpen(false);\n      if (onSuccess !== null) {\n        onSuccess();\n      }\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        dispatch(setError(true, getBadRequestErrorMessage(error.response.data)));\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n\n  const onConfirmClick = () => {\n    editThread();\n  };\n\n  useEffect(() => {\n    if (threadObj !== null) {\n      setTitle(threadObj.title);\n    } else {\n      clearData();\n    }\n  }, [threadObj]);\n\n  return (\n    <>\n      <Dialog\n        isOpen={isOpen}\n        setOpen={setDialogOpen}\n        maxWidth=\"500px\"\n        title={'Edit Thread'}\n        padding='10px 15px'\n        isDarkMode\n        child={\n          <>\n            <div>\n              <NATextField\n                type=\"text\"\n                label='Title'\n                labelFontWeight='500'\n                verticalLabel\n                value={title}\n                background='rgba(0, 0, 0, 0.3)'\n                isDarkMode\n                onChange={(e) => setTitle(e.target.value)}\n              />\n            </div>\n          </>\n        }\n        actions={\n          <>\n            <FlexSpacer />\n            <Button borderRadius={7} padding=\"10px 15px\" dark disabled={!isFormValid()}\n              onClick={onConfirmClick}>\n              Confirm\n            </Button>\n          </>\n        }\n      />\n    </>\n  );\n}\n\nexport default ThreadDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,aAAa;AACpE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,yBAAyB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGhE,SAASC,YAAYA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,SAAS,GAAC,IAAI;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAEpE,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM0B,WAAW,GAAGnB,WAAW,CAACoB,KAAK,IAAIA,KAAK,CAACD,WAAW,CAAC;EAE3D,MAAME,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACtBJ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMK,aAAa,GAAIC,IAAI,IAAK;IAC9B,IAAI,CAACA,IAAI,EAAE;MACTF,SAAS,CAAC,CAAC;IACb;IACAT,OAAO,CAACW,IAAI,CAAC;EACf,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,OAAOR,KAAK,CAACS,MAAM,GAAG,CAAC;EACzB,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE;MAClB;IACF;IAEA,IAAIG,IAAI,GAAG;MACTX,KAAK,EAAEA;IACT,CAAC;IAEDI,QAAQ,CAACzB,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCK,KAAK,CAAC4B,GAAG,CAAC,WAAW,GAAGf,SAAS,CAACgB,EAAE,EAAEF,IAAI,EAAE;MAC1CG,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGZ;MAC/B;IACF,CAAC,CAAC,CAACa,IAAI,CAAEC,QAAQ,IAAK;MACpBZ,QAAQ,CAACzB,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjCyB,QAAQ,CAACxB,UAAU,CAAC,IAAI,EAAE,+BAA+B,CAAC,CAAC;MAC3DqC,UAAU,CAAC,MAAM;QACfb,QAAQ,CAACxB,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MACjC,CAAC,EAAE,IAAI,CAAC;MACR0B,aAAa,CAAC,KAAK,CAAC;MACpB,IAAIR,SAAS,KAAK,IAAI,EAAE;QACtBA,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,CAACoB,KAAK,CAAEC,KAAK,IAAK;MAClBf,QAAQ,CAACzB,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAIwC,KAAK,CAACH,QAAQ,CAACI,MAAM,KAAKnC,SAAS,CAACmC,MAAM,CAACC,WAAW,EAAE;QAC1DjB,QAAQ,CAACvB,QAAQ,CAAC,IAAI,EAAEQ,yBAAyB,CAAC8B,KAAK,CAACH,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC;MAC1E,CAAC,MAAM;QACLP,QAAQ,CAACvB,QAAQ,CAAC,IAAI,EAAEI,SAAS,CAACqC,aAAa,CAAC,CAAC;MACnD;MACAL,UAAU,CAAC,MAAM;QACfb,QAAQ,CAACvB,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0C,cAAc,GAAGA,CAAA,KAAM;IAC3Bb,UAAU,CAAC,CAAC;EACd,CAAC;EAEDjC,SAAS,CAAC,MAAM;IACd,IAAIoB,SAAS,KAAK,IAAI,EAAE;MACtBI,QAAQ,CAACJ,SAAS,CAACG,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLK,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EAEf,oBACEN,OAAA,CAAAE,SAAA;IAAA+B,QAAA,eACEjC,OAAA,CAACb,MAAM;MACLiB,MAAM,EAAEA,MAAO;MACfC,OAAO,EAAEU,aAAc;MACvBmB,QAAQ,EAAC,OAAO;MAChBzB,KAAK,EAAE,aAAc;MACrB0B,OAAO,EAAC,WAAW;MACnBC,UAAU;MACVC,KAAK,eACHrC,OAAA,CAAAE,SAAA;QAAA+B,QAAA,eACEjC,OAAA;UAAAiC,QAAA,eACEjC,OAAA,CAACH,WAAW;YACVyC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,OAAO;YACbC,eAAe,EAAC,KAAK;YACrBC,aAAa;YACbC,KAAK,EAAEjC,KAAM;YACbkC,UAAU,EAAC,oBAAoB;YAC/BP,UAAU;YACVQ,QAAQ,EAAGC,CAAC,IAAKnC,QAAQ,CAACmC,CAAC,CAACC,MAAM,CAACJ,KAAK;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,gBACN,CACH;MACDC,OAAO,eACLnD,OAAA,CAAAE,SAAA;QAAA+B,QAAA,gBACEjC,OAAA,CAACL,UAAU;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdlD,OAAA,CAACJ,MAAM;UAACwD,YAAY,EAAE,CAAE;UAACjB,OAAO,EAAC,WAAW;UAACkB,IAAI;UAACC,QAAQ,EAAE,CAACrC,WAAW,CAAC,CAAE;UACzEsC,OAAO,EAAEvB,cAAe;UAAAC,QAAA,EAAC;QAE3B;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC,gBACF,CAAC;AAEP;AAAC1C,EAAA,CA7GQL,YAAY;EAAA,QAICX,WAAW,EAEdD,WAAW;AAAA;AAAAiE,EAAA,GANrBrD,YAAY;AA+GrB,eAAeA,YAAY;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}