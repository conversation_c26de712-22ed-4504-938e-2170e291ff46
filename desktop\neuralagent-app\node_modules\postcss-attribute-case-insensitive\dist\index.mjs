import e from"postcss-selector-parser";function t(e){return"attribute"===e.type&&e.insensitive}function n(e,t,s){const c=s.charAt(t);if(""===c)return e;let o=e.map((e=>e+c));const r=c.toLocaleUpperCase();return r!==c&&(o=o.concat(e.map((e=>e+r)))),n(o,t+1,s)}function s(e){return n([""],0,e.value).map((t=>{const n=e.clone({spaces:{after:e.spaces.after,before:e.spaces.before},insensitive:!1});return n.setValue(t),n}))}function c(n){let c=[];n.each((n=>{(function(e){return e.some(t)})(n)&&(c=c.concat(function(n){let c=[e.selector({value:"",nodes:[]})];return n.walk((e=>{if(!t(e))return void c.forEach((t=>{t.append(e.clone())}));const n=s(e),o=[];n.forEach((e=>{c.forEach((t=>{const n=t.clone({});n.append(e),o.push(n)}))})),c=o})),c}(n)),n.remove())})),c.length&&c.forEach((e=>n.append(e)))}const o=()=>({postcssPlugin:"postcss-attribute-case-insensitive",Rule(t){if(t.selector.includes("i]")){const n=e(c).processSync(t.selector);if(n===t.selector)return;t.replaceWith(t.clone({selector:n}))}}});o.postcss=!0;export{o as default};
