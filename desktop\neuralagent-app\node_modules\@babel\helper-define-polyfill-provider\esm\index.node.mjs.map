{"version": 3, "file": "index.node.mjs", "sources": ["../src/utils.ts", "../src/imports-injector.ts", "../src/debug-utils.ts", "../src/normalize-options.ts", "../src/visitors/usage.ts", "../src/visitors/entry.ts", "../src/node/dependencies.ts", "../src/meta-resolver.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, template } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type { Utils } from \"./types\";\nimport type ImportsCachedInjector from \"./imports-injector\";\n\nexport function intersection<T>(a: Set<T>, b: Set<T>): Set<T> {\n  const result = new Set<T>();\n  a.forEach(v => b.has(v) && result.add(v));\n  return result;\n}\n\nexport function has(object: any, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction resolve(\n  path: NodePath,\n  resolved: Set<NodePath> = new Set(),\n): NodePath | undefined {\n  if (resolved.has(path)) return;\n  resolved.add(path);\n\n  if (path.isVariableDeclarator()) {\n    if (path.get(\"id\").isIdentifier()) {\n      return resolve(path.get(\"init\"), resolved);\n    }\n  } else if (path.isReferencedIdentifier()) {\n    const binding = path.scope.getBinding(path.node.name);\n    if (!binding) return path;\n    if (!binding.constant) return;\n    return resolve(binding.path, resolved);\n  }\n  return path;\n}\n\nfunction resolveId(path: NodePath): string {\n  if (\n    path.isIdentifier() &&\n    !path.scope.hasBinding(path.node.name, /* noGlobals */ true)\n  ) {\n    return path.node.name;\n  }\n\n  const resolved = resolve(path);\n  if (resolved?.isIdentifier()) {\n    return resolved.node.name;\n  }\n}\n\nexport function resolveKey(\n  path: NodePath<t.Expression | t.PrivateName>,\n  computed: boolean = false,\n) {\n  const { scope } = path;\n  if (path.isStringLiteral()) return path.node.value;\n  const isIdentifier = path.isIdentifier();\n  if (\n    isIdentifier &&\n    !(computed || (path.parent as t.MemberExpression).computed)\n  ) {\n    return path.node.name;\n  }\n\n  if (\n    computed &&\n    path.isMemberExpression() &&\n    path.get(\"object\").isIdentifier({ name: \"Symbol\" }) &&\n    !scope.hasBinding(\"Symbol\", /* noGlobals */ true)\n  ) {\n    const sym = resolveKey(path.get(\"property\"), path.node.computed);\n    if (sym) return \"Symbol.\" + sym;\n  }\n\n  if (\n    isIdentifier\n      ? scope.hasBinding(path.node.name, /* noGlobals */ true)\n      : path.isPure()\n  ) {\n    const { value } = path.evaluate();\n    if (typeof value === \"string\") return value;\n  }\n}\n\nexport function resolveSource(obj: NodePath): {\n  id: string | null;\n  placement: \"prototype\" | \"static\" | null;\n} {\n  if (\n    obj.isMemberExpression() &&\n    obj.get(\"property\").isIdentifier({ name: \"prototype\" })\n  ) {\n    const id = resolveId(obj.get(\"object\"));\n\n    if (id) {\n      return { id, placement: \"prototype\" };\n    }\n    return { id: null, placement: null };\n  }\n\n  const id = resolveId(obj);\n  if (id) {\n    return { id, placement: \"static\" };\n  }\n\n  const path = resolve(obj);\n  switch (path?.type) {\n    case \"RegExpLiteral\":\n      return { id: \"RegExp\", placement: \"prototype\" };\n    case \"FunctionExpression\":\n      return { id: \"Function\", placement: \"prototype\" };\n    case \"StringLiteral\":\n      return { id: \"String\", placement: \"prototype\" };\n    case \"NumberLiteral\":\n      return { id: \"Number\", placement: \"prototype\" };\n    case \"BooleanLiteral\":\n      return { id: \"Boolean\", placement: \"prototype\" };\n    case \"ObjectExpression\":\n      return { id: \"Object\", placement: \"prototype\" };\n    case \"ArrayExpression\":\n      return { id: \"Array\", placement: \"prototype\" };\n  }\n\n  return { id: null, placement: null };\n}\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath<t.Statement>) {\n  if (!t.isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    t.isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nfunction hoist(node: t.Node) {\n  // @ts-expect-error\n  node._blockHoist = 3;\n  return node;\n}\n\nexport function createUtilsGetter(cache: ImportsCachedInjector) {\n  return (path: NodePath): Utils => {\n    const prog = path.findParent(p => p.isProgram()) as NodePath<t.Program>;\n\n    return {\n      injectGlobalImport(url, moduleName) {\n        cache.storeAnonymous(prog, url, moduleName, (isScript, source) => {\n          return isScript\n            ? template.statement.ast`require(${source})`\n            : t.importDeclaration([], source);\n        });\n      },\n      injectNamedImport(url, name, hint = name, moduleName) {\n        return cache.storeNamed(\n          prog,\n          url,\n          name,\n          moduleName,\n          (isScript, source, name) => {\n            const id = prog.scope.generateUidIdentifier(hint);\n            return {\n              node: isScript\n                ? hoist(template.statement.ast`\n                  var ${id} = require(${source}).${name}\n                `)\n                : t.importDeclaration([t.importSpecifier(id, name)], source),\n              name: id.name,\n            };\n          },\n        );\n      },\n      injectDefaultImport(url, hint = url, moduleName) {\n        return cache.storeNamed(\n          prog,\n          url,\n          \"default\",\n          moduleName,\n          (isScript, source) => {\n            const id = prog.scope.generateUidIdentifier(hint);\n            return {\n              node: isScript\n                ? hoist(template.statement.ast`var ${id} = require(${source})`)\n                : t.importDeclaration([t.importDefaultSpecifier(id)], source),\n              name: id.name,\n            };\n          },\n        );\n      },\n    };\n  };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\ntype StrMap<K> = Map<string, K>;\n\nexport default class ImportsCachedInjector {\n  _imports: WeakMap<NodePath<t.Program>, StrMap<string>>;\n  _anonymousImports: WeakMap<NodePath<t.Program>, Set<string>>;\n  _lastImports: WeakMap<\n    NodePath<t.Program>,\n    Array<{ path: NodePath<t.Node>; index: number }>\n  >;\n  _resolver: (url: string) => string;\n  _getPreferredIndex: (url: string) => number;\n\n  constructor(\n    resolver: (url: string) => string,\n    getPreferredIndex: (url: string) => number,\n  ) {\n    this._imports = new WeakMap();\n    this._anonymousImports = new WeakMap();\n    this._lastImports = new WeakMap();\n    this._resolver = resolver;\n    this._getPreferredIndex = getPreferredIndex;\n  }\n\n  storeAnonymous(\n    programPath: NodePath<t.Program>,\n    url: string,\n    moduleName: string,\n    getVal: (isScript: boolean, source: t.StringLiteral) => t.Node,\n  ) {\n    const key = this._normalizeKey(programPath, url);\n    const imports = this._ensure<Set<string>>(\n      this._anonymousImports,\n      programPath,\n      Set,\n    );\n\n    if (imports.has(key)) return;\n\n    const node = getVal(\n      programPath.node.sourceType === \"script\",\n      t.stringLiteral(this._resolver(url)),\n    );\n    imports.add(key);\n    this._injectImport(programPath, node, moduleName);\n  }\n\n  storeNamed(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string,\n    moduleName: string,\n    getVal: (\n      isScript: boolean,\n      // eslint-disable-next-line no-undef\n      source: t.StringLiteral,\n      // eslint-disable-next-line no-undef\n      name: t.Identifier,\n    ) => { node: t.Node; name: string },\n  ) {\n    const key = this._normalizeKey(programPath, url, name);\n    const imports = this._ensure<Map<string, any>>(\n      this._imports,\n      programPath,\n      Map,\n    );\n\n    if (!imports.has(key)) {\n      const { node, name: id } = getVal(\n        programPath.node.sourceType === \"script\",\n        t.stringLiteral(this._resolver(url)),\n        t.identifier(name),\n      );\n      imports.set(key, id);\n      this._injectImport(programPath, node, moduleName);\n    }\n\n    return t.identifier(imports.get(key));\n  }\n\n  _injectImport(\n    programPath: NodePath<t.Program>,\n    node: t.Node,\n    moduleName: string,\n  ) {\n    const newIndex = this._getPreferredIndex(moduleName);\n    const lastImports = this._lastImports.get(programPath) ?? [];\n\n    const isPathStillValid = (path: NodePath) =>\n      path.node &&\n      // Sometimes the AST is modified and the \"last import\"\n      // we have has been replaced\n      path.parent === programPath.node &&\n      path.container === programPath.node.body;\n\n    let last: NodePath;\n\n    if (newIndex === Infinity) {\n      // Fast path: we can always just insert at the end if newIndex is `Infinity`\n      if (lastImports.length > 0) {\n        last = lastImports[lastImports.length - 1].path;\n        if (!isPathStillValid(last)) last = undefined;\n      }\n    } else {\n      for (const [i, data] of lastImports.entries()) {\n        const { path, index } = data;\n        if (isPathStillValid(path)) {\n          if (newIndex < index) {\n            const [newPath] = path.insertBefore(node);\n            lastImports.splice(i, 0, { path: newPath, index: newIndex });\n            return;\n          }\n          last = path;\n        }\n      }\n    }\n\n    if (last) {\n      const [newPath] = last.insertAfter(node);\n      lastImports.push({ path: newPath, index: newIndex });\n    } else {\n      const [newPath] = programPath.unshiftContainer(\"body\", node);\n      this._lastImports.set(programPath, [{ path: newPath, index: newIndex }]);\n    }\n  }\n\n  _ensure<C extends Map<string, any> | Set<string>>(\n    map: WeakMap<NodePath<t.Program>, C>,\n    programPath: NodePath<t.Program>,\n    Collection: { new (...args: any): C },\n  ): C {\n    let collection = map.get(programPath);\n    if (!collection) {\n      collection = new Collection();\n      map.set(programPath, collection);\n    }\n    return collection;\n  }\n\n  _normalizeKey(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string = \"\",\n  ): string {\n    const { sourceType } = programPath.node;\n\n    // If we rely on the imported binding (the \"name\" parameter), we also need to cache\n    // based on the sourceType. This is because the module transforms change the names\n    // of the import variables.\n    return `${name && sourceType}::${url}::${name}`;\n  }\n}\n", "import { prettifyTargets } from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"./types\";\n\nexport const presetEnvSilentDebugHeader =\n  \"#__secret_key__@babel/preset-env__don't_log_debug_header_and_resolved_targets\";\n\nexport function stringifyTargetsMultiline(targets: Targets): string {\n  return JSON.stringify(prettifyTargets(targets), null, 2);\n}\n\nexport function stringifyTargets(targets: Targets): string {\n  return JSON.stringify(targets)\n    .replace(/,/g, \", \")\n    .replace(/^\\{\"/, '{ \"')\n    .replace(/\"\\}$/, '\" }');\n}\n", "import { intersection } from \"./utils\";\nimport type {\n  Pattern,\n  PluginOptions,\n  MissingDependenciesOption,\n} from \"./types\";\n\nfunction patternToRegExp(pattern: Pattern): RegExp | null {\n  if (pattern instanceof RegExp) return pattern;\n\n  try {\n    return new RegExp(`^${pattern}$`);\n  } catch {\n    return null;\n  }\n}\n\nfunction buildUnusedError(label, unused) {\n  if (!unused.length) return \"\";\n  return (\n    `  - The following \"${label}\" patterns didn't match any polyfill:\\n` +\n    unused.map(original => `    ${String(original)}\\n`).join(\"\")\n  );\n}\n\nfunction buldDuplicatesError(duplicates) {\n  if (!duplicates.size) return \"\";\n  return (\n    `  - The following polyfills were matched both by \"include\" and \"exclude\" patterns:\\n` +\n    Array.from(duplicates, name => `    ${name}\\n`).join(\"\")\n  );\n}\n\nexport function validateIncludeExclude(\n  provider: string,\n  polyfills: Map<string, unknown>,\n  includePatterns: Pattern[],\n  excludePatterns: Pattern[],\n) {\n  let current;\n  const filter = pattern => {\n    const regexp = patternToRegExp(pattern);\n    if (!regexp) return false;\n\n    let matched = false;\n    for (const polyfill of polyfills.keys()) {\n      if (regexp.test(polyfill)) {\n        matched = true;\n        current.add(polyfill);\n      }\n    }\n    return !matched;\n  };\n\n  // prettier-ignore\n  const include = current = new Set<string> ();\n  const unusedInclude = Array.from(includePatterns).filter(filter);\n\n  // prettier-ignore\n  const exclude = current = new Set<string> ();\n  const unusedExclude = Array.from(excludePatterns).filter(filter);\n\n  const duplicates = intersection(include, exclude);\n\n  if (\n    duplicates.size > 0 ||\n    unusedInclude.length > 0 ||\n    unusedExclude.length > 0\n  ) {\n    throw new Error(\n      `Error while validating the \"${provider}\" provider options:\\n` +\n        buildUnusedError(\"include\", unusedInclude) +\n        buildUnusedError(\"exclude\", unusedExclude) +\n        buldDuplicatesError(duplicates),\n    );\n  }\n\n  return { include, exclude };\n}\n\nexport function applyMissingDependenciesDefaults(\n  options: PluginOptions,\n  babelApi: any,\n): MissingDependenciesOption {\n  const { missingDependencies = {} } = options;\n  if (missingDependencies === false) return false;\n\n  const caller = babelApi.caller(caller => caller?.name);\n\n  const {\n    log = \"deferred\",\n    inject = caller === \"rollup-plugin-babel\" ? \"throw\" : \"import\",\n    all = false,\n  } = missingDependencies;\n\n  return { log, inject, all };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { CallProvider } from \"./index\";\n\nimport { resolveKey, resolveSource } from \"../utils\";\n\nfunction isRemoved(path: NodePath) {\n  if (path.removed) return true;\n  if (!path.parentPath) return false;\n  if (path.listKey) {\n    if (!path.parentPath.node?.[path.listKey]?.includes(path.node)) return true;\n  } else {\n    if (path.parentPath.node[path.key] !== path.node) return true;\n  }\n  return isRemoved(path.parentPath);\n}\n\nexport default (callProvider: CallProvider) => {\n  function property(object, key, placement, path) {\n    return callProvider({ kind: \"property\", object, key, placement }, path);\n  }\n\n  function handleReferencedIdentifier(path) {\n    const {\n      node: { name },\n      scope,\n    } = path;\n    if (scope.getBindingIdentifier(name)) return;\n\n    callProvider({ kind: \"global\", name }, path);\n  }\n\n  function analyzeMemberExpression(\n    path: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  ) {\n    const key = resolveKey(path.get(\"property\"), path.node.computed);\n    return { key, handleAsMemberExpression: !!key && key !== \"prototype\" };\n  }\n\n  return {\n    // Symbol(), new Promise\n    ReferencedIdentifier(path: NodePath<t.Identifier>) {\n      const { parentPath } = path;\n      if (\n        parentPath.isMemberExpression({ object: path.node }) &&\n        analyzeMemberExpression(parentPath).handleAsMemberExpression\n      ) {\n        return;\n      }\n      handleReferencedIdentifier(path);\n    },\n\n    \"MemberExpression|OptionalMemberExpression\"(\n      path: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n    ) {\n      const { key, handleAsMemberExpression } = analyzeMemberExpression(path);\n      if (!handleAsMemberExpression) return;\n\n      const object = path.get(\"object\");\n      let objectIsGlobalIdentifier = object.isIdentifier();\n      if (objectIsGlobalIdentifier) {\n        const binding = object.scope.getBinding(\n          (object.node as t.Identifier).name,\n        );\n        if (binding) {\n          if (binding.path.isImportNamespaceSpecifier()) return;\n          objectIsGlobalIdentifier = false;\n        }\n      }\n\n      const source = resolveSource(object);\n      let skipObject = property(source.id, key, source.placement, path);\n      skipObject ||=\n        !objectIsGlobalIdentifier ||\n        path.shouldSkip ||\n        object.shouldSkip ||\n        isRemoved(object);\n\n      if (!skipObject) handleReferencedIdentifier(object);\n    },\n\n    ObjectPattern(path: NodePath<t.ObjectPattern>) {\n      const { parentPath, parent } = path;\n      let obj;\n\n      // const { keys, values } = Object\n      if (parentPath.isVariableDeclarator()) {\n        obj = parentPath.get(\"init\");\n        // ({ keys, values } = Object)\n      } else if (parentPath.isAssignmentExpression()) {\n        obj = parentPath.get(\"right\");\n        // !function ({ keys, values }) {...} (Object)\n        // resolution does not work after properties transform :-(\n      } else if (parentPath.isFunction()) {\n        const grand = parentPath.parentPath;\n        if (grand.isCallExpression() || grand.isNewExpression()) {\n          if (grand.node.callee === parent) {\n            obj = grand.get(\"arguments\")[path.key];\n          }\n        }\n      }\n\n      let id = null;\n      let placement = null;\n      if (obj) ({ id, placement } = resolveSource(obj));\n\n      for (const prop of path.get(\"properties\")) {\n        if (prop.isObjectProperty()) {\n          const key = resolveKey(prop.get(\"key\"));\n          if (key) property(id, key, placement, prop);\n        }\n      }\n    },\n\n    BinaryExpression(path: NodePath<t.BinaryExpression>) {\n      if (path.node.operator !== \"in\") return;\n\n      const source = resolveSource(path.get(\"right\"));\n      const key = resolveKey(path.get(\"left\"), true);\n\n      if (!key) return;\n\n      callProvider(\n        {\n          kind: \"in\",\n          object: source.id,\n          key,\n          placement: source.placement,\n        },\n        path,\n      );\n    },\n  };\n};\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { CallProvider } from \"./index\";\n\nimport { getImportSource, getRequireSource } from \"../utils\";\n\nexport default (callProvider: CallProvider) => ({\n  ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n    const source = getImportSource(path);\n    if (!source) return;\n    callProvider({ kind: \"import\", source }, path);\n  },\n  Program(path: NodePath<t.Program>) {\n    path.get(\"body\").forEach(bodyPath => {\n      const source = getRequireSource(bodyPath);\n      if (!source) return;\n      callProvider({ kind: \"import\", source }, bodyPath);\n    });\n  },\n});\n", "import path from \"path\";\nimport debounce from \"lodash.debounce\";\nimport requireResolve from \"resolve\";\n\nconst nativeRequireResolve = parseFloat(process.versions.node) >= 8.9;\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import /*::(_)*/.meta.url); // eslint-disable-line\n\nfunction myResolve(name: string, basedir: string) {\n  if (nativeRequireResolve) {\n    return require\n      .resolve(name, {\n        paths: [basedir],\n      })\n      .replace(/\\\\/g, \"/\");\n  } else {\n    return requireResolve.sync(name, { basedir }).replace(/\\\\/g, \"/\");\n  }\n}\n\nexport function resolve(\n  dirname: string,\n  moduleName: string,\n  absoluteImports: boolean | string,\n): string {\n  if (absoluteImports === false) return moduleName;\n\n  let basedir = dirname;\n  if (typeof absoluteImports === \"string\") {\n    basedir = path.resolve(basedir, absoluteImports);\n  }\n\n  try {\n    return myResolve(moduleName, basedir);\n  } catch (err) {\n    if (err.code !== \"MODULE_NOT_FOUND\") throw err;\n\n    throw Object.assign(\n      new Error(`Failed to resolve \"${moduleName}\" relative to \"${dirname}\"`),\n      {\n        code: \"BABEL_POLYFILL_NOT_FOUND\",\n        polyfill: moduleName,\n        dirname,\n      },\n    );\n  }\n}\n\nexport function has(basedir: string, name: string) {\n  try {\n    myResolve(name, basedir);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function logMissing(missingDeps: Set<string>) {\n  if (missingDeps.size === 0) return;\n\n  const deps = Array.from(missingDeps).sort().join(\" \");\n\n  console.warn(\n    \"\\nSome polyfills have been added but are not present in your dependencies.\\n\" +\n      \"Please run one of the following commands:\\n\" +\n      `\\tnpm install --save ${deps}\\n` +\n      `\\tyarn add ${deps}\\n`,\n  );\n\n  process.exitCode = 1;\n}\n\nlet allMissingDeps = new Set<string>();\n\nconst laterLogMissingDependencies = debounce(() => {\n  logMissing(allMissingDeps);\n  allMissingDeps = new Set<string>();\n}, 100);\n\nexport function laterLogMissing(missingDeps: Set<string>) {\n  if (missingDeps.size === 0) return;\n\n  missingDeps.forEach(name => allMissingDeps.add(name));\n  laterLogMissingDependencies();\n}\n", "import type {\n  MetaDescriptor,\n  ResolverPolyfills,\n  ResolvedPolyfill,\n} from \"./types\";\n\nimport { has } from \"./utils\";\n\ntype ResolverFn<T> = (meta: MetaDescriptor) => void | ResolvedPolyfill<T>;\n\nconst PossibleGlobalObjects = new Set<string>([\n  \"global\",\n  \"globalThis\",\n  \"self\",\n  \"window\",\n]);\n\nexport default function createMetaResolver<T>(\n  polyfills: ResolverPolyfills<T>,\n): ResolverFn<T> {\n  const { static: staticP, instance: instanceP, global: globalP } = polyfills;\n\n  return meta => {\n    if (meta.kind === \"global\" && globalP && has(globalP, meta.name)) {\n      return { kind: \"global\", desc: globalP[meta.name], name: meta.name };\n    }\n\n    if (meta.kind === \"property\" || meta.kind === \"in\") {\n      const { placement, object, key } = meta;\n\n      if (object && placement === \"static\") {\n        if (globalP && PossibleGlobalObjects.has(object) && has(globalP, key)) {\n          return { kind: \"global\", desc: globalP[key], name: key };\n        }\n\n        if (staticP && has(staticP, object) && has(staticP[object], key)) {\n          return {\n            kind: \"static\",\n            desc: staticP[object][key],\n            name: `${object}$${key}`,\n          };\n        }\n      }\n\n      if (instanceP && has(instanceP, key)) {\n        return { kind: \"instance\", desc: instanceP[key], name: `${key}` };\n      }\n    }\n  };\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport _getTargets, {\n  isRequired,\n  getInclusionReasons,\n} from \"@babel/helper-compilation-targets\";\nconst getTargets = _getTargets.default || _getTargets;\n\nimport { createUtilsGetter } from \"./utils\";\nimport ImportsCachedInjector from \"./imports-injector\";\nimport {\n  stringifyTargetsMultiline,\n  presetEnvSilentDebugHeader,\n} from \"./debug-utils\";\nimport {\n  validateIncludeExclude,\n  applyMissingDependenciesDefaults,\n} from \"./normalize-options\";\n\nimport type {\n  ProviderApi,\n  MethodString,\n  Targets,\n  MetaDescriptor,\n  PolyfillProvider,\n  PluginOptions,\n  ProviderOptions,\n} from \"./types\";\n\nimport * as v from \"./visitors\";\nimport * as deps from \"./node/dependencies\";\n\nimport createMetaResolver from \"./meta-resolver\";\n\nexport type { PolyfillProvider, MetaDescriptor, Utils, Targets } from \"./types\";\n\nfunction resolveOptions<Options>(\n  options: PluginOptions,\n  babelApi,\n): {\n  method: MethodString;\n  methodName: \"usageGlobal\" | \"entryGlobal\" | \"usagePure\";\n  targets: Targets;\n  debug: boolean | typeof presetEnvSilentDebugHeader;\n  shouldInjectPolyfill:\n    | ((name: string, shouldInject: boolean) => boolean)\n    | undefined;\n  providerOptions: ProviderOptions<Options>;\n  absoluteImports: string | boolean;\n} {\n  const {\n    method,\n    targets: targetsOption,\n    ignoreBrowserslistConfig,\n    configPath,\n    debug,\n    shouldInjectPolyfill,\n    absoluteImports,\n    ...providerOptions\n  } = options;\n\n  if (isEmpty(options)) {\n    throw new Error(\n      `\\\nThis plugin requires options, for example:\n    {\n      \"plugins\": [\n        [\"<plugin name>\", { method: \"usage-pure\" }]\n      ]\n    }\n\nSee more options at https://github.com/babel/babel-polyfills/blob/main/docs/usage.md`,\n    );\n  }\n\n  let methodName;\n  if (method === \"usage-global\") methodName = \"usageGlobal\";\n  else if (method === \"entry-global\") methodName = \"entryGlobal\";\n  else if (method === \"usage-pure\") methodName = \"usagePure\";\n  else if (typeof method !== \"string\") {\n    throw new Error(\".method must be a string\");\n  } else {\n    throw new Error(\n      `.method must be one of \"entry-global\", \"usage-global\"` +\n        ` or \"usage-pure\" (received ${JSON.stringify(method)})`,\n    );\n  }\n\n  if (typeof shouldInjectPolyfill === \"function\") {\n    if (options.include || options.exclude) {\n      throw new Error(\n        `.include and .exclude are not supported when using the` +\n          ` .shouldInjectPolyfill function.`,\n      );\n    }\n  } else if (shouldInjectPolyfill != null) {\n    throw new Error(\n      `.shouldInjectPolyfill must be a function, or undefined` +\n        ` (received ${JSON.stringify(shouldInjectPolyfill)})`,\n    );\n  }\n\n  if (\n    absoluteImports != null &&\n    typeof absoluteImports !== \"boolean\" &&\n    typeof absoluteImports !== \"string\"\n  ) {\n    throw new Error(\n      `.absoluteImports must be a boolean, a string, or undefined` +\n        ` (received ${JSON.stringify(absoluteImports)})`,\n    );\n  }\n\n  let targets;\n\n  if (\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    targetsOption ||\n    configPath ||\n    ignoreBrowserslistConfig\n  ) {\n    const targetsObj =\n      typeof targetsOption === \"string\" || Array.isArray(targetsOption)\n        ? { browsers: targetsOption }\n        : targetsOption;\n\n    targets = getTargets(targetsObj, {\n      ignoreBrowserslistConfig,\n      configPath,\n    });\n  } else {\n    targets = babelApi.targets();\n  }\n\n  return {\n    method,\n    methodName,\n    targets,\n    absoluteImports: absoluteImports ?? false,\n    shouldInjectPolyfill,\n    debug: !!debug,\n    providerOptions: providerOptions as any as ProviderOptions<Options>,\n  };\n}\n\nfunction instantiateProvider<Options>(\n  factory: PolyfillProvider<Options>,\n  options: PluginOptions,\n  missingDependencies,\n  dirname,\n  debugLog,\n  babelApi,\n) {\n  const {\n    method,\n    methodName,\n    targets,\n    debug,\n    shouldInjectPolyfill,\n    providerOptions,\n    absoluteImports,\n  } = resolveOptions<Options>(options, babelApi);\n\n  // eslint-disable-next-line prefer-const\n  let include, exclude;\n  let polyfillsSupport;\n  let polyfillsNames: Map<string, number> | undefined;\n  let filterPolyfills;\n\n  const getUtils = createUtilsGetter(\n    new ImportsCachedInjector(\n      moduleName => deps.resolve(dirname, moduleName, absoluteImports),\n      (name: string) => polyfillsNames?.get(name) ?? Infinity,\n    ),\n  );\n\n  const depsCache = new Map();\n\n  const api: ProviderApi = {\n    babel: babelApi,\n    getUtils,\n    method: options.method,\n    targets,\n    createMetaResolver,\n    shouldInjectPolyfill(name) {\n      if (polyfillsNames === undefined) {\n        throw new Error(\n          `Internal error in the ${factory.name} provider: ` +\n            `shouldInjectPolyfill() can't be called during initialization.`,\n        );\n      }\n      if (!polyfillsNames.has(name)) {\n        console.warn(\n          `Internal error in the ${providerName} provider: ` +\n            `unknown polyfill \"${name}\".`,\n        );\n      }\n\n      if (filterPolyfills && !filterPolyfills(name)) return false;\n\n      let shouldInject = isRequired(name, targets, {\n        compatData: polyfillsSupport,\n        includes: include,\n        excludes: exclude,\n      });\n\n      if (shouldInjectPolyfill) {\n        shouldInject = shouldInjectPolyfill(name, shouldInject);\n        if (typeof shouldInject !== \"boolean\") {\n          throw new Error(`.shouldInjectPolyfill must return a boolean.`);\n        }\n      }\n\n      return shouldInject;\n    },\n    debug(name) {\n      debugLog().found = true;\n\n      if (!debug || !name) return;\n\n      if (debugLog().polyfills.has(providerName)) return;\n      debugLog().polyfills.add(name);\n      debugLog().polyfillsSupport ??= polyfillsSupport;\n    },\n    assertDependency(name, version = \"*\") {\n      if (missingDependencies === false) return;\n      if (absoluteImports) {\n        // If absoluteImports is not false, we will try resolving\n        // the dependency and throw if it's not possible. We can\n        // skip the check here.\n        return;\n      }\n\n      const dep = version === \"*\" ? name : `${name}@^${version}`;\n\n      const found = missingDependencies.all\n        ? false\n        : mapGetOr(depsCache, `${name} :: ${dirname}`, () =>\n            deps.has(dirname, name),\n          );\n\n      if (!found) {\n        debugLog().missingDeps.add(dep);\n      }\n    },\n  };\n\n  const provider = factory(api, providerOptions, dirname);\n  const providerName = provider.name || factory.name;\n\n  if (typeof provider[methodName] !== \"function\") {\n    throw new Error(\n      `The \"${providerName}\" provider doesn't support the \"${method}\" polyfilling method.`,\n    );\n  }\n\n  if (Array.isArray(provider.polyfills)) {\n    polyfillsNames = new Map(\n      provider.polyfills.map((name, index) => [name, index]),\n    );\n    filterPolyfills = provider.filterPolyfills;\n  } else if (provider.polyfills) {\n    polyfillsNames = new Map(\n      Object.keys(provider.polyfills).map((name, index) => [name, index]),\n    );\n    polyfillsSupport = provider.polyfills;\n    filterPolyfills = provider.filterPolyfills;\n  } else {\n    polyfillsNames = new Map();\n  }\n\n  ({ include, exclude } = validateIncludeExclude(\n    providerName,\n    polyfillsNames,\n    providerOptions.include || [],\n    providerOptions.exclude || [],\n  ));\n\n  let callProvider: (payload: MetaDescriptor, path: NodePath) => boolean;\n  if (methodName === \"usageGlobal\") {\n    callProvider = (payload, path) => {\n      const utils = getUtils(path);\n      return (\n        (provider[methodName](payload, utils, path) satisfies boolean) ?? false\n      );\n    };\n  } else {\n    callProvider = (payload, path) => {\n      const utils = getUtils(path);\n      provider[methodName](payload, utils, path) satisfies void;\n      return false;\n    };\n  }\n\n  return {\n    debug,\n    method,\n    targets,\n    provider,\n    providerName,\n    callProvider,\n  };\n}\n\nexport default function definePolyfillProvider<Options>(\n  factory: PolyfillProvider<Options>,\n) {\n  return declare((babelApi, options: PluginOptions, dirname: string) => {\n    babelApi.assertVersion(\"^7.0.0 || ^8.0.0-alpha.0\");\n    const { traverse } = babelApi;\n\n    let debugLog;\n\n    const missingDependencies = applyMissingDependenciesDefaults(\n      options,\n      babelApi,\n    );\n\n    const { debug, method, targets, provider, providerName, callProvider } =\n      instantiateProvider<Options>(\n        factory,\n        options,\n        missingDependencies,\n        dirname,\n        () => debugLog,\n        babelApi,\n      );\n\n    const createVisitor = method === \"entry-global\" ? v.entry : v.usage;\n\n    const visitor = provider.visitor\n      ? traverse.visitors.merge([createVisitor(callProvider), provider.visitor])\n      : createVisitor(callProvider);\n\n    if (debug && debug !== presetEnvSilentDebugHeader) {\n      console.log(`${providerName}: \\`DEBUG\\` option`);\n      console.log(`\\nUsing targets: ${stringifyTargetsMultiline(targets)}`);\n      console.log(`\\nUsing polyfills with \\`${method}\\` method:`);\n    }\n\n    const { runtimeName } = provider;\n\n    return {\n      name: \"inject-polyfills\",\n      visitor,\n\n      pre(file) {\n        if (runtimeName) {\n          if (\n            file.get(\"runtimeHelpersModuleName\") &&\n            file.get(\"runtimeHelpersModuleName\") !== runtimeName\n          ) {\n            console.warn(\n              `Two different polyfill providers` +\n                ` (${file.get(\"runtimeHelpersModuleProvider\")}` +\n                ` and ${providerName}) are trying to define two` +\n                ` conflicting @babel/runtime alternatives:` +\n                ` ${file.get(\"runtimeHelpersModuleName\")} and ${runtimeName}.` +\n                ` The second one will be ignored.`,\n            );\n          } else {\n            file.set(\"runtimeHelpersModuleName\", runtimeName);\n            file.set(\"runtimeHelpersModuleProvider\", providerName);\n          }\n        }\n\n        debugLog = {\n          polyfills: new Set(),\n          polyfillsSupport: undefined,\n          found: false,\n          providers: new Set(),\n          missingDeps: new Set(),\n        };\n\n        provider.pre?.apply(this, arguments);\n      },\n      post() {\n        provider.post?.apply(this, arguments);\n\n        if (missingDependencies !== false) {\n          if (missingDependencies.log === \"per-file\") {\n            deps.logMissing(debugLog.missingDeps);\n          } else {\n            deps.laterLogMissing(debugLog.missingDeps);\n          }\n        }\n\n        if (!debug) return;\n\n        if (this.filename) console.log(`\\n[${this.filename}]`);\n\n        if (debugLog.polyfills.size === 0) {\n          console.log(\n            method === \"entry-global\"\n              ? debugLog.found\n                ? `Based on your targets, the ${providerName} polyfill did not add any polyfill.`\n                : `The entry point for the ${providerName} polyfill has not been found.`\n              : `Based on your code and targets, the ${providerName} polyfill did not add any polyfill.`,\n          );\n\n          return;\n        }\n\n        if (method === \"entry-global\") {\n          console.log(\n            `The ${providerName} polyfill entry has been replaced with ` +\n              `the following polyfills:`,\n          );\n        } else {\n          console.log(\n            `The ${providerName} polyfill added the following polyfills:`,\n          );\n        }\n\n        for (const name of debugLog.polyfills) {\n          if (debugLog.polyfillsSupport?.[name]) {\n            const filteredTargets = getInclusionReasons(\n              name,\n              targets,\n              debugLog.polyfillsSupport,\n            );\n\n            const formattedTargets = JSON.stringify(filteredTargets)\n              .replace(/,/g, \", \")\n              .replace(/^\\{\"/, '{ \"')\n              .replace(/\"\\}$/, '\" }');\n\n            console.log(`  ${name} ${formattedTargets}`);\n          } else {\n            console.log(`  ${name}`);\n          }\n        }\n      },\n    };\n  });\n}\n\nfunction mapGetOr(map, key, getDefault) {\n  let val = map.get(key);\n  if (val === undefined) {\n    val = getDefault();\n    map.set(key, val);\n  }\n  return val;\n}\n\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n"], "names": ["types", "t", "template", "_babel", "default", "intersection", "a", "b", "result", "Set", "for<PERSON>ach", "v", "has", "add", "object", "key", "Object", "prototype", "hasOwnProperty", "call", "resolve", "path", "resolved", "isVariableDeclarator", "get", "isIdentifier", "isReferencedIdentifier", "binding", "scope", "getBinding", "node", "name", "constant", "resolveId", "hasBinding", "<PERSON><PERSON><PERSON>", "computed", "isStringLiteral", "value", "parent", "isMemberExpression", "sym", "isPure", "evaluate", "resolveSource", "obj", "id", "placement", "type", "getImportSource", "specifiers", "length", "source", "getRequireSource", "isExpressionStatement", "expression", "isCallExpression", "callee", "arguments", "hoist", "_blockHoist", "createUtilsGetter", "cache", "prog", "findParent", "p", "isProgram", "injectGlobalImport", "url", "moduleName", "storeAnonymous", "isScript", "statement", "ast", "importDeclaration", "injectNamedImport", "hint", "storeNamed", "generateUidIdentifier", "importSpecifier", "injectDefaultImport", "importDefaultSpecifier", "ImportsCachedInjector", "constructor", "resolver", "getPreferredIndex", "_imports", "WeakMap", "_anonymousImports", "_lastImports", "_resolver", "_getPreferredIndex", "programPath", "getVal", "_normalizeKey", "imports", "_ensure", "sourceType", "stringLiteral", "_injectImport", "Map", "identifier", "set", "_this$_lastImports$ge", "newIndex", "lastImports", "isPathStillValid", "container", "body", "last", "Infinity", "undefined", "i", "data", "entries", "index", "newPath", "insertBefore", "splice", "insertAfter", "push", "unshiftContainer", "map", "Collection", "collection", "presetEnvSilentDebugHeader", "stringifyTargetsMultiline", "targets", "JSON", "stringify", "prettifyTargets", "patternToRegExp", "pattern", "RegExp", "buildUnusedError", "label", "unused", "original", "String", "join", "buldDuplicatesError", "duplicates", "size", "Array", "from", "validateIncludeExclude", "provider", "polyfills", "includePatterns", "excludePatterns", "current", "filter", "regexp", "matched", "polyfill", "keys", "test", "include", "unusedInclude", "exclude", "unusedExclude", "Error", "applyMissingDependenciesDefaults", "options", "babelApi", "missingDependencies", "caller", "log", "inject", "all", "isRemoved", "removed", "parentPath", "<PERSON><PERSON><PERSON>", "_path$parentPath$node", "includes", "callProvider", "property", "kind", "handleReferencedIdentifier", "getBindingIdentifier", "analyzeMemberExpression", "handleAsMemberExpression", "ReferencedIdentifier", "MemberExpression|OptionalMemberExpression", "objectIsGlobalIdentifier", "isImportNamespaceSpecifier", "skipObject", "shouldSkip", "ObjectPattern", "isAssignmentExpression", "isFunction", "grand", "isNewExpression", "prop", "isObjectProperty", "BinaryExpression", "operator", "ImportDeclaration", "Program", "bodyPath", "nativeRequireResolve", "parseFloat", "process", "versions", "require", "createRequire", "import", "meta", "myResolve", "basedir", "paths", "replace", "requireResolve", "sync", "dirname", "absoluteImports", "err", "code", "assign", "logMissing", "missingDeps", "deps", "sort", "console", "warn", "exitCode", "allMissingDeps", "laterLogMissingDependencies", "debounce", "laterLog<PERSON><PERSON>ing", "PossibleGlobalObjects", "createMetaResolver", "static", "staticP", "instance", "instanceP", "global", "globalP", "desc", "getTargets", "_getTargets", "resolveOptions", "method", "targetsOption", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "debug", "shouldInjectPolyfill", "providerOptions", "isEmpty", "methodName", "targetsObj", "isArray", "browsers", "instantiateProvider", "factory", "debugLog", "polyfillsSupport", "polyfillsNames", "filterPolyfills", "getUtils", "_polyfillsNames$get", "_polyfillsNames", "deps<PERSON>ache", "api", "babel", "providerName", "shouldInject", "isRequired", "compatData", "excludes", "_debugLog", "_debugLog$polyfillsSu", "found", "assertDependency", "version", "dep", "mapGetOr", "payload", "_ref", "utils", "definePolyfillProvider", "declare", "assertVersion", "traverse", "createVisitor", "visitor", "visitors", "merge", "runtimeName", "pre", "file", "_provider$pre", "providers", "apply", "post", "_provider$post", "filename", "_debugLog$polyfillsSu2", "filteredTargets", "getInclusionReasons", "formattedTargets", "getDefault", "val"], "mappings": ";;;;;;;;;EAASA,KAAK,EAAIC,GAAC;EAAEC,QAAQ,EAARA;AAAQ,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAKtB,SAASE,YAAYA,CAAIC,CAAS,EAAEC,CAAS,EAAU;EAC5D,MAAMC,MAAM,GAAG,IAAIC,GAAG,EAAK;EAC3BH,CAAC,CAACI,OAAO,CAACC,CAAC,IAAIJ,CAAC,CAACK,GAAG,CAACD,CAAC,CAAC,IAAIH,MAAM,CAACK,GAAG,CAACF,CAAC,CAAC,CAAC;EACzC,OAAOH,MAAM;AACf;AAEO,SAASI,KAAGA,CAACE,MAAW,EAAEC,GAAW,EAAE;EAC5C,OAAOC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEC,GAAG,CAAC;AAC1D;AAEA,SAASK,SAAOA,CACdC,IAAc,EACdC,QAAuB,GAAG,IAAIb,GAAG,EAAE,EACb;EACtB,IAAIa,QAAQ,CAACV,GAAG,CAACS,IAAI,CAAC,EAAE;EACxBC,QAAQ,CAACT,GAAG,CAACQ,IAAI,CAAC;EAElB,IAAIA,IAAI,CAACE,oBAAoB,EAAE,EAAE;IAC/B,IAAIF,IAAI,CAACG,GAAG,CAAC,IAAI,CAAC,CAACC,YAAY,EAAE,EAAE;MACjC,OAAOL,SAAO,CAACC,IAAI,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEF,QAAQ,CAAC;;GAE7C,MAAM,IAAID,IAAI,CAACK,sBAAsB,EAAE,EAAE;IACxC,MAAMC,OAAO,GAAGN,IAAI,CAACO,KAAK,CAACC,UAAU,CAACR,IAAI,CAACS,IAAI,CAACC,IAAI,CAAC;IACrD,IAAI,CAACJ,OAAO,EAAE,OAAON,IAAI;IACzB,IAAI,CAACM,OAAO,CAACK,QAAQ,EAAE;IACvB,OAAOZ,SAAO,CAACO,OAAO,CAACN,IAAI,EAAEC,QAAQ,CAAC;;EAExC,OAAOD,IAAI;AACb;AAEA,SAASY,SAASA,CAACZ,IAAc,EAAU;EACzC,IACEA,IAAI,CAACI,YAAY,EAAE,IACnB,CAACJ,IAAI,CAACO,KAAK,CAACM,UAAU,CAACb,IAAI,CAACS,IAAI,CAACC,IAAI,iBAAkB,IAAI,CAAC,EAC5D;IACA,OAAOV,IAAI,CAACS,IAAI,CAACC,IAAI;;EAGvB,MAAMT,QAAQ,GAAGF,SAAO,CAACC,IAAI,CAAC;EAC9B,IAAIC,QAAQ,YAARA,QAAQ,CAAEG,YAAY,EAAE,EAAE;IAC5B,OAAOH,QAAQ,CAACQ,IAAI,CAACC,IAAI;;AAE7B;AAEO,SAASI,UAAUA,CACxBd,IAA4C,EAC5Ce,QAAiB,GAAG,KAAK,EACzB;EACA,MAAM;IAAER;GAAO,GAAGP,IAAI;EACtB,IAAIA,IAAI,CAACgB,eAAe,EAAE,EAAE,OAAOhB,IAAI,CAACS,IAAI,CAACQ,KAAK;EAClD,MAAMb,YAAY,GAAGJ,IAAI,CAACI,YAAY,EAAE;EACxC,IACEA,YAAY,IACZ,EAAEW,QAAQ,IAAKf,IAAI,CAACkB,MAAM,CAAwBH,QAAQ,CAAC,EAC3D;IACA,OAAOf,IAAI,CAACS,IAAI,CAACC,IAAI;;EAGvB,IACEK,QAAQ,IACRf,IAAI,CAACmB,kBAAkB,EAAE,IACzBnB,IAAI,CAACG,GAAG,CAAC,QAAQ,CAAC,CAACC,YAAY,CAAC;IAAEM,IAAI,EAAE;GAAU,CAAC,IACnD,CAACH,KAAK,CAACM,UAAU,CAAC,QAAQ,iBAAkB,IAAI,CAAC,EACjD;IACA,MAAMO,GAAG,GAAGN,UAAU,CAACd,IAAI,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEH,IAAI,CAACS,IAAI,CAACM,QAAQ,CAAC;IAChE,IAAIK,GAAG,EAAE,OAAO,SAAS,GAAGA,GAAG;;EAGjC,IACEhB,YAAY,GACRG,KAAK,CAACM,UAAU,CAACb,IAAI,CAACS,IAAI,CAACC,IAAI,iBAAkB,IAAI,CAAC,GACtDV,IAAI,CAACqB,MAAM,EAAE,EACjB;IACA,MAAM;MAAEJ;KAAO,GAAGjB,IAAI,CAACsB,QAAQ,EAAE;IACjC,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;;AAE/C;AAEO,SAASM,aAAaA,CAACC,GAAa,EAGzC;EACA,IACEA,GAAG,CAACL,kBAAkB,EAAE,IACxBK,GAAG,CAACrB,GAAG,CAAC,UAAU,CAAC,CAACC,YAAY,CAAC;IAAEM,IAAI,EAAE;GAAa,CAAC,EACvD;IACA,MAAMe,EAAE,GAAGb,SAAS,CAACY,GAAG,CAACrB,GAAG,CAAC,QAAQ,CAAC,CAAC;IAEvC,IAAIsB,EAAE,EAAE;MACN,OAAO;QAAEA,EAAE;QAAEC,SAAS,EAAE;OAAa;;IAEvC,OAAO;MAAED,EAAE,EAAE,IAAI;MAAEC,SAAS,EAAE;KAAM;;EAGtC,MAAMD,EAAE,GAAGb,SAAS,CAACY,GAAG,CAAC;EACzB,IAAIC,EAAE,EAAE;IACN,OAAO;MAAEA,EAAE;MAAEC,SAAS,EAAE;KAAU;;EAGpC,MAAM1B,IAAI,GAAGD,SAAO,CAACyB,GAAG,CAAC;EACzB,QAAQxB,IAAI,oBAAJA,IAAI,CAAE2B,IAAI;IAChB,KAAK,eAAe;MAClB,OAAO;QAAEF,EAAE,EAAE,QAAQ;QAAEC,SAAS,EAAE;OAAa;IACjD,KAAK,oBAAoB;MACvB,OAAO;QAAED,EAAE,EAAE,UAAU;QAAEC,SAAS,EAAE;OAAa;IACnD,KAAK,eAAe;MAClB,OAAO;QAAED,EAAE,EAAE,QAAQ;QAAEC,SAAS,EAAE;OAAa;IACjD,KAAK,eAAe;MAClB,OAAO;QAAED,EAAE,EAAE,QAAQ;QAAEC,SAAS,EAAE;OAAa;IACjD,KAAK,gBAAgB;MACnB,OAAO;QAAED,EAAE,EAAE,SAAS;QAAEC,SAAS,EAAE;OAAa;IAClD,KAAK,kBAAkB;MACrB,OAAO;QAAED,EAAE,EAAE,QAAQ;QAAEC,SAAS,EAAE;OAAa;IACjD,KAAK,iBAAiB;MACpB,OAAO;QAAED,EAAE,EAAE,OAAO;QAAEC,SAAS,EAAE;OAAa;;EAGlD,OAAO;IAAED,EAAE,EAAE,IAAI;IAAEC,SAAS,EAAE;GAAM;AACtC;AAEO,SAASE,eAAeA,CAAC;EAAEnB;AAAoC,CAAC,EAAE;EACvE,IAAIA,IAAI,CAACoB,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOrB,IAAI,CAACsB,MAAM,CAACd,KAAK;AAC5D;AAEO,SAASe,gBAAgBA,CAAC;EAAEvB;AAA4B,CAAC,EAAE;EAChE,IAAI,CAAC7B,GAAC,CAACqD,qBAAqB,CAACxB,IAAI,CAAC,EAAE;EACpC,MAAM;IAAEyB;GAAY,GAAGzB,IAAI;EAC3B,IACE7B,GAAC,CAACuD,gBAAgB,CAACD,UAAU,CAAC,IAC9BtD,GAAC,CAACwB,YAAY,CAAC8B,UAAU,CAACE,MAAM,CAAC,IACjCF,UAAU,CAACE,MAAM,CAAC1B,IAAI,KAAK,SAAS,IACpCwB,UAAU,CAACG,SAAS,CAACP,MAAM,KAAK,CAAC,IACjClD,GAAC,CAACoC,eAAe,CAACkB,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,EAC1C;IACA,OAAOH,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAACpB,KAAK;;AAExC;AAEA,SAASqB,KAAKA,CAAC7B,IAAY,EAAE;;EAE3BA,IAAI,CAAC8B,WAAW,GAAG,CAAC;EACpB,OAAO9B,IAAI;AACb;AAEO,SAAS+B,iBAAiBA,CAACC,KAA4B,EAAE;EAC9D,OAAQzC,IAAc,IAAY;IAChC,MAAM0C,IAAI,GAAG1C,IAAI,CAAC2C,UAAU,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,EAAE,CAAwB;IAEvE,OAAO;MACLC,kBAAkBA,CAACC,GAAG,EAAEC,UAAU,EAAE;QAClCP,KAAK,CAACQ,cAAc,CAACP,IAAI,EAAEK,GAAG,EAAEC,UAAU,EAAE,CAACE,QAAQ,EAAEnB,MAAM,KAAK;UAChE,OAAOmB,QAAQ,GACXrE,QAAQ,CAACsE,SAAS,CAACC,GAAI,WAAUrB,MAAO,GAAE,GAC1CnD,GAAC,CAACyE,iBAAiB,CAAC,EAAE,EAAEtB,MAAM,CAAC;SACpC,CAAC;OACH;MACDuB,iBAAiBA,CAACP,GAAG,EAAErC,IAAI,EAAE6C,IAAI,GAAG7C,IAAI,EAAEsC,UAAU,EAAE;QACpD,OAAOP,KAAK,CAACe,UAAU,CACrBd,IAAI,EACJK,GAAG,EACHrC,IAAI,EACJsC,UAAU,EACV,CAACE,QAAQ,EAAEnB,MAAM,EAAErB,IAAI,KAAK;UAC1B,MAAMe,EAAE,GAAGiB,IAAI,CAACnC,KAAK,CAACkD,qBAAqB,CAACF,IAAI,CAAC;UACjD,OAAO;YACL9C,IAAI,EAAEyC,QAAQ,GACVZ,KAAK,CAACzD,QAAQ,CAACsE,SAAS,CAACC,GAAI;AAC/C,wBAAwB3B,EAAG,cAAaM,MAAO,KAAIrB,IAAK;AACxD,iBAAiB,CAAC,GACA9B,GAAC,CAACyE,iBAAiB,CAAC,CAACzE,GAAC,CAAC8E,eAAe,CAACjC,EAAE,EAAEf,IAAI,CAAC,CAAC,EAAEqB,MAAM,CAAC;YAC9DrB,IAAI,EAAEe,EAAE,CAACf;WACV;SAEL,CAAC;OACF;MACDiD,mBAAmBA,CAACZ,GAAG,EAAEQ,IAAI,GAAGR,GAAG,EAAEC,UAAU,EAAE;QAC/C,OAAOP,KAAK,CAACe,UAAU,CACrBd,IAAI,EACJK,GAAG,EACH,SAAS,EACTC,UAAU,EACV,CAACE,QAAQ,EAAEnB,MAAM,KAAK;UACpB,MAAMN,EAAE,GAAGiB,IAAI,CAACnC,KAAK,CAACkD,qBAAqB,CAACF,IAAI,CAAC;UACjD,OAAO;YACL9C,IAAI,EAAEyC,QAAQ,GACVZ,KAAK,CAACzD,QAAQ,CAACsE,SAAS,CAACC,GAAI,OAAM3B,EAAG,cAAaM,MAAO,GAAE,CAAC,GAC7DnD,GAAC,CAACyE,iBAAiB,CAAC,CAACzE,GAAC,CAACgF,sBAAsB,CAACnC,EAAE,CAAC,CAAC,EAAEM,MAAM,CAAC;YAC/DrB,IAAI,EAAEe,EAAE,CAACf;WACV;SAEL,CAAC;;KAEJ;GACF;AACH;;;ECtMS/B,KAAK,EAAIC;AAAC,IAAAE,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAIJ,MAAM+E,qBAAqB,CAAC;EAUzCC,WAAWA,CACTC,QAAiC,EACjCC,iBAA0C,EAC1C;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIC,OAAO,EAAE;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAID,OAAO,EAAE;IACtC,IAAI,CAACE,YAAY,GAAG,IAAIF,OAAO,EAAE;IACjC,IAAI,CAACG,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,kBAAkB,GAAGN,iBAAiB;;EAG7Cf,cAAcA,CACZsB,WAAgC,EAChCxB,GAAW,EACXC,UAAkB,EAClBwB,MAA8D,EAC9D;IACA,MAAM9E,GAAG,GAAG,IAAI,CAAC+E,aAAa,CAACF,WAAW,EAAExB,GAAG,CAAC;IAChD,MAAM2B,OAAO,GAAG,IAAI,CAACC,OAAO,CAC1B,IAAI,CAACR,iBAAiB,EACtBI,WAAW,EACXnF,GACF,CAAC;IAED,IAAIsF,OAAO,CAACnF,GAAG,CAACG,GAAG,CAAC,EAAE;IAEtB,MAAMe,IAAI,GAAG+D,MAAM,CACjBD,WAAW,CAAC9D,IAAI,CAACmE,UAAU,KAAK,QAAQ,EACxChG,CAAC,CAACiG,aAAa,CAAC,IAAI,CAACR,SAAS,CAACtB,GAAG,CAAC,CACrC,CAAC;IACD2B,OAAO,CAAClF,GAAG,CAACE,GAAG,CAAC;IAChB,IAAI,CAACoF,aAAa,CAACP,WAAW,EAAE9D,IAAI,EAAEuC,UAAU,CAAC;;EAGnDQ,UAAUA,CACRe,WAAgC,EAChCxB,GAAW,EACXrC,IAAY,EACZsC,UAAkB,EAClBwB,MAMmC,EACnC;IACA,MAAM9E,GAAG,GAAG,IAAI,CAAC+E,aAAa,CAACF,WAAW,EAAExB,GAAG,EAAErC,IAAI,CAAC;IACtD,MAAMgE,OAAO,GAAG,IAAI,CAACC,OAAO,CAC1B,IAAI,CAACV,QAAQ,EACbM,WAAW,EACXQ,GACF,CAAC;IAED,IAAI,CAACL,OAAO,CAACnF,GAAG,CAACG,GAAG,CAAC,EAAE;MACrB,MAAM;QAAEe,IAAI;QAAEC,IAAI,EAAEe;OAAI,GAAG+C,MAAM,CAC/BD,WAAW,CAAC9D,IAAI,CAACmE,UAAU,KAAK,QAAQ,EACxChG,CAAC,CAACiG,aAAa,CAAC,IAAI,CAACR,SAAS,CAACtB,GAAG,CAAC,CAAC,EACpCnE,CAAC,CAACoG,UAAU,CAACtE,IAAI,CACnB,CAAC;MACDgE,OAAO,CAACO,GAAG,CAACvF,GAAG,EAAE+B,EAAE,CAAC;MACpB,IAAI,CAACqD,aAAa,CAACP,WAAW,EAAE9D,IAAI,EAAEuC,UAAU,CAAC;;IAGnD,OAAOpE,CAAC,CAACoG,UAAU,CAACN,OAAO,CAACvE,GAAG,CAACT,GAAG,CAAC,CAAC;;EAGvCoF,aAAaA,CACXP,WAAgC,EAChC9D,IAAY,EACZuC,UAAkB,EAClB;IAAA,IAAAkC,qBAAA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACb,kBAAkB,CAACtB,UAAU,CAAC;IACpD,MAAMoC,WAAW,IAAAF,qBAAA,GAAG,IAAI,CAACd,YAAY,CAACjE,GAAG,CAACoE,WAAW,CAAC,YAAAW,qBAAA,GAAI,EAAE;IAE5D,MAAMG,gBAAgB,GAAIrF,IAAc,IACtCA,IAAI,CAACS,IAAI;;;IAGTT,IAAI,CAACkB,MAAM,KAAKqD,WAAW,CAAC9D,IAAI,IAChCT,IAAI,CAACsF,SAAS,KAAKf,WAAW,CAAC9D,IAAI,CAAC8E,IAAI;IAE1C,IAAIC,IAAc;IAElB,IAAIL,QAAQ,KAAKM,QAAQ,EAAE;;MAEzB,IAAIL,WAAW,CAACtD,MAAM,GAAG,CAAC,EAAE;QAC1B0D,IAAI,GAAGJ,WAAW,CAACA,WAAW,CAACtD,MAAM,GAAG,CAAC,CAAC,CAAC9B,IAAI;QAC/C,IAAI,CAACqF,gBAAgB,CAACG,IAAI,CAAC,EAAEA,IAAI,GAAGE,SAAS;;KAEhD,MAAM;MACL,KAAK,MAAM,CAACC,CAAC,EAAEC,IAAI,CAAC,IAAIR,WAAW,CAACS,OAAO,EAAE,EAAE;QAC7C,MAAM;UAAE7F,IAAI;UAAE8F;SAAO,GAAGF,IAAI;QAC5B,IAAIP,gBAAgB,CAACrF,IAAI,CAAC,EAAE;UAC1B,IAAImF,QAAQ,GAAGW,KAAK,EAAE;YACpB,MAAM,CAACC,OAAO,CAAC,GAAG/F,IAAI,CAACgG,YAAY,CAACvF,IAAI,CAAC;YACzC2E,WAAW,CAACa,MAAM,CAACN,CAAC,EAAE,CAAC,EAAE;cAAE3F,IAAI,EAAE+F,OAAO;cAAED,KAAK,EAAEX;aAAU,CAAC;YAC5D;;UAEFK,IAAI,GAAGxF,IAAI;;;;IAKjB,IAAIwF,IAAI,EAAE;MACR,MAAM,CAACO,OAAO,CAAC,GAAGP,IAAI,CAACU,WAAW,CAACzF,IAAI,CAAC;MACxC2E,WAAW,CAACe,IAAI,CAAC;QAAEnG,IAAI,EAAE+F,OAAO;QAAED,KAAK,EAAEX;OAAU,CAAC;KACrD,MAAM;MACL,MAAM,CAACY,OAAO,CAAC,GAAGxB,WAAW,CAAC6B,gBAAgB,CAAC,MAAM,EAAE3F,IAAI,CAAC;MAC5D,IAAI,CAAC2D,YAAY,CAACa,GAAG,CAACV,WAAW,EAAE,CAAC;QAAEvE,IAAI,EAAE+F,OAAO;QAAED,KAAK,EAAEX;OAAU,CAAC,CAAC;;;EAI5ER,OAAOA,CACL0B,GAAoC,EACpC9B,WAAgC,EAChC+B,UAAqC,EAClC;IACH,IAAIC,UAAU,GAAGF,GAAG,CAAClG,GAAG,CAACoE,WAAW,CAAC;IACrC,IAAI,CAACgC,UAAU,EAAE;MACfA,UAAU,GAAG,IAAID,UAAU,EAAE;MAC7BD,GAAG,CAACpB,GAAG,CAACV,WAAW,EAAEgC,UAAU,CAAC;;IAElC,OAAOA,UAAU;;EAGnB9B,aAAaA,CACXF,WAAgC,EAChCxB,GAAW,EACXrC,IAAY,GAAG,EAAE,EACT;IACR,MAAM;MAAEkE;KAAY,GAAGL,WAAW,CAAC9D,IAAI;;;;;IAKvC,OAAQ,GAAEC,IAAI,IAAIkE,UAAW,KAAI7B,GAAI,KAAIrC,IAAK,EAAC;;AAEnD;;ACrJO,MAAM8F,0BAA0B,GACrC,+EAA+E;AAE1E,SAASC,yBAAyBA,CAACC,OAAgB,EAAU;EAClE,OAAOC,IAAI,CAACC,SAAS,CAACC,eAAe,CAACH,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D;;ACFA,SAASI,eAAeA,CAACC,OAAgB,EAAiB;EACxD,IAAIA,OAAO,YAAYC,MAAM,EAAE,OAAOD,OAAO;EAE7C,IAAI;IACF,OAAO,IAAIC,MAAM,CAAE,IAAGD,OAAQ,GAAE,CAAC;GAClC,CAAC,MAAM;IACN,OAAO,IAAI;;AAEf;AAEA,SAASE,gBAAgBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACvC,IAAI,CAACA,MAAM,CAACrF,MAAM,EAAE,OAAO,EAAE;EAC7B,OACG,sBAAqBoF,KAAM,yCAAwC,GACpEC,MAAM,CAACd,GAAG,CAACe,QAAQ,IAAK,OAAMC,MAAM,CAACD,QAAQ,CAAE,IAAG,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AAEhE;AAEA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,IAAI,CAACA,UAAU,CAACC,IAAI,EAAE,OAAO,EAAE;EAC/B,OACG,sFAAqF,GACtFC,KAAK,CAACC,IAAI,CAACH,UAAU,EAAE9G,IAAI,IAAK,OAAMA,IAAK,IAAG,CAAC,CAAC4G,IAAI,CAAC,EAAE,CAAC;AAE5D;AAEO,SAASM,sBAAsBA,CACpCC,QAAgB,EAChBC,SAA+B,EAC/BC,eAA0B,EAC1BC,eAA0B,EAC1B;EACA,IAAIC,OAAO;EACX,MAAMC,MAAM,GAAGnB,OAAO,IAAI;IACxB,MAAMoB,MAAM,GAAGrB,eAAe,CAACC,OAAO,CAAC;IACvC,IAAI,CAACoB,MAAM,EAAE,OAAO,KAAK;IAEzB,IAAIC,OAAO,GAAG,KAAK;IACnB,KAAK,MAAMC,QAAQ,IAAIP,SAAS,CAACQ,IAAI,EAAE,EAAE;MACvC,IAAIH,MAAM,CAACI,IAAI,CAACF,QAAQ,CAAC,EAAE;QACzBD,OAAO,GAAG,IAAI;QACdH,OAAO,CAACzI,GAAG,CAAC6I,QAAQ,CAAC;;;IAGzB,OAAO,CAACD,OAAO;GAChB;;;EAGD,MAAMI,OAAO,GAAGP,OAAO,GAAG,IAAI7I,GAAG,EAAW;EAC5C,MAAMqJ,aAAa,GAAGf,KAAK,CAACC,IAAI,CAACI,eAAe,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC;;;EAGhE,MAAMQ,OAAO,GAAGT,OAAO,GAAG,IAAI7I,GAAG,EAAW;EAC5C,MAAMuJ,aAAa,GAAGjB,KAAK,CAACC,IAAI,CAACK,eAAe,CAAC,CAACE,MAAM,CAACA,MAAM,CAAC;EAEhE,MAAMV,UAAU,GAAGxI,YAAY,CAACwJ,OAAO,EAAEE,OAAO,CAAC;EAEjD,IACElB,UAAU,CAACC,IAAI,GAAG,CAAC,IACnBgB,aAAa,CAAC3G,MAAM,GAAG,CAAC,IACxB6G,aAAa,CAAC7G,MAAM,GAAG,CAAC,EACxB;IACA,MAAM,IAAI8G,KAAK,CACZ,+BAA8Bf,QAAS,uBAAsB,GAC5DZ,gBAAgB,CAAC,SAAS,EAAEwB,aAAa,CAAC,GAC1CxB,gBAAgB,CAAC,SAAS,EAAE0B,aAAa,CAAC,GAC1CpB,mBAAmB,CAACC,UAAU,CAClC,CAAC;;EAGH,OAAO;IAAEgB,OAAO;IAAEE;GAAS;AAC7B;AAEO,SAASG,gCAAgCA,CAC9CC,OAAsB,EACtBC,QAAa,EACc;EAC3B,MAAM;IAAEC,mBAAmB,GAAG;GAAI,GAAGF,OAAO;EAC5C,IAAIE,mBAAmB,KAAK,KAAK,EAAE,OAAO,KAAK;EAE/C,MAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAACA,MAAM,IAAIA,MAAM,oBAANA,MAAM,CAAEvI,IAAI,CAAC;EAEtD,MAAM;IACJwI,GAAG,GAAG,UAAU;IAChBC,MAAM,GAAGF,MAAM,KAAK,qBAAqB,GAAG,OAAO,GAAG,QAAQ;IAC9DG,GAAG,GAAG;GACP,GAAGJ,mBAAmB;EAEvB,OAAO;IAAEE,GAAG;IAAEC,MAAM;IAAEC;GAAK;AAC7B;;AC1FA,SAASC,SAASA,CAACrJ,IAAc,EAAE;EACjC,IAAIA,IAAI,CAACsJ,OAAO,EAAE,OAAO,IAAI;EAC7B,IAAI,CAACtJ,IAAI,CAACuJ,UAAU,EAAE,OAAO,KAAK;EAClC,IAAIvJ,IAAI,CAACwJ,OAAO,EAAE;IAAA,IAAAC,qBAAA;IAChB,IAAI,GAAAA,qBAAA,GAACzJ,IAAI,CAACuJ,UAAU,CAAC9I,IAAI,cAAAgJ,qBAAA,GAApBA,qBAAA,CAAuBzJ,IAAI,CAACwJ,OAAO,CAAC,aAApCC,qBAAA,CAAsCC,QAAQ,CAAC1J,IAAI,CAACS,IAAI,CAAC,GAAE,OAAO,IAAI;GAC5E,MAAM;IACL,IAAIT,IAAI,CAACuJ,UAAU,CAAC9I,IAAI,CAACT,IAAI,CAACN,GAAG,CAAC,KAAKM,IAAI,CAACS,IAAI,EAAE,OAAO,IAAI;;EAE/D,OAAO4I,SAAS,CAACrJ,IAAI,CAACuJ,UAAU,CAAC;AACnC;AAEA,aAAgBI,YAA0B,IAAK;EAC7C,SAASC,QAAQA,CAACnK,MAAM,EAAEC,GAAG,EAAEgC,SAAS,EAAE1B,IAAI,EAAE;IAC9C,OAAO2J,YAAY,CAAC;MAAEE,IAAI,EAAE,UAAU;MAAEpK,MAAM;MAAEC,GAAG;MAAEgC;KAAW,EAAE1B,IAAI,CAAC;;EAGzE,SAAS8J,0BAA0BA,CAAC9J,IAAI,EAAE;IACxC,MAAM;MACJS,IAAI,EAAE;QAAEC;OAAM;MACdH;KACD,GAAGP,IAAI;IACR,IAAIO,KAAK,CAACwJ,oBAAoB,CAACrJ,IAAI,CAAC,EAAE;IAEtCiJ,YAAY,CAAC;MAAEE,IAAI,EAAE,QAAQ;MAAEnJ;KAAM,EAAEV,IAAI,CAAC;;EAG9C,SAASgK,uBAAuBA,CAC9BhK,IAA+D,EAC/D;IACA,MAAMN,GAAG,GAAGoB,UAAU,CAACd,IAAI,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEH,IAAI,CAACS,IAAI,CAACM,QAAQ,CAAC;IAChE,OAAO;MAAErB,GAAG;MAAEuK,wBAAwB,EAAE,CAAC,CAACvK,GAAG,IAAIA,GAAG,KAAK;KAAa;;EAGxE,OAAO;;IAELwK,oBAAoBA,CAAClK,IAA4B,EAAE;MACjD,MAAM;QAAEuJ;OAAY,GAAGvJ,IAAI;MAC3B,IACEuJ,UAAU,CAACpI,kBAAkB,CAAC;QAAE1B,MAAM,EAAEO,IAAI,CAACS;OAAM,CAAC,IACpDuJ,uBAAuB,CAACT,UAAU,CAAC,CAACU,wBAAwB,EAC5D;QACA;;MAEFH,0BAA0B,CAAC9J,IAAI,CAAC;KACjC;IAED,2CAA2CmK,CACzCnK,IAA+D,EAC/D;MACA,MAAM;QAAEN,GAAG;QAAEuK;OAA0B,GAAGD,uBAAuB,CAAChK,IAAI,CAAC;MACvE,IAAI,CAACiK,wBAAwB,EAAE;MAE/B,MAAMxK,MAAM,GAAGO,IAAI,CAACG,GAAG,CAAC,QAAQ,CAAC;MACjC,IAAIiK,wBAAwB,GAAG3K,MAAM,CAACW,YAAY,EAAE;MACpD,IAAIgK,wBAAwB,EAAE;QAC5B,MAAM9J,OAAO,GAAGb,MAAM,CAACc,KAAK,CAACC,UAAU,CACpCf,MAAM,CAACgB,IAAI,CAAkBC,IAChC,CAAC;QACD,IAAIJ,OAAO,EAAE;UACX,IAAIA,OAAO,CAACN,IAAI,CAACqK,0BAA0B,EAAE,EAAE;UAC/CD,wBAAwB,GAAG,KAAK;;;MAIpC,MAAMrI,MAAM,GAAGR,aAAa,CAAC9B,MAAM,CAAC;MACpC,IAAI6K,UAAU,GAAGV,QAAQ,CAAC7H,MAAM,CAACN,EAAE,EAAE/B,GAAG,EAAEqC,MAAM,CAACL,SAAS,EAAE1B,IAAI,CAAC;MACjEsK,UAAU,KAAVA,UAAU,GACR,CAACF,wBAAwB,IACzBpK,IAAI,CAACuK,UAAU,IACf9K,MAAM,CAAC8K,UAAU,IACjBlB,SAAS,CAAC5J,MAAM,CAAC;MAEnB,IAAI,CAAC6K,UAAU,EAAER,0BAA0B,CAACrK,MAAM,CAAC;KACpD;IAED+K,aAAaA,CAACxK,IAA+B,EAAE;MAC7C,MAAM;QAAEuJ,UAAU;QAAErI;OAAQ,GAAGlB,IAAI;MACnC,IAAIwB,GAAG;;;MAGP,IAAI+H,UAAU,CAACrJ,oBAAoB,EAAE,EAAE;QACrCsB,GAAG,GAAG+H,UAAU,CAACpJ,GAAG,CAAC,MAAM,CAAC;;OAE7B,MAAM,IAAIoJ,UAAU,CAACkB,sBAAsB,EAAE,EAAE;QAC9CjJ,GAAG,GAAG+H,UAAU,CAACpJ,GAAG,CAAC,OAAO,CAAC;;;OAG9B,MAAM,IAAIoJ,UAAU,CAACmB,UAAU,EAAE,EAAE;QAClC,MAAMC,KAAK,GAAGpB,UAAU,CAACA,UAAU;QACnC,IAAIoB,KAAK,CAACxI,gBAAgB,EAAE,IAAIwI,KAAK,CAACC,eAAe,EAAE,EAAE;UACvD,IAAID,KAAK,CAAClK,IAAI,CAAC2B,MAAM,KAAKlB,MAAM,EAAE;YAChCM,GAAG,GAAGmJ,KAAK,CAACxK,GAAG,CAAC,WAAW,CAAC,CAACH,IAAI,CAACN,GAAG,CAAC;;;;MAK5C,IAAI+B,EAAE,GAAG,IAAI;MACb,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIF,GAAG,EAAE,CAAC;QAAEC,EAAE;QAAEC;OAAW,GAAGH,aAAa,CAACC,GAAG,CAAC;MAEhD,KAAK,MAAMqJ,IAAI,IAAI7K,IAAI,CAACG,GAAG,CAAC,YAAY,CAAC,EAAE;QACzC,IAAI0K,IAAI,CAACC,gBAAgB,EAAE,EAAE;UAC3B,MAAMpL,GAAG,GAAGoB,UAAU,CAAC+J,IAAI,CAAC1K,GAAG,CAAC,KAAK,CAAC,CAAC;UACvC,IAAIT,GAAG,EAAEkK,QAAQ,CAACnI,EAAE,EAAE/B,GAAG,EAAEgC,SAAS,EAAEmJ,IAAI,CAAC;;;KAGhD;IAEDE,gBAAgBA,CAAC/K,IAAkC,EAAE;MACnD,IAAIA,IAAI,CAACS,IAAI,CAACuK,QAAQ,KAAK,IAAI,EAAE;MAEjC,MAAMjJ,MAAM,GAAGR,aAAa,CAACvB,IAAI,CAACG,GAAG,CAAC,OAAO,CAAC,CAAC;MAC/C,MAAMT,GAAG,GAAGoB,UAAU,CAACd,IAAI,CAACG,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;MAE9C,IAAI,CAACT,GAAG,EAAE;MAEViK,YAAY,CACV;QACEE,IAAI,EAAE,IAAI;QACVpK,MAAM,EAAEsC,MAAM,CAACN,EAAE;QACjB/B,GAAG;QACHgC,SAAS,EAAEK,MAAM,CAACL;OACnB,EACD1B,IACF,CAAC;;GAEJ;AACH,CAAC;;AC/HD,aAAgB2J,YAA0B,KAAM;EAC9CsB,iBAAiBA,CAACjL,IAAmC,EAAE;IACrD,MAAM+B,MAAM,GAAGH,eAAe,CAAC5B,IAAI,CAAC;IACpC,IAAI,CAAC+B,MAAM,EAAE;IACb4H,YAAY,CAAC;MAAEE,IAAI,EAAE,QAAQ;MAAE9H;KAAQ,EAAE/B,IAAI,CAAC;GAC/C;EACDkL,OAAOA,CAAClL,IAAyB,EAAE;IACjCA,IAAI,CAACG,GAAG,CAAC,MAAM,CAAC,CAACd,OAAO,CAAC8L,QAAQ,IAAI;MACnC,MAAMpJ,MAAM,GAAGC,gBAAgB,CAACmJ,QAAQ,CAAC;MACzC,IAAI,CAACpJ,MAAM,EAAE;MACb4H,YAAY,CAAC;QAAEE,IAAI,EAAE,QAAQ;QAAE9H;OAAQ,EAAEoJ,QAAQ,CAAC;KACnD,CAAC;;AAEN,CAAC,CAAC;;ACfF,MAAMC,oBAAoB,GAAGC,UAAU,CAACC,OAAO,CAACC,QAAQ,CAAC9K,IAAI,CAAC,IAAI,GAAG;AAGrE,MAAM+K,OAAO,GAAGC,aAAa,CAACC,MAAM,WAAWC,IAAI,CAAC5I,GAAG,CAAC,CAAC;;AAEzD,SAAS6I,SAASA,CAAClL,IAAY,EAAEmL,OAAe,EAAE;EAChD,IAAIT,oBAAoB,EAAE;IACxB,OAAOI,OAAO,CACXzL,OAAO,CAACW,IAAI,EAAE;MACboL,KAAK,EAAE,CAACD,OAAO;KAChB,CAAC,CACDE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;GACvB,MAAM;IACL,OAAOC,cAAc,CAACC,IAAI,CAACvL,IAAI,EAAE;MAAEmL;KAAS,CAAC,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;;AAErE;AAEO,SAAShM,OAAOA,CACrBmM,OAAe,EACflJ,UAAkB,EAClBmJ,eAAiC,EACzB;EACR,IAAIA,eAAe,KAAK,KAAK,EAAE,OAAOnJ,UAAU;EAEhD,IAAI6I,OAAO,GAAGK,OAAO;EACrB,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;IACvCN,OAAO,GAAG7L,IAAI,CAACD,OAAO,CAAC8L,OAAO,EAAEM,eAAe,CAAC;;EAGlD,IAAI;IACF,OAAOP,SAAS,CAAC5I,UAAU,EAAE6I,OAAO,CAAC;GACtC,CAAC,OAAOO,GAAG,EAAE;IACZ,IAAIA,GAAG,CAACC,IAAI,KAAK,kBAAkB,EAAE,MAAMD,GAAG;IAE9C,MAAMzM,MAAM,CAAC2M,MAAM,CACjB,IAAI1D,KAAK,CAAE,sBAAqB5F,UAAW,kBAAiBkJ,OAAQ,GAAE,CAAC,EACvE;MACEG,IAAI,EAAE,0BAA0B;MAChChE,QAAQ,EAAErF,UAAU;MACpBkJ;KAEJ,CAAC;;AAEL;AAEO,SAAS3M,GAAGA,CAACsM,OAAe,EAAEnL,IAAY,EAAE;EACjD,IAAI;IACFkL,SAAS,CAAClL,IAAI,EAAEmL,OAAO,CAAC;IACxB,OAAO,IAAI;GACZ,CAAC,MAAM;IACN,OAAO,KAAK;;AAEhB;AAEO,SAASU,UAAUA,CAACC,WAAwB,EAAE;EACnD,IAAIA,WAAW,CAAC/E,IAAI,KAAK,CAAC,EAAE;EAE5B,MAAMgF,IAAI,GAAG/E,KAAK,CAACC,IAAI,CAAC6E,WAAW,CAAC,CAACE,IAAI,EAAE,CAACpF,IAAI,CAAC,GAAG,CAAC;EAErDqF,OAAO,CAACC,IAAI,CACV,8EAA8E,GAC5E,6CAA6C,GAC5C,wBAAuBH,IAAK,IAAG,GAC/B,cAAaA,IAAK,IACvB,CAAC;EAEDnB,OAAO,CAACuB,QAAQ,GAAG,CAAC;AACtB;AAEA,IAAIC,cAAc,GAAG,IAAI1N,GAAG,EAAU;AAEtC,MAAM2N,2BAA2B,GAAGC,QAAQ,CAAC,MAAM;EACjDT,UAAU,CAACO,cAAc,CAAC;EAC1BA,cAAc,GAAG,IAAI1N,GAAG,EAAU;AACpC,CAAC,EAAE,GAAG,CAAC;AAEA,SAAS6N,eAAeA,CAACT,WAAwB,EAAE;EACxD,IAAIA,WAAW,CAAC/E,IAAI,KAAK,CAAC,EAAE;EAE5B+E,WAAW,CAACnN,OAAO,CAACqB,IAAI,IAAIoM,cAAc,CAACtN,GAAG,CAACkB,IAAI,CAAC,CAAC;EACrDqM,2BAA2B,EAAE;AAC/B;;AC3EA,MAAMG,qBAAqB,GAAG,IAAI9N,GAAG,CAAS,CAC5C,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,QAAQ,CACT,CAAC;AAEa,SAAS+N,kBAAkBA,CACxCrF,SAA+B,EAChB;EACf,MAAM;IAAEsF,MAAM,EAAEC,OAAO;IAAEC,QAAQ,EAAEC,SAAS;IAAEC,MAAM,EAAEC;GAAS,GAAG3F,SAAS;EAE3E,OAAO6D,IAAI,IAAI;IACb,IAAIA,IAAI,CAAC9B,IAAI,KAAK,QAAQ,IAAI4D,OAAO,IAAIlO,KAAG,CAACkO,OAAO,EAAE9B,IAAI,CAACjL,IAAI,CAAC,EAAE;MAChE,OAAO;QAAEmJ,IAAI,EAAE,QAAQ;QAAE6D,IAAI,EAAED,OAAO,CAAC9B,IAAI,CAACjL,IAAI,CAAC;QAAEA,IAAI,EAAEiL,IAAI,CAACjL;OAAM;;IAGtE,IAAIiL,IAAI,CAAC9B,IAAI,KAAK,UAAU,IAAI8B,IAAI,CAAC9B,IAAI,KAAK,IAAI,EAAE;MAClD,MAAM;QAAEnI,SAAS;QAAEjC,MAAM;QAAEC;OAAK,GAAGiM,IAAI;MAEvC,IAAIlM,MAAM,IAAIiC,SAAS,KAAK,QAAQ,EAAE;QACpC,IAAI+L,OAAO,IAAIP,qBAAqB,CAAC3N,GAAG,CAACE,MAAM,CAAC,IAAIF,KAAG,CAACkO,OAAO,EAAE/N,GAAG,CAAC,EAAE;UACrE,OAAO;YAAEmK,IAAI,EAAE,QAAQ;YAAE6D,IAAI,EAAED,OAAO,CAAC/N,GAAG,CAAC;YAAEgB,IAAI,EAAEhB;WAAK;;QAG1D,IAAI2N,OAAO,IAAI9N,KAAG,CAAC8N,OAAO,EAAE5N,MAAM,CAAC,IAAIF,KAAG,CAAC8N,OAAO,CAAC5N,MAAM,CAAC,EAAEC,GAAG,CAAC,EAAE;UAChE,OAAO;YACLmK,IAAI,EAAE,QAAQ;YACd6D,IAAI,EAAEL,OAAO,CAAC5N,MAAM,CAAC,CAACC,GAAG,CAAC;YAC1BgB,IAAI,EAAG,GAAEjB,MAAO,IAAGC,GAAI;WACxB;;;MAIL,IAAI6N,SAAS,IAAIhO,KAAG,CAACgO,SAAS,EAAE7N,GAAG,CAAC,EAAE;QACpC,OAAO;UAAEmK,IAAI,EAAE,UAAU;UAAE6D,IAAI,EAAEH,SAAS,CAAC7N,GAAG,CAAC;UAAEgB,IAAI,EAAG,GAAEhB,GAAI;SAAG;;;GAGtE;AACH;;AC1CA,MAAMiO,UAAU,GAAGC,WAAW,CAAC7O,OAAO,IAAI6O,WAAW;AA8BrD,SAASC,cAAcA,CACrB/E,OAAsB,EACtBC,QAAQ,EAWR;EACA,MAAM;IACJ+E,MAAM;IACNpH,OAAO,EAAEqH,aAAa;IACtBC,wBAAwB;IACxBC,UAAU;IACVC,KAAK;IACLC,oBAAoB;IACpBhC,eAAe;IACf,GAAGiC;GACJ,GAAGtF,OAAO;EAEX,IAAIuF,OAAO,CAACvF,OAAO,CAAC,EAAE;IACpB,MAAM,IAAIF,KAAK,CACZ;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFACI,CAAC;;EAGH,IAAI0F,UAAU;EACd,IAAIR,MAAM,KAAK,cAAc,EAAEQ,UAAU,GAAG,aAAa,CAAC,KACrD,IAAIR,MAAM,KAAK,cAAc,EAAEQ,UAAU,GAAG,aAAa,CAAC,KAC1D,IAAIR,MAAM,KAAK,YAAY,EAAEQ,UAAU,GAAG,WAAW,CAAC,KACtD,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;IACnC,MAAM,IAAIlF,KAAK,CAAC,0BAA0B,CAAC;GAC5C,MAAM;IACL,MAAM,IAAIA,KAAK,CACZ,uDAAsD,GACpD,8BAA6BjC,IAAI,CAACC,SAAS,CAACkH,MAAM,CAAE,GACzD,CAAC;;EAGH,IAAI,OAAOK,oBAAoB,KAAK,UAAU,EAAE;IAC9C,IAAIrF,OAAO,CAACN,OAAO,IAAIM,OAAO,CAACJ,OAAO,EAAE;MACtC,MAAM,IAAIE,KAAK,CACZ,wDAAuD,GACrD,kCACL,CAAC;;GAEJ,MAAM,IAAIuF,oBAAoB,IAAI,IAAI,EAAE;IACvC,MAAM,IAAIvF,KAAK,CACZ,wDAAuD,GACrD,cAAajC,IAAI,CAACC,SAAS,CAACuH,oBAAoB,CAAE,GACvD,CAAC;;EAGH,IACEhC,eAAe,IAAI,IAAI,IACvB,OAAOA,eAAe,KAAK,SAAS,IACpC,OAAOA,eAAe,KAAK,QAAQ,EACnC;IACA,MAAM,IAAIvD,KAAK,CACZ,4DAA2D,GACzD,cAAajC,IAAI,CAACC,SAAS,CAACuF,eAAe,CAAE,GAClD,CAAC;;EAGH,IAAIzF,OAAO;EAEX;;;EAGEqH,aAAa,IACbE,UAAU,IACVD,wBAAwB,EACxB;IACA,MAAMO,UAAU,GACd,OAAOR,aAAa,KAAK,QAAQ,IAAIrG,KAAK,CAAC8G,OAAO,CAACT,aAAa,CAAC,GAC7D;MAAEU,QAAQ,EAAEV;KAAe,GAC3BA,aAAa;IAEnBrH,OAAO,GAAGiH,UAAU,CAACY,UAAU,EAAE;MAC/BP,wBAAwB;MACxBC;KACD,CAAC;GACH,MAAM;IACLvH,OAAO,GAAGqC,QAAQ,CAACrC,OAAO,EAAE;;EAG9B,OAAO;IACLoH,MAAM;IACNQ,UAAU;IACV5H,OAAO;IACPyF,eAAe,EAAEA,eAAe,WAAfA,eAAe,GAAI,KAAK;IACzCgC,oBAAoB;IACpBD,KAAK,EAAE,CAAC,CAACA,KAAK;IACdE,eAAe,EAAEA;GAClB;AACH;AAEA,SAASM,mBAAmBA,CAC1BC,OAAkC,EAClC7F,OAAsB,EACtBE,mBAAmB,EACnBkD,OAAO,EACP0C,QAAQ,EACR7F,QAAQ,EACR;EACA,MAAM;IACJ+E,MAAM;IACNQ,UAAU;IACV5H,OAAO;IACPwH,KAAK;IACLC,oBAAoB;IACpBC,eAAe;IACfjC;GACD,GAAG0B,cAAc,CAAU/E,OAAO,EAAEC,QAAQ,CAAC;;;EAG9C,IAAIP,OAAO,EAAEE,OAAO;EACpB,IAAImG,gBAAgB;EACpB,IAAIC,cAA+C;EACnD,IAAIC,eAAe;EAEnB,MAAMC,QAAQ,GAAGxM,iBAAiB,CAChC,IAAIqB,qBAAqB,CACvBb,UAAU,IAAIyJ,OAAY,CAACP,OAAO,EAAElJ,UAAU,EAAEmJ,eAAe,CAAC,EAC/DzL,IAAY;IAAA,IAAAuO,mBAAA,EAAAC,eAAA;IAAA,QAAAD,mBAAA,IAAAC,eAAA,GAAKJ,cAAc,qBAAdI,eAAA,CAAgB/O,GAAG,CAACO,IAAI,CAAC,YAAAuO,mBAAA,GAAIxJ,QAAQ;GACzD,CACF,CAAC;EAED,MAAM0J,SAAS,GAAG,IAAIpK,GAAG,EAAE;EAE3B,MAAMqK,GAAgB,GAAG;IACvBC,KAAK,EAAEtG,QAAQ;IACfiG,QAAQ;IACRlB,MAAM,EAAEhF,OAAO,CAACgF,MAAM;IACtBpH,OAAO;IACPyG,kBAAkB;IAClBgB,oBAAoBA,CAACzN,IAAI,EAAE;MACzB,IAAIoO,cAAc,KAAKpJ,SAAS,EAAE;QAChC,MAAM,IAAIkD,KAAK,CACZ,yBAAwB+F,OAAO,CAACjO,IAAK,aAAY,GAC/C,+DACL,CAAC;;MAEH,IAAI,CAACoO,cAAc,CAACvP,GAAG,CAACmB,IAAI,CAAC,EAAE;QAC7BiM,OAAO,CAACC,IAAI,CACT,yBAAwB0C,YAAa,aAAY,GAC/C,qBAAoB5O,IAAK,IAC9B,CAAC;;MAGH,IAAIqO,eAAe,IAAI,CAACA,eAAe,CAACrO,IAAI,CAAC,EAAE,OAAO,KAAK;MAE3D,IAAI6O,YAAY,GAAGC,UAAU,CAAC9O,IAAI,EAAEgG,OAAO,EAAE;QAC3C+I,UAAU,EAAEZ,gBAAgB;QAC5BnF,QAAQ,EAAElB,OAAO;QACjBkH,QAAQ,EAAEhH;OACX,CAAC;MAEF,IAAIyF,oBAAoB,EAAE;QACxBoB,YAAY,GAAGpB,oBAAoB,CAACzN,IAAI,EAAE6O,YAAY,CAAC;QACvD,IAAI,OAAOA,YAAY,KAAK,SAAS,EAAE;UACrC,MAAM,IAAI3G,KAAK,CAAE,8CAA6C,CAAC;;;MAInE,OAAO2G,YAAY;KACpB;IACDrB,KAAKA,CAACxN,IAAI,EAAE;MAAA,IAAAiP,SAAA,EAAAC,qBAAA;MACVhB,QAAQ,EAAE,CAACiB,KAAK,GAAG,IAAI;MAEvB,IAAI,CAAC3B,KAAK,IAAI,CAACxN,IAAI,EAAE;MAErB,IAAIkO,QAAQ,EAAE,CAAC9G,SAAS,CAACvI,GAAG,CAAC+P,YAAY,CAAC,EAAE;MAC5CV,QAAQ,EAAE,CAAC9G,SAAS,CAACtI,GAAG,CAACkB,IAAI,CAAC;MAC9B,CAAAkP,qBAAA,IAAAD,SAAA,GAAAf,QAAQ,EAAE,EAACC,gBAAgB,YAAAe,qBAAA,GAA3BD,SAAA,CAAWd,gBAAgB,GAAKA,gBAAgB;KACjD;IACDiB,gBAAgBA,CAACpP,IAAI,EAAEqP,OAAO,GAAG,GAAG,EAAE;MACpC,IAAI/G,mBAAmB,KAAK,KAAK,EAAE;MACnC,IAAImD,eAAe,EAAE;;;;QAInB;;MAGF,MAAM6D,GAAG,GAAGD,OAAO,KAAK,GAAG,GAAGrP,IAAI,GAAI,GAAEA,IAAK,KAAIqP,OAAQ,EAAC;MAE1D,MAAMF,KAAK,GAAG7G,mBAAmB,CAACI,GAAG,GACjC,KAAK,GACL6G,QAAQ,CAACd,SAAS,EAAG,GAAEzO,IAAK,OAAMwL,OAAQ,EAAC,EAAE,MAC3CO,GAAQ,CAACP,OAAO,EAAExL,IAAI,CACxB,CAAC;MAEL,IAAI,CAACmP,KAAK,EAAE;QACVjB,QAAQ,EAAE,CAACpC,WAAW,CAAChN,GAAG,CAACwQ,GAAG,CAAC;;;GAGpC;EAED,MAAMnI,QAAQ,GAAG8G,OAAO,CAACS,GAAG,EAAEhB,eAAe,EAAElC,OAAO,CAAC;EACvD,MAAMoD,YAAY,GAAGzH,QAAQ,CAACnH,IAAI,IAAIiO,OAAO,CAACjO,IAAI;EAElD,IAAI,OAAOmH,QAAQ,CAACyG,UAAU,CAAC,KAAK,UAAU,EAAE;IAC9C,MAAM,IAAI1F,KAAK,CACZ,QAAO0G,YAAa,mCAAkCxB,MAAO,uBAChE,CAAC;;EAGH,IAAIpG,KAAK,CAAC8G,OAAO,CAAC3G,QAAQ,CAACC,SAAS,CAAC,EAAE;IACrCgH,cAAc,GAAG,IAAI/J,GAAG,CACtB8C,QAAQ,CAACC,SAAS,CAACzB,GAAG,CAAC,CAAC3F,IAAI,EAAEoF,KAAK,KAAK,CAACpF,IAAI,EAAEoF,KAAK,CAAC,CACvD,CAAC;IACDiJ,eAAe,GAAGlH,QAAQ,CAACkH,eAAe;GAC3C,MAAM,IAAIlH,QAAQ,CAACC,SAAS,EAAE;IAC7BgH,cAAc,GAAG,IAAI/J,GAAG,CACtBpF,MAAM,CAAC2I,IAAI,CAACT,QAAQ,CAACC,SAAS,CAAC,CAACzB,GAAG,CAAC,CAAC3F,IAAI,EAAEoF,KAAK,KAAK,CAACpF,IAAI,EAAEoF,KAAK,CAAC,CACpE,CAAC;IACD+I,gBAAgB,GAAGhH,QAAQ,CAACC,SAAS;IACrCiH,eAAe,GAAGlH,QAAQ,CAACkH,eAAe;GAC3C,MAAM;IACLD,cAAc,GAAG,IAAI/J,GAAG,EAAE;;EAG5B,CAAC;IAAEyD,OAAO;IAAEE;GAAS,GAAGd,sBAAsB,CAC5C0H,YAAY,EACZR,cAAc,EACdV,eAAe,CAAC5F,OAAO,IAAI,EAAE,EAC7B4F,eAAe,CAAC1F,OAAO,IAAI,EAC7B,CAAC;EAED,IAAIiB,YAAkE;EACtE,IAAI2E,UAAU,KAAK,aAAa,EAAE;IAChC3E,YAAY,GAAGA,CAACuG,OAAO,EAAElQ,IAAI,KAAK;MAAA,IAAAmQ,IAAA;MAChC,MAAMC,KAAK,GAAGpB,QAAQ,CAAChP,IAAI,CAAC;MAC5B,QAAAmQ,IAAA,GACGtI,QAAQ,CAACyG,UAAU,CAAC,CAAC4B,OAAO,EAAEE,KAAK,EAAEpQ,IAAI,CAAC,YAAAmQ,IAAA,GAAuB,KAAK;KAE1E;GACF,MAAM;IACLxG,YAAY,GAAGA,CAACuG,OAAO,EAAElQ,IAAI,KAAK;MAChC,MAAMoQ,KAAK,GAAGpB,QAAQ,CAAChP,IAAI,CAAC;MAC5B6H,QAAQ,CAACyG,UAAU,CAAC,CAAC4B,OAAO,EAAEE,KAAK,EAAEpQ,IAAI,CAAC;MAC1C,OAAO,KAAK;KACb;;EAGH,OAAO;IACLkO,KAAK;IACLJ,MAAM;IACNpH,OAAO;IACPmB,QAAQ;IACRyH,YAAY;IACZ3F;GACD;AACH;AAEe,SAAS0G,sBAAsBA,CAC5C1B,OAAkC,EAClC;EACA,OAAO2B,OAAO,CAAC,CAACvH,QAAQ,EAAED,OAAsB,EAAEoD,OAAe,KAAK;IACpEnD,QAAQ,CAACwH,aAAa,CAAC,0BAA0B,CAAC;IAClD,MAAM;MAAEC;KAAU,GAAGzH,QAAQ;IAE7B,IAAI6F,QAAQ;IAEZ,MAAM5F,mBAAmB,GAAGH,gCAAgC,CAC1DC,OAAO,EACPC,QACF,CAAC;IAED,MAAM;MAAEmF,KAAK;MAAEJ,MAAM;MAAEpH,OAAO;MAAEmB,QAAQ;MAAEyH,YAAY;MAAE3F;KAAc,GACpE+E,mBAAmB,CACjBC,OAAO,EACP7F,OAAO,EACPE,mBAAmB,EACnBkD,OAAO,EACP,MAAM0C,QAAQ,EACd7F,QACF,CAAC;IAEH,MAAM0H,aAAa,GAAG3C,MAAM,KAAK,cAAc,GAAGxO,KAAO,GAAGA,KAAO;IAEnE,MAAMoR,OAAO,GAAG7I,QAAQ,CAAC6I,OAAO,GAC5BF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,CAACH,aAAa,CAAC9G,YAAY,CAAC,EAAE9B,QAAQ,CAAC6I,OAAO,CAAC,CAAC,GACxED,aAAa,CAAC9G,YAAY,CAAC;IAE/B,IAAIuE,KAAK,IAAIA,KAAK,KAAK1H,0BAA0B,EAAE;MACjDmG,OAAO,CAACzD,GAAG,CAAE,GAAEoG,YAAa,oBAAmB,CAAC;MAChD3C,OAAO,CAACzD,GAAG,CAAE,oBAAmBzC,yBAAyB,CAACC,OAAO,CAAE,EAAC,CAAC;MACrEiG,OAAO,CAACzD,GAAG,CAAE,4BAA2B4E,MAAO,YAAW,CAAC;;IAG7D,MAAM;MAAE+C;KAAa,GAAGhJ,QAAQ;IAEhC,OAAO;MACLnH,IAAI,EAAE,kBAAkB;MACxBgQ,OAAO;MAEPI,GAAGA,CAACC,IAAI,EAAE;QAAA,IAAAC,aAAA;QACR,IAAIH,WAAW,EAAE;UACf,IACEE,IAAI,CAAC5Q,GAAG,CAAC,0BAA0B,CAAC,IACpC4Q,IAAI,CAAC5Q,GAAG,CAAC,0BAA0B,CAAC,KAAK0Q,WAAW,EACpD;YACAlE,OAAO,CAACC,IAAI,CACT,kCAAiC,GAC/B,KAAImE,IAAI,CAAC5Q,GAAG,CAAC,8BAA8B,CAAE,EAAC,GAC9C,QAAOmP,YAAa,4BAA2B,GAC/C,2CAA0C,GAC1C,IAAGyB,IAAI,CAAC5Q,GAAG,CAAC,0BAA0B,CAAE,QAAO0Q,WAAY,GAAE,GAC7D,kCACL,CAAC;WACF,MAAM;YACLE,IAAI,CAAC9L,GAAG,CAAC,0BAA0B,EAAE4L,WAAW,CAAC;YACjDE,IAAI,CAAC9L,GAAG,CAAC,8BAA8B,EAAEqK,YAAY,CAAC;;;QAI1DV,QAAQ,GAAG;UACT9G,SAAS,EAAE,IAAI1I,GAAG,EAAE;UACpByP,gBAAgB,EAAEnJ,SAAS;UAC3BmK,KAAK,EAAE,KAAK;UACZoB,SAAS,EAAE,IAAI7R,GAAG,EAAE;UACpBoN,WAAW,EAAE,IAAIpN,GAAG;SACrB;QAED,CAAA4R,aAAA,GAAAnJ,QAAQ,CAACiJ,GAAG,qBAAZE,aAAA,CAAcE,KAAK,CAAC,IAAI,EAAE7O,SAAS,CAAC;OACrC;MACD8O,IAAIA,GAAG;QAAA,IAAAC,cAAA;QACL,CAAAA,cAAA,GAAAvJ,QAAQ,CAACsJ,IAAI,qBAAbC,cAAA,CAAeF,KAAK,CAAC,IAAI,EAAE7O,SAAS,CAAC;QAErC,IAAI2G,mBAAmB,KAAK,KAAK,EAAE;UACjC,IAAIA,mBAAmB,CAACE,GAAG,KAAK,UAAU,EAAE;YAC1CuD,UAAe,CAACmC,QAAQ,CAACpC,WAAW,CAAC;WACtC,MAAM;YACLC,eAAoB,CAACmC,QAAQ,CAACpC,WAAW,CAAC;;;QAI9C,IAAI,CAAC0B,KAAK,EAAE;QAEZ,IAAI,IAAI,CAACmD,QAAQ,EAAE1E,OAAO,CAACzD,GAAG,CAAE,MAAK,IAAI,CAACmI,QAAS,GAAE,CAAC;QAEtD,IAAIzC,QAAQ,CAAC9G,SAAS,CAACL,IAAI,KAAK,CAAC,EAAE;UACjCkF,OAAO,CAACzD,GAAG,CACT4E,MAAM,KAAK,cAAc,GACrBc,QAAQ,CAACiB,KAAK,GACX,8BAA6BP,YAAa,qCAAoC,GAC9E,2BAA0BA,YAAa,+BAA8B,GACvE,uCAAsCA,YAAa,qCAC1D,CAAC;UAED;;QAGF,IAAIxB,MAAM,KAAK,cAAc,EAAE;UAC7BnB,OAAO,CAACzD,GAAG,CACR,OAAMoG,YAAa,yCAAwC,GACzD,0BACL,CAAC;SACF,MAAM;UACL3C,OAAO,CAACzD,GAAG,CACR,OAAMoG,YAAa,0CACtB,CAAC;;QAGH,KAAK,MAAM5O,IAAI,IAAIkO,QAAQ,CAAC9G,SAAS,EAAE;UAAA,IAAAwJ,sBAAA;UACrC,KAAAA,sBAAA,GAAI1C,QAAQ,CAACC,gBAAgB,aAAzByC,sBAAA,CAA4B5Q,IAAI,CAAC,EAAE;YACrC,MAAM6Q,eAAe,GAAGC,mBAAmB,CACzC9Q,IAAI,EACJgG,OAAO,EACPkI,QAAQ,CAACC,gBACX,CAAC;YAED,MAAM4C,gBAAgB,GAAG9K,IAAI,CAACC,SAAS,CAAC2K,eAAe,CAAC,CACrDxF,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CACnBA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CACtBA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YAEzBY,OAAO,CAACzD,GAAG,CAAE,KAAIxI,IAAK,IAAG+Q,gBAAiB,EAAC,CAAC;WAC7C,MAAM;YACL9E,OAAO,CAACzD,GAAG,CAAE,KAAIxI,IAAK,EAAC,CAAC;;;;KAI/B;GACF,CAAC;AACJ;AAEA,SAASuP,QAAQA,CAAC5J,GAAG,EAAE3G,GAAG,EAAEgS,UAAU,EAAE;EACtC,IAAIC,GAAG,GAAGtL,GAAG,CAAClG,GAAG,CAACT,GAAG,CAAC;EACtB,IAAIiS,GAAG,KAAKjM,SAAS,EAAE;IACrBiM,GAAG,GAAGD,UAAU,EAAE;IAClBrL,GAAG,CAACpB,GAAG,CAACvF,GAAG,EAAEiS,GAAG,CAAC;;EAEnB,OAAOA,GAAG;AACZ;AAEA,SAAStD,OAAOA,CAAC7M,GAAG,EAAE;EACpB,OAAO7B,MAAM,CAAC2I,IAAI,CAAC9G,GAAG,CAAC,CAACM,MAAM,KAAK,CAAC;AACtC;;;;"}