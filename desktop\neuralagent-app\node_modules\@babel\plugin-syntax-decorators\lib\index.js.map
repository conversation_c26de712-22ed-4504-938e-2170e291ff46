{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "version", "legacy", "undefined", "Error", "decoratorsBeforeExport", "name", "manipulateOptions", "generatorOpts", "parserOpts", "plugins", "push", "allowCallParenthesized"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  // TODO(Babel 8): Remove\n  legacy?: boolean;\n  // TODO(Babel 8): Remove \"2018-09\", \"2021-12\", '2022-03', '2023-01' and '2023-05'\n  version?:\n    | \"legacy\"\n    | \"2018-09\"\n    | \"2021-12\"\n    | \"2022-03\"\n    | \"2023-01\"\n    | \"2023-05\"\n    | \"2023-11\";\n  // TODO(Babel 8): Remove\n  decoratorsBeforeExport?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  let { version } = options;\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (version === undefined) {\n      throw new Error(\n        \"The decorators plugin requires a 'version' option, whose value must be one of: \" +\n          \"'2023-11', '2023-05', '2023-01', '2022-03', '2021-12', '2018-09', or 'legacy'.\",\n      );\n    }\n    if (\n      version !== \"2023-11\" &&\n      version !== \"2023-05\" &&\n      version !== \"2023-01\" &&\n      version !== \"2022-03\" &&\n      version !== \"2021-12\" &&\n      version !== \"legacy\"\n    ) {\n      throw new Error(\"Unsupported decorators version: \" + version);\n    }\n    if (options.legacy !== undefined) {\n      throw new Error(\n        `The .legacy option has been removed in Babel 8. Use .version: \"legacy\" instead.`,\n      );\n    }\n    if (options.decoratorsBeforeExport !== undefined) {\n      throw new Error(\n        `The .decoratorsBeforeExport option has been removed in Babel 8.`,\n      );\n    }\n  } else {\n    const { legacy } = options;\n\n    if (legacy !== undefined) {\n      if (typeof legacy !== \"boolean\") {\n        throw new Error(\".legacy must be a boolean.\");\n      }\n      if (version !== undefined) {\n        throw new Error(\n          \"You can either use the .legacy or the .version option, not both.\",\n        );\n      }\n    }\n\n    if (version === undefined) {\n      version = legacy ? \"legacy\" : \"2018-09\";\n    } else if (\n      version !== \"2023-11\" &&\n      version !== \"2023-05\" &&\n      version !== \"2023-01\" &&\n      version !== \"2022-03\" &&\n      version !== \"2021-12\" &&\n      version !== \"2018-09\" &&\n      version !== \"legacy\"\n    ) {\n      // Fallback to print the invalid version option regardless of the type\n      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n      throw new Error(\"Unsupported decorators version: \" + version);\n    }\n\n    // eslint-disable-next-line no-var\n    var { decoratorsBeforeExport } = options;\n    if (decoratorsBeforeExport === undefined) {\n      if (version === \"2021-12\" || version === \"2022-03\") {\n        decoratorsBeforeExport = false;\n      } else if (version === \"2018-09\") {\n        throw new Error(\n          \"The decorators plugin, when .version is '2018-09' or not specified,\" +\n            \" requires a 'decoratorsBeforeExport' option, whose value must be a boolean.\",\n        );\n      }\n    } else {\n      if (\n        version === \"legacy\" ||\n        version === \"2022-03\" ||\n        version === \"2023-01\"\n      ) {\n        throw new Error(\n          `'decoratorsBeforeExport' can't be used with ${version} decorators.`,\n        );\n      }\n      if (typeof decoratorsBeforeExport !== \"boolean\") {\n        throw new Error(\"'decoratorsBeforeExport' must be a boolean.\");\n      }\n    }\n  }\n\n  return {\n    name: \"syntax-decorators\",\n\n    manipulateOptions({ generatorOpts }, parserOpts) {\n      if (version === \"legacy\") {\n        parserOpts.plugins.push(\"decorators-legacy\");\n      } else if (process.env.BABEL_8_BREAKING) {\n        parserOpts.plugins.push(\n          [\"decorators\", { allowCallParenthesized: false }],\n          \"decoratorAutoAccessors\",\n        );\n      } else {\n        if (\n          version === \"2023-01\" ||\n          version === \"2023-05\" ||\n          version === \"2023-11\"\n        ) {\n          parserOpts.plugins.push(\n            [\"decorators\", { allowCallParenthesized: false }],\n            \"decoratorAutoAccessors\",\n          );\n        } else if (version === \"2022-03\") {\n          parserOpts.plugins.push(\n            [\n              \"decorators\",\n              { decoratorsBeforeExport: false, allowCallParenthesized: false },\n            ],\n            \"decoratorAutoAccessors\",\n          );\n        } else if (version === \"2021-12\") {\n          parserOpts.plugins.push(\n            [\"decorators\", { decoratorsBeforeExport }],\n            \"decoratorAutoAccessors\",\n          );\n          generatorOpts.decoratorsBeforeExport = decoratorsBeforeExport;\n        } else if (version === \"2018-09\") {\n          parserOpts.plugins.push([\"decorators\", { decoratorsBeforeExport }]);\n          generatorOpts.decoratorsBeforeExport = decoratorsBeforeExport;\n        }\n      }\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAkBtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAChDD,GAAG,CAACE,aAAa,uCAAoB,CAAC;EAEtC,IAAI;IAAEC;EAAQ,CAAC,GAAGF,OAAO;EA6BlB;IACL,MAAM;MAAEG;IAAO,CAAC,GAAGH,OAAO;IAE1B,IAAIG,MAAM,KAAKC,SAAS,EAAE;MACxB,IAAI,OAAOD,MAAM,KAAK,SAAS,EAAE;QAC/B,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MACA,IAAIH,OAAO,KAAKE,SAAS,EAAE;QACzB,MAAM,IAAIC,KAAK,CACb,kEACF,CAAC;MACH;IACF;IAEA,IAAIH,OAAO,KAAKE,SAAS,EAAE;MACzBF,OAAO,GAAGC,MAAM,GAAG,QAAQ,GAAG,SAAS;IACzC,CAAC,MAAM,IACLD,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,QAAQ,EACpB;MAGA,MAAM,IAAIG,KAAK,CAAC,kCAAkC,GAAGH,OAAO,CAAC;IAC/D;IAGA,IAAI;MAAEI;IAAuB,CAAC,GAAGN,OAAO;IACxC,IAAIM,sBAAsB,KAAKF,SAAS,EAAE;MACxC,IAAIF,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;QAClDI,sBAAsB,GAAG,KAAK;MAChC,CAAC,MAAM,IAAIJ,OAAO,KAAK,SAAS,EAAE;QAChC,MAAM,IAAIG,KAAK,CACb,qEAAqE,GACnE,6EACJ,CAAC;MACH;IACF,CAAC,MAAM;MACL,IACEH,OAAO,KAAK,QAAQ,IACpBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;QACA,MAAM,IAAIG,KAAK,CACb,+CAA+CH,OAAO,cACxD,CAAC;MACH;MACA,IAAI,OAAOI,sBAAsB,KAAK,SAAS,EAAE;QAC/C,MAAM,IAAID,KAAK,CAAC,6CAA6C,CAAC;MAChE;IACF;EACF;EAEA,OAAO;IACLE,IAAI,EAAE,mBAAmB;IAEzBC,iBAAiBA,CAAC;MAAEC;IAAc,CAAC,EAAEC,UAAU,EAAE;MAC/C,IAAIR,OAAO,KAAK,QAAQ,EAAE;QACxBQ,UAAU,CAACC,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;MAC9C,CAAC,MAKM;QACL,IACEV,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;UACAQ,UAAU,CAACC,OAAO,CAACC,IAAI,CACrB,CAAC,YAAY,EAAE;YAAEC,sBAAsB,EAAE;UAAM,CAAC,CAAC,EACjD,wBACF,CAAC;QACH,CAAC,MAAM,IAAIX,OAAO,KAAK,SAAS,EAAE;UAChCQ,UAAU,CAACC,OAAO,CAACC,IAAI,CACrB,CACE,YAAY,EACZ;YAAEN,sBAAsB,EAAE,KAAK;YAAEO,sBAAsB,EAAE;UAAM,CAAC,CACjE,EACD,wBACF,CAAC;QACH,CAAC,MAAM,IAAIX,OAAO,KAAK,SAAS,EAAE;UAChCQ,UAAU,CAACC,OAAO,CAACC,IAAI,CACrB,CAAC,YAAY,EAAE;YAAEN;UAAuB,CAAC,CAAC,EAC1C,wBACF,CAAC;UACDG,aAAa,CAACH,sBAAsB,GAAGA,sBAAsB;QAC/D,CAAC,MAAM,IAAIJ,OAAO,KAAK,SAAS,EAAE;UAChCQ,UAAU,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC,YAAY,EAAE;YAAEN;UAAuB,CAAC,CAAC,CAAC;UACnEG,aAAa,CAACH,sBAAsB,GAAGA,sBAAsB;QAC/D;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}