{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\views\\\\BackgroundTask.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BackgroundTask() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n      src: \"http://127.0.0.1:39742/vnc.html?autoconnect=true&view_only=true&bell=off\",\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none'\n      },\n      title: \"NeuralAgent Background\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = BackgroundTask;\nvar _c;\n$RefreshReg$(_c, \"BackgroundTask\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "BackgroundTask", "style", "height", "width", "overflow", "children", "src", "border", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/views/BackgroundTask.js"], "sourcesContent": ["import React from 'react';\n\nexport default function BackgroundTask() {\n  return (\n    <div style={{ height: '100vh', width: '100vw', overflow: 'hidden' }}>\n      <iframe\n        src=\"http://127.0.0.1:39742/vnc.html?autoconnect=true&view_only=true&bell=off\"\n        style={{ width: '100%', height: '100%', border: 'none' }}\n        title=\"NeuralAgent Background\"\n      />\n    </div>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,oBACED,OAAA;IAAKE,KAAK,EAAE;MAAEC,MAAM,EAAE,OAAO;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAC,QAAA,eAClEN,OAAA;MACEO,GAAG,EAAC,0EAA0E;MAC9EL,KAAK,EAAE;QAAEE,KAAK,EAAE,MAAM;QAAED,MAAM,EAAE,MAAM;QAAEK,MAAM,EAAE;MAAO,CAAE;MACzDC,KAAK,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACC,EAAA,GAVuBb,cAAc;AAAA,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}