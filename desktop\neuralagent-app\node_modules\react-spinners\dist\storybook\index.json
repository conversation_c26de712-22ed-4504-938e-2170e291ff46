{"v": 5, "entries": {"barloader--docs": {"id": "barloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/BarLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "barloader--primary": {"type": "story", "id": "barloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/BarLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "beatloader--docs": {"id": "beatloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/BeatLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "beatloader--primary": {"type": "story", "id": "beatloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/BeatLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "bounceloader--docs": {"id": "bounceloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/BounceLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "bounceloader--primary": {"type": "story", "id": "bounceloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/BounceLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "circleloader--docs": {"id": "circleloader--docs", "title": "Circle<PERSON><PERSON>der", "name": "Docs", "importPath": "./stories/CircleLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "circleloader--primary": {"type": "story", "id": "circleloader--primary", "name": "Primary", "title": "Circle<PERSON><PERSON>der", "importPath": "./stories/CircleLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "climbingboxloader--docs": {"id": "climbingboxloader--docs", "title": "ClimbingBoxLoader", "name": "Docs", "importPath": "./stories/ClimbingBoxLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "climbingboxloader--primary": {"type": "story", "id": "climbingboxloader--primary", "name": "Primary", "title": "ClimbingBoxLoader", "importPath": "./stories/ClimbingBoxLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "cliploader--docs": {"id": "cliploader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/ClipLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "cliploader--primary": {"type": "story", "id": "cliploader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/ClipLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "clockloader--docs": {"id": "clockloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/ClockLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "clockloader--primary": {"type": "story", "id": "clockloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/ClockLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "dotloader--docs": {"id": "dotloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/DotLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "dotloader--primary": {"type": "story", "id": "dotloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/DotLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "fadeloader--docs": {"id": "fadeloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/FadeLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "fadeloader--primary": {"type": "story", "id": "fadeloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/FadeLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "gridloader--docs": {"id": "gridloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/GridLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "gridloader--primary": {"type": "story", "id": "gridloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/GridLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "hashloader--docs": {"id": "hashloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/HashLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "hashloader--primary": {"type": "story", "id": "hashloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/HashLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "moonloader--docs": {"id": "moonloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/MoonLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "moonloader--primary": {"type": "story", "id": "moonloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/MoonLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "pacmanloader--docs": {"id": "pacmanloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/PacmanLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "pacmanloader--primary": {"type": "story", "id": "pacmanloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/PacmanLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "propagateloader--docs": {"id": "propagateloader--docs", "title": "Propagate<PERSON><PERSON>der", "name": "Docs", "importPath": "./stories/PropagateLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "propagateloader--primary": {"type": "story", "id": "propagateloader--primary", "name": "Primary", "title": "Propagate<PERSON><PERSON>der", "importPath": "./stories/PropagateLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "puffloader--docs": {"id": "puffloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/PuffLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "puffloader--primary": {"type": "story", "id": "puffloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/PuffLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "pulseloader--docs": {"id": "pulseloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/PulseLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "pulseloader--primary": {"type": "story", "id": "pulseloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/PulseLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "ringloader--docs": {"id": "ringloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/RingLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "ringloader--primary": {"type": "story", "id": "ringloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/RingLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "riseloader--docs": {"id": "riseloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/RiseLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "riseloader--primary": {"type": "story", "id": "riseloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/RiseLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "rotateloader--docs": {"id": "rotateloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/RotateLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "rotateloader--primary": {"type": "story", "id": "rotateloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/RotateLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "scaleloader--docs": {"id": "scaleloader--docs", "title": "Scale<PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/ScaleLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "scaleloader--primary": {"type": "story", "id": "scaleloader--primary", "name": "Primary", "title": "Scale<PERSON><PERSON><PERSON>", "importPath": "./stories/ScaleLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "skewloader--docs": {"id": "skewloader--docs", "title": "SkewLoader", "name": "Docs", "importPath": "./stories/SkewLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "skewloader--primary": {"type": "story", "id": "skewloader--primary", "name": "Primary", "title": "SkewLoader", "importPath": "./stories/SkewLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "squareloader--docs": {"id": "squareloader--docs", "title": "Square<PERSON>oader", "name": "Docs", "importPath": "./stories/SquareLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "squareloader--primary": {"type": "story", "id": "squareloader--primary", "name": "Primary", "title": "Square<PERSON>oader", "importPath": "./stories/SquareLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "syncloader--docs": {"id": "syncloader--docs", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Docs", "importPath": "./stories/SyncLoader.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "syncloader--primary": {"type": "story", "id": "syncloader--primary", "name": "Primary", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "./stories/SyncLoader.stories.tsx", "tags": ["dev", "test", "autodocs"]}}}