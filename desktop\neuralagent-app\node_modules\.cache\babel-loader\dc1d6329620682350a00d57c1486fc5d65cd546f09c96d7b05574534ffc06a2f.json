{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.parseLengthAndUnit = parseLengthAndUnit;\nexports.cssValue = cssValue;\nvar cssUnit = {\n  cm: true,\n  mm: true,\n  in: true,\n  px: true,\n  pt: true,\n  pc: true,\n  em: true,\n  ex: true,\n  ch: true,\n  rem: true,\n  vw: true,\n  vh: true,\n  vmin: true,\n  vmax: true,\n  \"%\": true\n};\n/**\n * If size is a number, append px to the value as default unit.\n * If size is a string, validate against list of valid units.\n * If unit is valid, return size as is.\n * If unit is invalid, console warn issue, replace with px as the unit.\n *\n * @param {(number | string)} size\n * @return {LengthObject} LengthObject\n */\nfunction parseLengthAndUnit(size) {\n  if (typeof size === \"number\") {\n    return {\n      value: size,\n      unit: \"px\"\n    };\n  }\n  var value;\n  var valueString = (size.match(/^[0-9.]*/) || \"\").toString();\n  if (valueString.includes(\".\")) {\n    value = parseFloat(valueString);\n  } else {\n    value = parseInt(valueString, 10);\n  }\n  var unit = (size.match(/[^0-9]*$/) || \"\").toString();\n  if (cssUnit[unit]) {\n    return {\n      value: value,\n      unit: unit\n    };\n  }\n  console.warn(\"React Spinners: \".concat(size, \" is not a valid css value. Defaulting to \").concat(value, \"px.\"));\n  return {\n    value: value,\n    unit: \"px\"\n  };\n}\n/**\n * Take value as an input and return valid css value\n *\n * @param {(number | string)} value\n * @return {string} valid css value\n */\nfunction cssValue(value) {\n  var lengthWithunit = parseLengthAndUnit(value);\n  return \"\".concat(lengthWithunit.value).concat(lengthWithunit.unit);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "parseLengthAndUnit", "cssValue", "cssUnit", "cm", "mm", "in", "px", "pt", "pc", "em", "ex", "ch", "rem", "vw", "vh", "vmin", "vmax", "size", "unit", "valueString", "match", "toString", "includes", "parseFloat", "parseInt", "console", "warn", "concat", "lengthWithunit"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/react-spinners/helpers/unitConverter.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseLengthAndUnit = parseLengthAndUnit;\nexports.cssValue = cssValue;\nvar cssUnit = {\n    cm: true,\n    mm: true,\n    in: true,\n    px: true,\n    pt: true,\n    pc: true,\n    em: true,\n    ex: true,\n    ch: true,\n    rem: true,\n    vw: true,\n    vh: true,\n    vmin: true,\n    vmax: true,\n    \"%\": true,\n};\n/**\n * If size is a number, append px to the value as default unit.\n * If size is a string, validate against list of valid units.\n * If unit is valid, return size as is.\n * If unit is invalid, console warn issue, replace with px as the unit.\n *\n * @param {(number | string)} size\n * @return {LengthObject} LengthObject\n */\nfunction parseLengthAndUnit(size) {\n    if (typeof size === \"number\") {\n        return {\n            value: size,\n            unit: \"px\",\n        };\n    }\n    var value;\n    var valueString = (size.match(/^[0-9.]*/) || \"\").toString();\n    if (valueString.includes(\".\")) {\n        value = parseFloat(valueString);\n    }\n    else {\n        value = parseInt(valueString, 10);\n    }\n    var unit = (size.match(/[^0-9]*$/) || \"\").toString();\n    if (cssUnit[unit]) {\n        return {\n            value: value,\n            unit: unit,\n        };\n    }\n    console.warn(\"React Spinners: \".concat(size, \" is not a valid css value. Defaulting to \").concat(value, \"px.\"));\n    return {\n        value: value,\n        unit: \"px\",\n    };\n}\n/**\n * Take value as an input and return valid css value\n *\n * @param {(number | string)} value\n * @return {string} valid css value\n */\nfunction cssValue(value) {\n    var lengthWithunit = parseLengthAndUnit(value);\n    return \"\".concat(lengthWithunit.value).concat(lengthWithunit.unit);\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/CF,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3B,IAAIC,OAAO,GAAG;EACVC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,IAAI;EACTC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACV,GAAG,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShB,kBAAkBA,CAACiB,IAAI,EAAE;EAC9B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAO;MACHlB,KAAK,EAAEkB,IAAI;MACXC,IAAI,EAAE;IACV,CAAC;EACL;EACA,IAAInB,KAAK;EACT,IAAIoB,WAAW,GAAG,CAACF,IAAI,CAACG,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,EAAEC,QAAQ,CAAC,CAAC;EAC3D,IAAIF,WAAW,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC3BvB,KAAK,GAAGwB,UAAU,CAACJ,WAAW,CAAC;EACnC,CAAC,MACI;IACDpB,KAAK,GAAGyB,QAAQ,CAACL,WAAW,EAAE,EAAE,CAAC;EACrC;EACA,IAAID,IAAI,GAAG,CAACD,IAAI,CAACG,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,EAAEC,QAAQ,CAAC,CAAC;EACpD,IAAInB,OAAO,CAACgB,IAAI,CAAC,EAAE;IACf,OAAO;MACHnB,KAAK,EAAEA,KAAK;MACZmB,IAAI,EAAEA;IACV,CAAC;EACL;EACAO,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAACC,MAAM,CAACV,IAAI,EAAE,2CAA2C,CAAC,CAACU,MAAM,CAAC5B,KAAK,EAAE,KAAK,CAAC,CAAC;EAC/G,OAAO;IACHA,KAAK,EAAEA,KAAK;IACZmB,IAAI,EAAE;EACV,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,QAAQA,CAACF,KAAK,EAAE;EACrB,IAAI6B,cAAc,GAAG5B,kBAAkB,CAACD,KAAK,CAAC;EAC9C,OAAO,EAAE,CAAC4B,MAAM,CAACC,cAAc,CAAC7B,KAAK,CAAC,CAAC4B,MAAM,CAACC,cAAc,CAACV,IAAI,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}