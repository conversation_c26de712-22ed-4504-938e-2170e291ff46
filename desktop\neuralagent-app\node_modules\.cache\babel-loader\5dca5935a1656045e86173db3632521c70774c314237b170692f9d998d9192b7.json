{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\views\\\\Thread.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport axios from '../utils/axios';\nimport constants from '../utils/constants';\nimport { setLoadingDialog, setError } from '../store';\nimport ChatMessage from '../components/ChatMessage';\nimport { FlexSpacer } from '../components/Elements/SmallElements';\nimport NATextArea from '../components/Elements/TextAreas';\nimport { IconButton } from '../components/Elements/Button';\nimport { MdEdit, MdDelete } from 'react-icons/md';\nimport { FaArrowAltCircleUp, FaStopCircle } from 'react-icons/fa';\nimport ClipLoader from 'react-spinners/ClipLoader';\nimport { Text } from '../components/Elements/Typography';\nimport ThreadDialog from '../components/DataDialogs/ThreadDialog';\nimport YesNoDialog from '../components/Elements/YesNoDialog';\nimport { useNavigate } from 'react-router-dom';\nimport { MdOutlineSchedule } from 'react-icons/md';\nimport { GiBrain } from 'react-icons/gi';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ThreadDiv = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow: hidden;\n`;\n_c = ThreadDiv;\nconst ChatContainer = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding-top: 12px;\n  padding-bottom: 12px;\n`;\n_c2 = ChatContainer;\nconst SendingContainer = styled.div`\n  border: thin solid rgba(255,255,255,0.3);\n  padding: 10px;\n  border-radius: 20px;\n`;\n_c3 = SendingContainer;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 20px;\n`;\n_c4 = Header;\nconst ToggleContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.9rem;\n  color: var(--secondary-color);\n`;\n_c5 = ToggleContainer;\nconst ModeToggle = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  background-color: ${({\n  active\n}) => active ? 'rgba(255,255,255,0.1)' : 'transparent'};\n  color: #fff;\n  border: thin solid rgba(255,255,255,0.3);\n  border-radius: 999px;\n  padding: 6px 12px;\n  font-size: 13px;\n  transition: background-color 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255,255,255,0.1);\n  }\n`;\n_c6 = ModeToggle;\nexport default function Thread() {\n  _s();\n  const [thread, setThread] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [isSendingMessage, setSendingMessage] = useState(false);\n  const [backgroundMode, setBackgroundMode] = useState(false);\n  const [thinkingMode, setThinkingMode] = useState(false);\n  const [isThreadDialogOpen, setThreadDialogOpen] = useState(false);\n  const [isDeleteThreadDialogOpen, setDeleteThreadDialogOpen] = useState(false);\n  const accessToken = useSelector(state => state.accessToken);\n  const {\n    tid\n  } = useParams();\n  const bottomRef = useRef(null);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const getThread = () => {\n    dispatch(setLoadingDialog(true));\n    axios.get(`/threads/${tid}`, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      setThread(response.data);\n      dispatch(setLoadingDialog(false));\n    }).catch(error => {\n      var _error$response;\n      dispatch(setLoadingDialog(false));\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n  const getThreadMessages = () => {\n    dispatch(setLoadingDialog(true));\n    axios.get(`/threads/${tid}/thread_messages`, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      setMessages(response.data);\n      dispatch(setLoadingDialog(false));\n    }).catch(error => {\n      var _error$response2;\n      dispatch(setLoadingDialog(false));\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n  const sendMessage = () => {\n    if (messageText.length === 0 || isSendingMessage || thread.status === 'working') {\n      return;\n    }\n    const data = {\n      text: messageText.trim(),\n      background_mode: backgroundMode,\n      extended_thinking_mode: thinkingMode\n    };\n    setMessageText('');\n    setSendingMessage(true);\n    dispatch(setLoadingDialog(true));\n    axios.post(`/threads/${tid}/send_message`, data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(async response => {\n      dispatch(setLoadingDialog(false));\n      setSendingMessage(false);\n      if (response.data.type === 'desktop_task') {\n        if (!backgroundMode && response.data.is_background_mode_requested) {\n          const ready = await window.electronAPI.isBackgroundModeReady();\n          if (!ready) {\n            cancelRunningTask();\n            return;\n          }\n        }\n        setBackgroundMode(backgroundMode || response.data.is_background_mode_requested);\n        setThinkingMode(thinkingMode || response.data.is_extended_thinking_mode_requested);\n        window.electronAPI.setLastThinkingModeValue((thinkingMode || response.data.is_extended_thinking_mode_requested).toString());\n        window.electronAPI.launchAIAgent(process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS, tid, backgroundMode || response.data.is_background_mode_requested);\n      }\n      // TODO Remove\n      getThread();\n      getThreadMessages();\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      setSendingMessage(false);\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        var _error$response$data;\n        if (((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) === 'Not_Browser_Task_BG_Mode') {\n          dispatch(setError(true, 'Background Mode only supports browser tasks.'));\n        } else {\n          dispatch(setError(true, 'Something Wrong Happened, Please try again.'));\n        }\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n  const deleteThread = () => {\n    dispatch(setLoadingDialog(true));\n    axios.delete('/threads/' + tid, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      dispatch(setLoadingDialog(false));\n      navigate('/');\n      window.location.reload();\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n        setTimeout(() => {\n          dispatch(setError(false, ''));\n        }, 3000);\n      }\n    });\n  };\n  const cancelRunningTask = () => {\n    if (thread.status !== 'working') {\n      return;\n    }\n    dispatch(setLoadingDialog(true));\n    axios.post(`/threads/${tid}/cancel_task`, {}, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      dispatch(setLoadingDialog(false));\n      window.electronAPI.stopAIAgent();\n      // TODO Remove\n      getThreadMessages();\n      getThread();\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n  const handleTextEnterKey = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  const onBGModeToggleChange = async value => {\n    if (value) {\n      const ready = await window.electronAPI.isBackgroundModeReady();\n      if (!ready) {\n        window.electronAPI.startBackgroundSetup();\n        return;\n      }\n    }\n    setBackgroundMode(value);\n  };\n  useEffect(() => {\n    getThread();\n    getThreadMessages();\n  }, [tid]);\n  useEffect(() => {\n    var _bottomRef$current;\n    (_bottomRef$current = bottomRef.current) === null || _bottomRef$current === void 0 ? void 0 : _bottomRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n  useEffect(() => {\n    var _window$electronAPI;\n    if ((_window$electronAPI = window.electronAPI) !== null && _window$electronAPI !== void 0 && _window$electronAPI.onAIAgentLaunch) {\n      window.electronAPI.onAIAgentLaunch(() => {\n        window.location.reload();\n      });\n    }\n  }, []);\n  useEffect(() => {\n    var _window$electronAPI2;\n    if ((_window$electronAPI2 = window.electronAPI) !== null && _window$electronAPI2 !== void 0 && _window$electronAPI2.onAIAgentExit) {\n      window.electronAPI.onAIAgentExit(() => {\n        getThread();\n        getThreadMessages();\n      });\n    }\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastBackgroundModeValue = await window.electronAPI.getLastBackgroundModeValue();\n      setBackgroundMode(lastBackgroundModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastThinkingModeValue = await window.electronAPI.getLastThinkingModeValue();\n      setThinkingMode(lastThinkingModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n  return thread !== null ? /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ThreadDialog, {\n      isOpen: isThreadDialogOpen,\n      setOpen: setThreadDialogOpen,\n      threadObj: Object.assign({}, thread),\n      onSuccess: () => window.location.reload()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(YesNoDialog, {\n      isOpen: isDeleteThreadDialogOpen,\n      setOpen: setDeleteThreadDialogOpen,\n      title: \"Delete Thread\",\n      text: \"Are you sure that you want to delete this thread?\",\n      onYesClicked: deleteThread,\n      isDarkMode: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ThreadDiv, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          fontSize: \"20px\",\n          fontWeight: \"600\",\n          color: '#fff',\n          children: thread.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FlexSpacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            iconSize: \"27px\",\n            color: \"#fff\",\n            style: {\n              margin: '0 5px'\n            },\n            dark: true,\n            onClick: () => setThreadDialogOpen(true),\n            children: /*#__PURE__*/_jsxDEV(MdEdit, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            iconSize: \"27px\",\n            color: \"#fff\",\n            style: {\n              margin: '0 5px'\n            },\n            dark: true,\n            onClick: () => setDeleteThreadDialogOpen(true),\n            children: /*#__PURE__*/_jsxDEV(MdDelete, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChatContainer, {\n        children: [messages.map(msg => /*#__PURE__*/_jsxDEV(ChatMessage, {\n          message: msg\n        }, 'thread_message__' + msg.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: bottomRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px'\n        },\n        children: /*#__PURE__*/_jsxDEV(SendingContainer, {\n          children: [/*#__PURE__*/_jsxDEV(NATextArea, {\n            background: \"transparent\",\n            placeholder: 'What do you want NeuralAgent to do?',\n            value: messageText,\n            isDarkMode: true,\n            rows: \"2\",\n            onKeyDown: handleTextEnterKey,\n            onChange: e => setMessageText(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '5px',\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ToggleContainer, {\n              children: /*#__PURE__*/_jsxDEV(ModeToggle, {\n                active: backgroundMode,\n                onClick: () => onBGModeToggleChange(!backgroundMode),\n                children: [/*#__PURE__*/_jsxDEV(MdOutlineSchedule, {\n                  style: {\n                    fontSize: '19px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this), \"Background\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToggleContainer, {\n              children: /*#__PURE__*/_jsxDEV(ModeToggle, {\n                active: thinkingMode,\n                onClick: () => setThinkingMode(!thinkingMode),\n                children: [/*#__PURE__*/_jsxDEV(GiBrain, {\n                  style: {\n                    fontSize: '19px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this), \"Thinking\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FlexSpacer, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), isSendingMessage ? /*#__PURE__*/_jsxDEV(ClipLoader, {\n              color: '#fff',\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this) : thread.status === 'working' ? /*#__PURE__*/_jsxDEV(IconButton, {\n              iconSize: \"35px\",\n              color: '#fff',\n              onClick: () => cancelRunningTask(),\n              children: /*#__PURE__*/_jsxDEV(FaStopCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(IconButton, {\n              iconSize: \"35px\",\n              color: '#fff',\n              disabled: messageText.length === 0,\n              onClick: () => sendMessage(),\n              children: /*#__PURE__*/_jsxDEV(FaArrowAltCircleUp, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n}\n_s(Thread, \"08IMjgcsVlbM6DACACV5KWwPG1Y=\", false, function () {\n  return [useSelector, useParams, useNavigate, useDispatch];\n});\n_c7 = Thread;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ThreadDiv\");\n$RefreshReg$(_c2, \"ChatContainer\");\n$RefreshReg$(_c3, \"SendingContainer\");\n$RefreshReg$(_c4, \"Header\");\n$RefreshReg$(_c5, \"ToggleContainer\");\n$RefreshReg$(_c6, \"ModeToggle\");\n$RefreshReg$(_c7, \"Thread\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useParams", "useSelector", "useDispatch", "axios", "constants", "setLoadingDialog", "setError", "ChatMessage", "FlexSpacer", "NATextArea", "IconButton", "MdEdit", "MdDelete", "FaArrowAltCircleUp", "FaStopCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text", "ThreadDialog", "YesNoDialog", "useNavigate", "MdOutlineSchedule", "GiBrain", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ThreadDiv", "div", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c2", "SendingContainer", "_c3", "Header", "_c4", "ToggleContainer", "_c5", "ModeToggle", "button", "active", "_c6", "<PERSON><PERSON><PERSON>", "_s", "thread", "setThread", "messages", "setMessages", "messageText", "setMessageText", "isSendingMessage", "setSendingMessage", "backgroundMode", "setBackgroundMode", "thinkingMode", "setThinkingMode", "isThreadDialogOpen", "setThreadDialogOpen", "isDeleteThreadDialogOpen", "setDeleteThreadDialogOpen", "accessToken", "state", "tid", "bottomRef", "navigate", "dispatch", "getThread", "get", "headers", "then", "response", "data", "catch", "error", "_error$response", "status", "UNAUTHORIZED", "window", "location", "reload", "getThreadMessages", "_error$response2", "sendMessage", "length", "text", "trim", "background_mode", "extended_thinking_mode", "post", "type", "is_background_mode_requested", "ready", "electronAPI", "isBackgroundModeReady", "cancelRunningTask", "is_extended_thinking_mode_requested", "setLastThinkingModeValue", "toString", "launchAIAgent", "process", "env", "REACT_APP_PROTOCOL", "REACT_APP_DNS", "BAD_REQUEST", "_error$response$data", "message", "GENERAL_ERROR", "setTimeout", "deleteThread", "delete", "stopAIAgent", "handleTextEnterKey", "e", "key", "shift<PERSON>ey", "preventDefault", "onBGModeToggleChange", "value", "startBackgroundSetup", "_bottomRef$current", "current", "scrollIntoView", "behavior", "_window$electronAPI", "onAIAgentLaunch", "_window$electronAPI2", "onAIAgentExit", "asyncTask", "lastBackgroundModeValue", "getLastBackgroundModeValue", "lastThinkingModeValue", "getLastThinkingModeValue", "children", "isOpen", "<PERSON><PERSON><PERSON>", "threadObj", "Object", "assign", "onSuccess", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onYesClicked", "isDarkMode", "fontSize", "fontWeight", "color", "style", "display", "alignItems", "iconSize", "margin", "dark", "onClick", "map", "msg", "id", "ref", "padding", "background", "placeholder", "rows", "onKeyDown", "onChange", "target", "marginTop", "width", "size", "disabled", "_c7", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/views/Thread.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport axios from '../utils/axios';\nimport constants from '../utils/constants';\nimport { setLoadingDialog, setError } from '../store';\nimport ChatMessage from '../components/ChatMessage';\nimport { FlexSpacer } from '../components/Elements/SmallElements';\nimport NATextArea from '../components/Elements/TextAreas';\nimport { IconButton } from '../components/Elements/Button';\nimport { MdEdit, MdDelete } from 'react-icons/md';\nimport { FaArrowAltCircleUp, FaStopCircle } from 'react-icons/fa';\nimport ClipLoader from 'react-spinners/ClipLoader';\nimport { Text } from '../components/Elements/Typography';\nimport ThreadDialog from '../components/DataDialogs/ThreadDialog';\nimport YesNoDialog from '../components/Elements/YesNoDialog';\nimport { useNavigate } from 'react-router-dom';\nimport { MdOutlineSchedule } from 'react-icons/md';\nimport { GiBrain } from 'react-icons/gi';\n\nimport styled from 'styled-components';\n\nconst ThreadDiv = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow: hidden;\n`;\n\nconst ChatContainer = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding-top: 12px;\n  padding-bottom: 12px;\n`;\n\nconst SendingContainer = styled.div`\n  border: thin solid rgba(255,255,255,0.3);\n  padding: 10px;\n  border-radius: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 20px;\n`;\n\nconst ToggleContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.9rem;\n  color: var(--secondary-color);\n`;\n\n\nconst ModeToggle = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  background-color: ${({ active }) => (active ? 'rgba(255,255,255,0.1)' : 'transparent')};\n  color: #fff;\n  border: thin solid rgba(255,255,255,0.3);\n  border-radius: 999px;\n  padding: 6px 12px;\n  font-size: 13px;\n  transition: background-color 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255,255,255,0.1);\n  }\n`;\n\nexport default function Thread() {\n  \n  const [thread, setThread] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [isSendingMessage, setSendingMessage] = useState(false);\n  const [backgroundMode, setBackgroundMode] = useState(false);\n  const [thinkingMode, setThinkingMode] = useState(false);\n\n  const [isThreadDialogOpen, setThreadDialogOpen] = useState(false);\n  const [isDeleteThreadDialogOpen, setDeleteThreadDialogOpen] = useState(false);\n\n  const accessToken = useSelector(state => state.accessToken);\n\n  const { tid } = useParams();\n\n  const bottomRef = useRef(null);\n\n  const navigate = useNavigate();\n\n  const dispatch = useDispatch();\n\n  const getThread = () => {\n    dispatch(setLoadingDialog(true));\n    axios.get(`/threads/${tid}`, {\n      headers: { 'Authorization': 'Bearer ' + accessToken }\n    }).then(response => {\n      setThread(response.data);\n      dispatch(setLoadingDialog(false));\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response?.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n\n  const getThreadMessages = () => {\n    dispatch(setLoadingDialog(true));\n    axios.get(`/threads/${tid}/thread_messages`, {\n      headers: { 'Authorization': 'Bearer ' + accessToken }\n    }).then(response => {\n      setMessages(response.data);\n      dispatch(setLoadingDialog(false));\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response?.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n\n  const sendMessage = () => {\n    if (messageText.length === 0 || isSendingMessage || thread.status === 'working') {\n      return;\n    }\n    \n    const data = {text: messageText.trim(), background_mode: backgroundMode, extended_thinking_mode: thinkingMode};\n    setMessageText('');\n    setSendingMessage(true);\n    dispatch(setLoadingDialog(true));\n    axios.post(`/threads/${tid}/send_message`, data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then(async (response) => {\n      dispatch(setLoadingDialog(false));\n      setSendingMessage(false);\n      if (response.data.type === 'desktop_task') {\n        if (!backgroundMode && response.data.is_background_mode_requested) {\n          const ready = await window.electronAPI.isBackgroundModeReady();\n          if (!ready) {\n            cancelRunningTask();\n            return;\n          }\n        }\n        setBackgroundMode(backgroundMode || response.data.is_background_mode_requested);\n        setThinkingMode(thinkingMode || response.data.is_extended_thinking_mode_requested);\n        window.electronAPI.setLastThinkingModeValue((thinkingMode || response.data.is_extended_thinking_mode_requested).toString());\n        window.electronAPI.launchAIAgent(\n          process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS,\n          tid,\n          backgroundMode || response.data.is_background_mode_requested\n        );\n      }\n      // TODO Remove\n      getThread();\n      getThreadMessages();\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      setSendingMessage(false);\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        if (error.response.data?.message === 'Not_Browser_Task_BG_Mode') {\n          dispatch(setError(true, 'Background Mode only supports browser tasks.'));\n        } else {\n          dispatch(setError(true, 'Something Wrong Happened, Please try again.'));\n        }\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n\n  const deleteThread = () => {\n    dispatch(setLoadingDialog(true));\n    axios.delete('/threads/' + tid, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      dispatch(setLoadingDialog(false));\n      navigate('/');\n      window.location.reload();\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n        setTimeout(() => {\n          dispatch(setError(false, ''));\n        }, 3000);\n      }\n    });\n  }\n\n  const cancelRunningTask = () => {\n    if (thread.status !== 'working') {\n      return;\n    }\n\n    dispatch(setLoadingDialog(true));\n    axios.post(`/threads/${tid}/cancel_task`, {}, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      dispatch(setLoadingDialog(false));\n      window.electronAPI.stopAIAgent();\n      // TODO Remove\n      getThreadMessages();\n      getThread();\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n\n  const handleTextEnterKey = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const onBGModeToggleChange = async (value) => {\n    if (value) {\n      const ready = await window.electronAPI.isBackgroundModeReady();\n      if (!ready) {\n        window.electronAPI.startBackgroundSetup();\n        return;\n      }\n    }\n    setBackgroundMode(value);\n  };\n\n  useEffect(() => {\n    getThread();\n    getThreadMessages();\n  }, [tid]);\n\n  useEffect(() => {\n    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  useEffect(() => {\n    if (window.electronAPI?.onAIAgentLaunch) {\n      window.electronAPI.onAIAgentLaunch(() => {\n        window.location.reload();\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    if (window.electronAPI?.onAIAgentExit) {\n      window.electronAPI.onAIAgentExit(() => {\n        getThread();\n        getThreadMessages();\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastBackgroundModeValue = await window.electronAPI.getLastBackgroundModeValue();\n      setBackgroundMode(lastBackgroundModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastThinkingModeValue = await window.electronAPI.getLastThinkingModeValue();\n      setThinkingMode(lastThinkingModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n\n  return thread !== null ? (\n    <>\n      <ThreadDialog\n        isOpen={isThreadDialogOpen}\n        setOpen={setThreadDialogOpen}\n        threadObj={Object.assign({}, thread)}\n        onSuccess={() => window.location.reload()}\n      />\n      <YesNoDialog\n        isOpen={isDeleteThreadDialogOpen}\n        setOpen={setDeleteThreadDialogOpen}\n        title='Delete Thread'\n        text='Are you sure that you want to delete this thread?'\n        onYesClicked={deleteThread}\n        isDarkMode={true}\n      />\n      <ThreadDiv>\n        <Header>\n          <Text fontSize='20px' fontWeight='600' color={'#fff'}>\n            {thread.title}\n          </Text>\n          <FlexSpacer />\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <IconButton iconSize='27px' color='#fff' style={{ margin: '0 5px' }} dark\n              onClick={() => setThreadDialogOpen(true)}>\n              <MdEdit />\n            </IconButton>\n            <IconButton iconSize='27px' color='#fff' style={{ margin: '0 5px' }} dark\n              onClick={() => setDeleteThreadDialogOpen(true)}>\n              <MdDelete />\n            </IconButton>\n          </div>\n        </Header>\n        <ChatContainer>\n          {messages.map((msg) => (\n            <ChatMessage key={'thread_message__' + msg.id} message={msg} />\n          ))}\n          <div ref={bottomRef} />\n        </ChatContainer>\n        <div style={{ padding: '15px' }}>\n          <SendingContainer>\n            <NATextArea\n              background='transparent'\n              placeholder={'What do you want NeuralAgent to do?'}\n              value={messageText}\n              isDarkMode\n              rows='2'\n              onKeyDown={handleTextEnterKey}\n              onChange={(e) => setMessageText(e.target.value)}\n            />\n            <div style={{ marginTop: '5px', display: 'flex', alignItems: 'center' }}>\n              <ToggleContainer>\n                <ModeToggle\n                  active={backgroundMode}\n                  onClick={() => onBGModeToggleChange(!backgroundMode)}\n                >\n                  <MdOutlineSchedule style={{fontSize: '19px'}} />\n                  Background\n                </ModeToggle>\n              </ToggleContainer>\n              <div style={{width: '10px'}} />\n              <ToggleContainer>\n                <ModeToggle\n                  active={thinkingMode}\n                  onClick={() => setThinkingMode(!thinkingMode)}\n                >\n                  <GiBrain style={{fontSize: '19px'}} />\n                  Thinking\n                </ModeToggle>\n              </ToggleContainer>\n              <FlexSpacer />\n              {isSendingMessage ? (\n                <ClipLoader color={'#fff'} size={40} />\n              ) : (\n                thread.status === 'working' ? (\n                  <IconButton\n                    iconSize='35px'\n                    color={'#fff'}\n                    onClick={() => cancelRunningTask()}>\n                    <FaStopCircle />\n                  </IconButton>\n                ) : (\n                  <IconButton\n                    iconSize='35px'\n                    color={'#fff'}\n                    disabled={messageText.length === 0}\n                    onClick={() => sendMessage()}>\n                    <FaArrowAltCircleUp />\n                  </IconButton>\n                )\n              )}\n            </div>\n          </SendingContainer>\n        </div>\n      </ThreadDiv>\n    </>\n  ) : <></>;\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,gBAAgB,EAAEC,QAAQ,QAAQ,UAAU;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,UAAU,QAAQ,sCAAsC;AACjE,OAAOC,UAAU,MAAM,kCAAkC;AACzD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AACjD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,gBAAgB;AACjE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,SAASC,IAAI,QAAQ,mCAAmC;AACxD,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,gBAAgB;AAExC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,SAAS,GAAGL,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,SAAS;AAQf,MAAMG,aAAa,GAAGR,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,aAAa;AAOnB,MAAME,gBAAgB,GAAGV,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,gBAAgB;AAMtB,MAAME,MAAM,GAAGZ,MAAM,CAACM,GAAG;AACzB;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,MAAM;AAMZ,MAAME,eAAe,GAAGd,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GANID,eAAe;AASrB,MAAME,UAAU,GAAGhB,MAAM,CAACiB,MAAM;AAChC;AACA;AACA;AACA,sBAAsB,CAAC;EAAEC;AAAO,CAAC,KAAMA,MAAM,GAAG,uBAAuB,GAAG,aAAc;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhBIH,UAAU;AAkBhB,eAAe,SAASI,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAE/B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqD,gBAAgB,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM,CAAC2D,kBAAkB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6D,wBAAwB,EAAEC,yBAAyB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAE7E,MAAM+D,WAAW,GAAG3D,WAAW,CAAC4D,KAAK,IAAIA,KAAK,CAACD,WAAW,CAAC;EAE3D,MAAM;IAAEE;EAAI,CAAC,GAAG9D,SAAS,CAAC,CAAC;EAE3B,MAAM+D,SAAS,GAAGhE,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMiE,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAE9B,MAAM8C,QAAQ,GAAG/D,WAAW,CAAC,CAAC;EAE9B,MAAMgE,SAAS,GAAGA,CAAA,KAAM;IACtBD,QAAQ,CAAC5D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCF,KAAK,CAACgE,GAAG,CAAC,YAAYL,GAAG,EAAE,EAAE;MAC3BM,OAAO,EAAE;QAAE,eAAe,EAAE,SAAS,GAAGR;MAAY;IACtD,CAAC,CAAC,CAACS,IAAI,CAACC,QAAQ,IAAI;MAClBzB,SAAS,CAACyB,QAAQ,CAACC,IAAI,CAAC;MACxBN,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC,CAACmE,KAAK,CAACC,KAAK,IAAI;MAAA,IAAAC,eAAA;MAChBT,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAI,EAAAqE,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAKvE,SAAS,CAACuE,MAAM,CAACC,YAAY,EAAE;QAC5DC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bf,QAAQ,CAAC5D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCF,KAAK,CAACgE,GAAG,CAAC,YAAYL,GAAG,kBAAkB,EAAE;MAC3CM,OAAO,EAAE;QAAE,eAAe,EAAE,SAAS,GAAGR;MAAY;IACtD,CAAC,CAAC,CAACS,IAAI,CAACC,QAAQ,IAAI;MAClBvB,WAAW,CAACuB,QAAQ,CAACC,IAAI,CAAC;MAC1BN,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC,CAACmE,KAAK,CAACC,KAAK,IAAI;MAAA,IAAAQ,gBAAA;MAChBhB,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAI,EAAA4E,gBAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgBN,MAAM,MAAKvE,SAAS,CAACuE,MAAM,CAACC,YAAY,EAAE;QAC5DC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIlC,WAAW,CAACmC,MAAM,KAAK,CAAC,IAAIjC,gBAAgB,IAAIN,MAAM,CAAC+B,MAAM,KAAK,SAAS,EAAE;MAC/E;IACF;IAEA,MAAMJ,IAAI,GAAG;MAACa,IAAI,EAAEpC,WAAW,CAACqC,IAAI,CAAC,CAAC;MAAEC,eAAe,EAAElC,cAAc;MAAEmC,sBAAsB,EAAEjC;IAAY,CAAC;IAC9GL,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;IACvBc,QAAQ,CAAC5D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCF,KAAK,CAACqF,IAAI,CAAC,YAAY1B,GAAG,eAAe,EAAES,IAAI,EAAE;MAC/CH,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGR;MAC/B;IACF,CAAC,CAAC,CAACS,IAAI,CAAC,MAAOC,QAAQ,IAAK;MAC1BL,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC8C,iBAAiB,CAAC,KAAK,CAAC;MACxB,IAAImB,QAAQ,CAACC,IAAI,CAACkB,IAAI,KAAK,cAAc,EAAE;QACzC,IAAI,CAACrC,cAAc,IAAIkB,QAAQ,CAACC,IAAI,CAACmB,4BAA4B,EAAE;UACjE,MAAMC,KAAK,GAAG,MAAMd,MAAM,CAACe,WAAW,CAACC,qBAAqB,CAAC,CAAC;UAC9D,IAAI,CAACF,KAAK,EAAE;YACVG,iBAAiB,CAAC,CAAC;YACnB;UACF;QACF;QACAzC,iBAAiB,CAACD,cAAc,IAAIkB,QAAQ,CAACC,IAAI,CAACmB,4BAA4B,CAAC;QAC/EnC,eAAe,CAACD,YAAY,IAAIgB,QAAQ,CAACC,IAAI,CAACwB,mCAAmC,CAAC;QAClFlB,MAAM,CAACe,WAAW,CAACI,wBAAwB,CAAC,CAAC1C,YAAY,IAAIgB,QAAQ,CAACC,IAAI,CAACwB,mCAAmC,EAAEE,QAAQ,CAAC,CAAC,CAAC;QAC3HpB,MAAM,CAACe,WAAW,CAACM,aAAa,CAC9BC,OAAO,CAACC,GAAG,CAACC,kBAAkB,GAAG,KAAK,GAAGF,OAAO,CAACC,GAAG,CAACE,aAAa,EAClExC,GAAG,EACHV,cAAc,IAAIkB,QAAQ,CAACC,IAAI,CAACmB,4BAClC,CAAC;MACH;MACA;MACAxB,SAAS,CAAC,CAAC;MACXc,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,CAACR,KAAK,CAAEC,KAAK,IAAK;MAClBR,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC8C,iBAAiB,CAAC,KAAK,CAAC;MACxB,IAAIsB,KAAK,CAACH,QAAQ,CAACK,MAAM,KAAKvE,SAAS,CAACuE,MAAM,CAAC4B,WAAW,EAAE;QAAA,IAAAC,oBAAA;QAC1D,IAAI,EAAAA,oBAAA,GAAA/B,KAAK,CAACH,QAAQ,CAACC,IAAI,cAAAiC,oBAAA,uBAAnBA,oBAAA,CAAqBC,OAAO,MAAK,0BAA0B,EAAE;UAC/DxC,QAAQ,CAAC3D,QAAQ,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;QAC1E,CAAC,MAAM;UACL2D,QAAQ,CAAC3D,QAAQ,CAAC,IAAI,EAAE,6CAA6C,CAAC,CAAC;QACzE;MACF,CAAC,MAAM;QACL2D,QAAQ,CAAC3D,QAAQ,CAAC,IAAI,EAAEF,SAAS,CAACsG,aAAa,CAAC,CAAC;MACnD;MACAC,UAAU,CAAC,MAAM;QACf1C,QAAQ,CAAC3D,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsG,YAAY,GAAGA,CAAA,KAAM;IACzB3C,QAAQ,CAAC5D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCF,KAAK,CAAC0G,MAAM,CAAC,WAAW,GAAG/C,GAAG,EAAE;MAC9BM,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGR;MAC/B;IACF,CAAC,CAAC,CAACS,IAAI,CAAEC,QAAQ,IAAK;MACpBL,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC2D,QAAQ,CAAC,GAAG,CAAC;MACba,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC,CAACP,KAAK,CAAEC,KAAK,IAAK;MAClBR,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAIoE,KAAK,CAACH,QAAQ,CAACK,MAAM,KAAKvE,SAAS,CAACuE,MAAM,CAACC,YAAY,EAAE;QAC3DC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLd,QAAQ,CAAC3D,QAAQ,CAAC,IAAI,EAAEF,SAAS,CAACsG,aAAa,CAAC,CAAC;QACjDC,UAAU,CAAC,MAAM;UACf1C,QAAQ,CAAC3D,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC/B,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIlD,MAAM,CAAC+B,MAAM,KAAK,SAAS,EAAE;MAC/B;IACF;IAEAV,QAAQ,CAAC5D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCF,KAAK,CAACqF,IAAI,CAAC,YAAY1B,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE;MAC5CM,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGR;MAC/B;IACF,CAAC,CAAC,CAACS,IAAI,CAAEC,QAAQ,IAAK;MACpBL,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjCwE,MAAM,CAACe,WAAW,CAACkB,WAAW,CAAC,CAAC;MAChC;MACA9B,iBAAiB,CAAC,CAAC;MACnBd,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,CAACM,KAAK,CAAEC,KAAK,IAAK;MAClBR,QAAQ,CAAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAIoE,KAAK,CAACH,QAAQ,CAACK,MAAM,KAAKvE,SAAS,CAACuE,MAAM,CAAC4B,WAAW,EAAE;QAC1DtC,QAAQ,CAAC3D,QAAQ,CAAC,IAAI,EAAEF,SAAS,CAACsG,aAAa,CAAC,CAAC;MACnD,CAAC,MAAM;QACLzC,QAAQ,CAAC3D,QAAQ,CAAC,IAAI,EAAEF,SAAS,CAACsG,aAAa,CAAC,CAAC;MACnD;MACAC,UAAU,CAAC,MAAM;QACf1C,QAAQ,CAAC3D,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyG,kBAAkB,GAAIC,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBjC,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAG,MAAOC,KAAK,IAAK;IAC5C,IAAIA,KAAK,EAAE;MACT,MAAM1B,KAAK,GAAG,MAAMd,MAAM,CAACe,WAAW,CAACC,qBAAqB,CAAC,CAAC;MAC9D,IAAI,CAACF,KAAK,EAAE;QACVd,MAAM,CAACe,WAAW,CAAC0B,oBAAoB,CAAC,CAAC;QACzC;MACF;IACF;IACAjE,iBAAiB,CAACgE,KAAK,CAAC;EAC1B,CAAC;EAEDvH,SAAS,CAAC,MAAM;IACdoE,SAAS,CAAC,CAAC;IACXc,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAClB,GAAG,CAAC,CAAC;EAEThE,SAAS,CAAC,MAAM;IAAA,IAAAyH,kBAAA;IACd,CAAAA,kBAAA,GAAAxD,SAAS,CAACyD,OAAO,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC3D,CAAC,EAAE,CAAC5E,QAAQ,CAAC,CAAC;EAEdhD,SAAS,CAAC,MAAM;IAAA,IAAA6H,mBAAA;IACd,KAAAA,mBAAA,GAAI9C,MAAM,CAACe,WAAW,cAAA+B,mBAAA,eAAlBA,mBAAA,CAAoBC,eAAe,EAAE;MACvC/C,MAAM,CAACe,WAAW,CAACgC,eAAe,CAAC,MAAM;QACvC/C,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAENjF,SAAS,CAAC,MAAM;IAAA,IAAA+H,oBAAA;IACd,KAAAA,oBAAA,GAAIhD,MAAM,CAACe,WAAW,cAAAiC,oBAAA,eAAlBA,oBAAA,CAAoBC,aAAa,EAAE;MACrCjD,MAAM,CAACe,WAAW,CAACkC,aAAa,CAAC,MAAM;QACrC5D,SAAS,CAAC,CAAC;QACXc,iBAAiB,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAENlF,SAAS,CAAC,MAAM;IACd,MAAMiI,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,uBAAuB,GAAG,MAAMnD,MAAM,CAACe,WAAW,CAACqC,0BAA0B,CAAC,CAAC;MACrF5E,iBAAiB,CAAC2E,uBAAuB,KAAK,MAAM,CAAC;IACvD,CAAC;IACDD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAENjI,SAAS,CAAC,MAAM;IACd,MAAMiI,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMG,qBAAqB,GAAG,MAAMrD,MAAM,CAACe,WAAW,CAACuC,wBAAwB,CAAC,CAAC;MACjF5E,eAAe,CAAC2E,qBAAqB,KAAK,MAAM,CAAC;IACnD,CAAC;IACDH,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOnF,MAAM,KAAK,IAAI,gBACpBpB,OAAA,CAAAE,SAAA;IAAA0G,QAAA,gBACE5G,OAAA,CAACP,YAAY;MACXoH,MAAM,EAAE7E,kBAAmB;MAC3B8E,OAAO,EAAE7E,mBAAoB;MAC7B8E,SAAS,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7F,MAAM,CAAE;MACrC8F,SAAS,EAAEA,CAAA,KAAM7D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAAE;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eACFtH,OAAA,CAACN,WAAW;MACVmH,MAAM,EAAE3E,wBAAyB;MACjC4E,OAAO,EAAE3E,yBAA0B;MACnCoF,KAAK,EAAC,eAAe;MACrB3D,IAAI,EAAC,mDAAmD;MACxD4D,YAAY,EAAEpC,YAAa;MAC3BqC,UAAU,EAAE;IAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eACFtH,OAAA,CAACG,SAAS;MAAAyG,QAAA,gBACR5G,OAAA,CAACU,MAAM;QAAAkG,QAAA,gBACL5G,OAAA,CAACR,IAAI;UAACkI,QAAQ,EAAC,MAAM;UAACC,UAAU,EAAC,KAAK;UAACC,KAAK,EAAE,MAAO;UAAAhB,QAAA,EAClDxF,MAAM,CAACmG;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPtH,OAAA,CAAChB,UAAU;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdtH,OAAA;UAAK6H,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAnB,QAAA,gBACpD5G,OAAA,CAACd,UAAU;YAAC8I,QAAQ,EAAC,MAAM;YAACJ,KAAK,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEI,MAAM,EAAE;YAAQ,CAAE;YAACC,IAAI;YACvEC,OAAO,EAAEA,CAAA,KAAMlG,mBAAmB,CAAC,IAAI,CAAE;YAAA2E,QAAA,eACzC5G,OAAA,CAACb,MAAM;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACbtH,OAAA,CAACd,UAAU;YAAC8I,QAAQ,EAAC,MAAM;YAACJ,KAAK,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEI,MAAM,EAAE;YAAQ,CAAE;YAACC,IAAI;YACvEC,OAAO,EAAEA,CAAA,KAAMhG,yBAAyB,CAAC,IAAI,CAAE;YAAAyE,QAAA,eAC/C5G,OAAA,CAACZ,QAAQ;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACTtH,OAAA,CAACM,aAAa;QAAAsG,QAAA,GACXtF,QAAQ,CAAC8G,GAAG,CAAEC,GAAG,iBAChBrI,OAAA,CAACjB,WAAW;UAAmCkG,OAAO,EAAEoD;QAAI,GAA1C,kBAAkB,GAAGA,GAAG,CAACC,EAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAC/D,CAAC,eACFtH,OAAA;UAAKuI,GAAG,EAAEhG;QAAU;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAChBtH,OAAA;QAAK6H,KAAK,EAAE;UAAEW,OAAO,EAAE;QAAO,CAAE;QAAA5B,QAAA,eAC9B5G,OAAA,CAACQ,gBAAgB;UAAAoG,QAAA,gBACf5G,OAAA,CAACf,UAAU;YACTwJ,UAAU,EAAC,aAAa;YACxBC,WAAW,EAAE,qCAAsC;YACnD7C,KAAK,EAAErE,WAAY;YACnBiG,UAAU;YACVkB,IAAI,EAAC,GAAG;YACRC,SAAS,EAAErD,kBAAmB;YAC9BsD,QAAQ,EAAGrD,CAAC,IAAK/D,cAAc,CAAC+D,CAAC,CAACsD,MAAM,CAACjD,KAAK;UAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACFtH,OAAA;YAAK6H,KAAK,EAAE;cAAEkB,SAAS,EAAE,KAAK;cAAEjB,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAnB,QAAA,gBACtE5G,OAAA,CAACY,eAAe;cAAAgG,QAAA,eACd5G,OAAA,CAACc,UAAU;gBACTE,MAAM,EAAEY,cAAe;gBACvBuG,OAAO,EAAEA,CAAA,KAAMvC,oBAAoB,CAAC,CAAChE,cAAc,CAAE;gBAAAgF,QAAA,gBAErD5G,OAAA,CAACJ,iBAAiB;kBAACiI,KAAK,EAAE;oBAACH,QAAQ,EAAE;kBAAM;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAClBtH,OAAA;cAAK6H,KAAK,EAAE;gBAACmB,KAAK,EAAE;cAAM;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BtH,OAAA,CAACY,eAAe;cAAAgG,QAAA,eACd5G,OAAA,CAACc,UAAU;gBACTE,MAAM,EAAEc,YAAa;gBACrBqG,OAAO,EAAEA,CAAA,KAAMpG,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAA8E,QAAA,gBAE9C5G,OAAA,CAACH,OAAO;kBAACgI,KAAK,EAAE;oBAACH,QAAQ,EAAE;kBAAM;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAClBtH,OAAA,CAAChB,UAAU;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACb5F,gBAAgB,gBACf1B,OAAA,CAACT,UAAU;cAACqI,KAAK,EAAE,MAAO;cAACqB,IAAI,EAAE;YAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAEvClG,MAAM,CAAC+B,MAAM,KAAK,SAAS,gBACzBnD,OAAA,CAACd,UAAU;cACT8I,QAAQ,EAAC,MAAM;cACfJ,KAAK,EAAE,MAAO;cACdO,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAAC,CAAE;cAAAsC,QAAA,eACnC5G,OAAA,CAACV,YAAY;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAEbtH,OAAA,CAACd,UAAU;cACT8I,QAAQ,EAAC,MAAM;cACfJ,KAAK,EAAE,MAAO;cACdsB,QAAQ,EAAE1H,WAAW,CAACmC,MAAM,KAAK,CAAE;cACnCwE,OAAO,EAAEA,CAAA,KAAMzE,WAAW,CAAC,CAAE;cAAAkD,QAAA,eAC7B5G,OAAA,CAACX,kBAAkB;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAEf;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA,eACZ,CAAC,gBACDtH,OAAA,CAAAE,SAAA,mBAAI,CAAC;AACX;AAACiB,EAAA,CA3TuBD,MAAM;EAAA,QAYRzC,WAAW,EAEfD,SAAS,EAIRmB,WAAW,EAEXjB,WAAW;AAAA;AAAAyK,GAAA,GApBNjI,MAAM;AAAA,IAAAb,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAkI,GAAA;AAAAC,YAAA,CAAA/I,EAAA;AAAA+I,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}