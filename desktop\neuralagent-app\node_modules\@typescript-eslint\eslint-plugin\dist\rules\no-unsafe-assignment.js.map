{"version": 3, "file": "no-unsafe-assignment.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-assignment.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AAGnC,8CAAgC;AAChC,kCAA4C;AAW5C,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,wEAAwE;YAC1E,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,aAAa,EAAE,sCAAsC;YACrD,iBAAiB,EAAE;gBACjB,gEAAgE;gBAChE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,kBAAkB,EAAE,qDAAqD;YACzE,2BAA2B,EACzB,oEAAoE;YACtE,gBAAgB,EACd,0EAA0E;YAC5E,iBAAiB,EAAE,8CAA8C;SAClE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACrD,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,0CAA0C;QAC1C,SAAS,2BAA2B,CAClC,YAA2B,EAC3B,UAAyB;YAEzB,IAAI,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE;gBACrD,OAAO,KAAK,CAAC;aACd;YAED,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE3D,OAAO,qBAAqB,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QACvE,CAAC;QAED,0CAA0C;QAC1C,SAAS,qBAAqB,CAC5B,YAAmC,EACnC,UAAmB,EACnB,UAAmB;YAEnB,YAAY;YACZ,6BAA6B;YAC7B,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;gBAChD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,YAAY;oBAClB,SAAS,EAAE,oBAAoB;iBAChC,CAAC,CAAC;gBACH,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;gBACpC,OAAO,IAAI,CAAC;aACb;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAEjE,iBAAiB;YACjB,0BAA0B;YAC1B,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,KACE,IAAI,aAAa,GAAG,CAAC,EACrB,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,EAC5C,aAAa,IAAI,CAAC,EAClB;gBACA,MAAM,eAAe,GAAG,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC7D,IAAI,CAAC,eAAe,EAAE;oBACpB,SAAS;iBACV;gBAED,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE;oBACvD,qDAAqD;oBACrD,SAAS;iBACV;gBAED,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAwB,CAAC;gBACvE,IAAI,CAAC,UAAU,EAAE;oBACf,SAAS;iBACV;gBAED,gEAAgE;gBAChE,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;oBAClC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,eAAe;wBACrB,SAAS,EAAE,6BAA6B;qBACzC,CAAC,CAAC;oBACH,0DAA0D;oBAC1D,SAAS,GAAG,IAAI,CAAC;iBAClB;qBAAM,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE;oBAC/D,SAAS,GAAG,qBAAqB,CAC/B,eAAe,EACf,UAAU,EACV,UAAU,CACX,CAAC;iBACH;qBAAM,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE;oBAChE,SAAS,GAAG,sBAAsB,CAChC,eAAe,EACf,UAAU,EACV,UAAU,CACX,CAAC;iBACH;aACF;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,0CAA0C;QAC1C,SAAS,4BAA4B,CACnC,YAA2B,EAC3B,UAAyB;YAEzB,IAAI,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE;gBACtD,OAAO,KAAK,CAAC;aACd;YAED,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE3D,OAAO,sBAAsB,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QACxE,CAAC;QAED,0CAA0C;QAC1C,SAAS,sBAAsB,CAC7B,YAAoC,EACpC,UAAmB,EACnB,UAAmB;YAEnB,MAAM,UAAU,GAAG,IAAI,GAAG,CACxB,UAAU;iBACP,aAAa,EAAE;iBACf,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACf,QAAQ,CAAC,OAAO,EAAE;gBAClB,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,UAAU,CAAC;aACxD,CAAC,CACL,CAAC;YAEF,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,KAAK,MAAM,gBAAgB,IAAI,YAAY,CAAC,UAAU,EAAE;gBACtD,IAAI,gBAAgB,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE;oBACxD,6BAA6B;oBAC7B,SAAS;iBACV;gBAED,IAAI,GAAW,CAAC;gBAChB,IAAI,gBAAgB,CAAC,QAAQ,KAAK,KAAK,EAAE;oBACvC,GAAG;wBACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BACrD,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI;4BAC3B,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC1C;qBAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;oBAC/D,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC1C;qBAAM,IACL,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBAC5D,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxC;oBACA,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;iBAC3D;qBAAM;oBACL,wCAAwC;oBACxC,SAAS;iBACV;gBAED,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACvC,IAAI,CAAC,UAAU,EAAE;oBACf,SAAS;iBACV;gBAED,uEAAuE;gBACvE,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;oBAClC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,gBAAgB,CAAC,KAAK;wBAC5B,SAAS,EAAE,6BAA6B;qBACzC,CAAC,CAAC;oBACH,SAAS,GAAG,IAAI,CAAC;iBAClB;qBAAM,IACL,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAC3D;oBACA,SAAS,GAAG,qBAAqB,CAC/B,gBAAgB,CAAC,KAAK,EACtB,UAAU,EACV,UAAU,CACX,CAAC;iBACH;qBAAM,IACL,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAC5D;oBACA,SAAS,GAAG,sBAAsB,CAChC,gBAAgB,CAAC,KAAK,EACtB,UAAU,EACV,UAAU,CACX,CAAC;iBACH;aACF;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,0CAA0C;QAC1C,SAAS,eAAe,CACtB,YAA2B,EAC3B,UAA+B,EAC/B,aAA4B,EAC5B,cAA8B;;YAE9B,MAAM,cAAc,GAAG,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,YAAY,GAChB,cAAc,sCAA8B;gBAC1C,CAAC,CAAC,MAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAA+B,CAAC,mCAChE,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC3C,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAC1C,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CACtC,CAAC;YAEF,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;gBAClC,+CAA+C;gBAC/C,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;oBACxC,OAAO,KAAK,CAAC;iBACd;gBAED,IAAI,SAAS,GAA0C,eAAe,CAAC;gBAEvE,IAAI,CAAC,gBAAgB,EAAE;oBACrB,mBAAmB;oBACnB,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,UAAU,CAAC,CAAC;oBACrD,IACE,cAAc;wBACd,IAAI,CAAC,aAAa,CAChB,IAAI,CAAC,4BAA4B,CAC/B,OAAO,EACP,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,CAC1C,CACF,EACD;wBACA,SAAS,GAAG,mBAAmB,CAAC;qBACjC;iBACF;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,SAAS;iBACV,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;aACb;YAED,IAAI,cAAc,gCAAwB,EAAE;gBAC1C,OAAO,KAAK,CAAC;aACd;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CACpC,UAAU,EACV,YAAY,EACZ,OAAO,EACP,UAAU,CACX,CAAC;YACF,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,KAAK,CAAC;aACd;YAED,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YACpC,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,kBAAkB;gBAC7B,IAAI,EAAE;oBACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;oBACpC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC;iBACzC;aACF,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,iBAAiB,CACxB,cAAqD;YAErD,OAAO,cAAc;gBACnB,CAAC,CAAC,uDAAuD;;gBAEzD,CAAC,CAAC,iFAAiF;+CAC9D,CAAC;QAC1B,CAAC;QAED,OAAO;YACL,kCAAkC,CAChC,IAAiC;gBAEjC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAC1B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CACvD,CAAC;gBACF,IAAI,SAAS,GAAG,eAAe,CAC7B,IAAI,CAAC,EAAE,EACP,IAAI,EACJ,IAAI,EACJ,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,CAC1C,CAAC;gBAEF,IAAI,CAAC,SAAS,EAAE;oBACd,SAAS,GAAG,2BAA2B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBACxD;gBACD,IAAI,CAAC,SAAS,EAAE;oBACd,4BAA4B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBAC7C;YACH,CAAC;YACD,mCAAmC,CACjC,IAAiC;gBAEjC,eAAe,CACb,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,KAAM,EACX,IAAI,EACJ,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CACvC,CAAC;YACJ,CAAC;YACD,yDAAyD,CACvD,IAAgE;gBAEhE,IAAI,SAAS,GAAG,eAAe,CAC7B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,EACV,IAAI,+BAGL,CAAC;gBAEF,IAAI,CAAC,SAAS,EAAE;oBACd,SAAS,GAAG,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;iBAChE;gBACD,IAAI,CAAC,SAAS,EAAE;oBACd,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;iBACrD;YACH,CAAC;YACD,mDAAmD;YACnD,gCAAgC,CAAC,IAAuB;gBACtD,IACE,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACpD,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,6BAA6B,EAChE;oBACA,4BAA4B;oBAC5B,OAAO;iBACR;gBAED,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,oCAA4B,CAAC;YACzE,CAAC;YACD,iCAAiC,CAAC,IAA4B;gBAC5D,MAAM,SAAS,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBACtD,IACE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAC5B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,EAC1C;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,IAAI;wBACV,SAAS,EAAE,mBAAmB;qBAC/B,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,6BAA6B,CAAC,IAA2B;gBACvD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAC3B,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CACxD,CAAC;gBACF,IACE,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;oBACpD,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC3D;oBACA,OAAO;iBACR;gBAED,eAAe,CACb,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,UAAU,EAChB,KAAK,CAAC,UAAU,oCAEjB,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}