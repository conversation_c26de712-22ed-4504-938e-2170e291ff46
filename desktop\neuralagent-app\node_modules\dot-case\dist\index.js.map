{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,mCAA0C;AAI1C,SAAgB,OAAO,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC1D,OAAO,gBAAM,CAAC,KAAK,qBACjB,SAAS,EAAE,GAAG,IACX,OAAO,EACV,CAAC;AACL,CAAC;AALD,0BAKC", "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function dotCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options,\n  });\n}\n"]}