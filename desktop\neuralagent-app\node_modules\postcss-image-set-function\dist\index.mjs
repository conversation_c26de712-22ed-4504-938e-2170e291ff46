import e from"postcss-value-parser";const t=/^(cross-fade|image|(repeating-)?(conic|linear|radial)-gradient|url|var)$/i;function n(n){return!(!n||!n.type)&&("string"===n.type?"url("+e.stringify(n)+")":!("function"!==n.type||!t.test(n.value.toLowerCase()))&&e.stringify(n))}const r={dpcm:2.54,dpi:1,dppx:96,x:96};function i(e,t,n){if("boolean"==typeof e)return!1;const i=Math.floor(e/r.x*100)/100;return t.atRule({name:"media",params:`(-webkit-min-device-pixel-ratio: ${i}), (min-resolution: ${e}dpi)`,source:n.source})}function o(t){if(!t)return!1;if("word"!==t.type)return!1;if(!function(t){if(!t||!t.value)return!1;try{return!1!==e.unit(t.value)}catch(e){return!1}}(t))return!1;const n=e.unit(t.value);return!!n&&(n.unit.toLowerCase()in r&&Number(n.number)*r[n.unit.toLowerCase()])}const s=(e,t,n)=>{if("warn"===e.oninvalid)e.decl.warn(e.result,t,{word:String(n)});else if("throw"===e.oninvalid)throw e.decl.error(t,{word:String(n)})},a=/(^|[^\w-])(-webkit-)?image-set\(/i,l=/^(-webkit-)?image-set$/i,u=t=>{const r=!("preserve"in Object(t))||Boolean(t.preserve),u="oninvalid"in Object(t)?t.oninvalid:"ignore";return{postcssPlugin:"postcss-image-set-function",Declaration(t,{result:c,postcss:p}){const f=t.value;if(!a.test(f.toLowerCase()))return;let d;try{d=e(f)}catch(e){t.warn(c,`Failed to parse value '${f}' as an image-set function. Leaving the original value intact.`)}if(void 0===d)return;const v=[];d.walk((n=>{if("function"!==n.type)return;if(!l.test(n.value.toLowerCase()))return;let r=!1;if(e.walk(n.nodes,(e=>{"function"===e.type&&l.test(e.value.toLowerCase())&&(r=!0)})),r)return s({decl:t,oninvalid:u,result:c},"nested image-set functions are not allowed",e.stringify(n)),!1;const i=n.nodes.filter((e=>"comment"!==e.type&&"space"!==e.type));v.push({imageSetFunction:n,imageSetOptionNodes:i})})),((t,r,a)=>{const l=r.parent,u=new Map,c=r.value;for(let l=0;l<t.length;l++){const{imageSetFunction:f,imageSetOptionNodes:d}=t[l],v=new Map,g=d.length;let m=-1;for(;m<g;){const t=m<0||(p=d[m],"div"===Object(p).type&&","===Object(p).value),l=n(d[m+1]),g=o(d[m+2]),w=i(g,a.postcss,r);if(!t)return void s(a,"expected a comma",e.stringify(d));if(!l)return void s(a,"unexpected image",e.stringify(d));if(!w||!g||v.has(g))return void s(a,"unexpected resolution",e.stringify(d));if(v.set(g,w),u.has(g)){const t=u.get(g);t.value=t.value.replace(e.stringify(f),l.trim()),u.set(g,t)}else u.set(g,{atRule:w,value:c.replace(e.stringify(f),l.trim())});m+=3}}var p;for(const{atRule:e,value:t}of u.values()){const n=l.clone().removeAll(),i=r.clone({value:t});n.append(i),e.append(n)}const f=Array.from(u.keys()).sort(((e,t)=>e-t)).map((e=>u.get(e).atRule));if(!f.length)return;const d=f[0],v=f.slice(1);v.length&&l.after(v);const g=d.nodes[0].nodes[0];r.cloneBefore({value:g.value.trim()}),a.preserve||(r.remove(),l.nodes.length||l.remove())})(v,t,{decl:t,oninvalid:u,preserve:r,result:c,postcss:p})}}};u.postcss=!0;export{u as default};
