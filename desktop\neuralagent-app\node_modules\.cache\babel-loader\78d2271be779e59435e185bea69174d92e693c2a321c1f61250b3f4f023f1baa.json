{"ast": null, "code": "import styled from 'styled-components';\nexport const DialogContainer = styled.div`\n  position: fixed;\n  z-index: 1000;\n  width: 90%;\n  max-width: ${props => props.maxWidth ? props.maxWidth : '500px'};\n  background: ${props => props.isDarkMode ? 'var(--dark-theme-background)' : '#fff'};\n  justify-content: center;\n  align-items: center;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border-radius: 10px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n  max-height: 100vh;\n`;\nexport const DialogOverlay = styled.div`\n  position: fixed;\n  z-index: 1000;\n  height: 100%;\n  width: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  left: 0%;\n  top: 0%;\n`;\nexport const DialogHeader = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 15px 20px;\n`;\nexport const HeaderEnd = styled.div`\n  margin-left: ${props => props.isRTL ? '0px' : 'auto'};\n  margin-right: ${props => props.isRTL ? 'auto' : '0px'};\n`;\nexport const DialogTitle = styled.div`\n  font-size: 17px;\n  color: ${props => props.isDarkMode ? '#fff' : '#000'};\n  font-weight: 700;\n`;\nexport const DialogContent = styled.div`\n  padding: ${props => props.padding ? props.padding : '10px'};\n  display: flex;\n  flex-direction: column;\n  overflow: ${props => props.scrollable ? 'auto' : 'unset'};\n  height: ${props => props.scrollable ? props.height ? props.height : '500px' : 'unset'};\n`;\nexport const DialogActions = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 10px;\n`;", "map": {"version": 3, "names": ["styled", "DialogContainer", "div", "props", "max<PERSON><PERSON><PERSON>", "isDarkMode", "DialogOverlay", "DialogHeader", "HeaderEnd", "isRTL", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "padding", "scrollable", "height", "DialogActions"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/Dialog/DialogElements.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const DialogContainer = styled.div`\n  position: fixed;\n  z-index: 1000;\n  width: 90%;\n  max-width: ${props => props.maxWidth ? props.maxWidth : '500px'};\n  background: ${props => props.isDarkMode ? 'var(--dark-theme-background)' : '#fff'};\n  justify-content: center;\n  align-items: center;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border-radius: 10px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n  max-height: 100vh;\n`\n\nexport const DialogOverlay = styled.div`\n  position: fixed;\n  z-index: 1000;\n  height: 100%;\n  width: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  left: 0%;\n  top: 0%;\n`\n\nexport const DialogHeader = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 15px 20px;\n`\n\nexport const HeaderEnd = styled.div`\n  margin-left: ${props => props.isRTL ? '0px' : 'auto'};\n  margin-right: ${props => props.isRTL ? 'auto' : '0px'};\n`\n\nexport const DialogTitle = styled.div`\n  font-size: 17px;\n  color: ${props => props.isDarkMode ? '#fff' : '#000'};\n  font-weight: 700;\n`\n\nexport const DialogContent = styled.div`\n  padding: ${props => props.padding ? props.padding : '10px'};\n  display: flex;\n  flex-direction: column;\n  overflow: ${props => props.scrollable ? 'auto' : 'unset'};\n  height: ${props => props.scrollable ? (props.height ? props.height : '500px') : 'unset'};\n`\n\nexport const DialogActions = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 10px;\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,eAAe,GAAGD,MAAM,CAACE,GAAG;AACzC;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,GAAG,OAAO;AACjE,gBAAgBD,KAAK,IAAIA,KAAK,CAACE,UAAU,GAAG,8BAA8B,GAAG,MAAM;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGN,MAAM,CAACE,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMK,YAAY,GAAGP,MAAM,CAACE,GAAG;AACtC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMM,SAAS,GAAGR,MAAM,CAACE,GAAG;AACnC,iBAAiBC,KAAK,IAAIA,KAAK,CAACM,KAAK,GAAG,KAAK,GAAG,MAAM;AACtD,kBAAkBN,KAAK,IAAIA,KAAK,CAACM,KAAK,GAAG,MAAM,GAAG,KAAK;AACvD,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGV,MAAM,CAACE,GAAG;AACrC;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACE,UAAU,GAAG,MAAM,GAAG,MAAM;AACtD;AACA,CAAC;AAED,OAAO,MAAMM,aAAa,GAAGX,MAAM,CAACE,GAAG;AACvC,aAAaC,KAAK,IAAIA,KAAK,CAACS,OAAO,GAAGT,KAAK,CAACS,OAAO,GAAG,MAAM;AAC5D;AACA;AACA,cAAcT,KAAK,IAAIA,KAAK,CAACU,UAAU,GAAG,MAAM,GAAG,OAAO;AAC1D,YAAYV,KAAK,IAAIA,KAAK,CAACU,UAAU,GAAIV,KAAK,CAACW,MAAM,GAAGX,KAAK,CAACW,MAAM,GAAG,OAAO,GAAI,OAAO;AACzF,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGf,MAAM,CAACE,GAAG;AACvC;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}