{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\ChatMessage.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Tag } from './Elements/Tag';\nimport { FaMousePointer, FaRegKeyboard, FaCheckCircle, FaScroll, FaPause } from 'react-icons/fa';\nimport { MdOutlineOpenInBrowser, MdDragIndicator, MdCancel, MdError, MdApps, MdScreenShare } from 'react-icons/md';\nimport { FiCornerDownRight } from 'react-icons/fi';\nimport { GiBrain } from 'react-icons/gi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MessageContainer = styled.div`\n  display: flex;\n  justify-content: ${({\n  role\n}) => role === 'user' ? 'flex-end' : 'flex-start'};\n  padding: 10px 18px;\n`;\n_c = MessageContainer;\nconst Bubble = styled.div`\n  background-color: ${({\n  role\n}) => role === 'user' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.05)'};\n  color: #fff;\n  padding: 14px 18px;\n  border-radius: 16px;\n  max-width: 65%;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.35);\n  font-size: 14.5px;\n  line-height: 1.6;\n  white-space: pre-wrap;\n  word-break: break-word;\n  border-bottom-left-radius: ${({\n  role\n}) => role === 'user' ? '16px' : '0'};\n  border-bottom-right-radius: ${({\n  role\n}) => role === 'user' ? '0' : '16px'};\n`;\n_c2 = Bubble;\nconst ThoughtBox = styled.div`\n  font-style: italic;\n  opacity: 0.9;\n  padding: 10px;\n  background-color: #2a2a2a;\n  border-left: 3px solid #4ade80;\n  border-radius: 6px;\n  margin-top: 6px;\n  font-size: 13px;\n`;\n_c3 = ThoughtBox;\nconst Label = styled.div`\n  font-size: 13px;\n  color: #ccc;\n  margin-top: 8px;\n`;\n_c4 = Label;\nconst Spacer = styled.div`\n  height: 6px;\n`;\n_c5 = Spacer;\nconst iconStyle = {\n  color: '#fff',\n  fontSize: '15px'\n};\nconst actionMap = {\n  mouse_move: {\n    label: 'Move Cursor',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaMousePointer, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 76\n    }, this)\n  },\n  left_click: {\n    label: 'Click',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaMousePointer, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 70\n    }, this)\n  },\n  right_click: {\n    label: 'Right Click',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaMousePointer, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 77\n    }, this)\n  },\n  double_click: {\n    label: 'Double Click',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaMousePointer, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 79\n    }, this)\n  },\n  triple_click: {\n    label: 'Triple Click',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaMousePointer, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 79\n    }, this)\n  },\n  left_click_drag: {\n    label: 'Click & Drag',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdDragIndicator, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 82\n    }, this)\n  },\n  left_mouse_down: {\n    label: 'Mouse Down',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdDragIndicator, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 80\n    }, this)\n  },\n  left_mouse_up: {\n    label: 'Mouse Up',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdDragIndicator, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 76\n    }, this)\n  },\n  scroll: {\n    label: 'Scroll',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaScroll, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 67\n    }, this)\n  },\n  type: {\n    label: 'Type Text',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaRegKeyboard, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 68\n    }, this)\n  },\n  key: {\n    label: 'Press Key',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaRegKeyboard, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 67\n    }, this)\n  },\n  hold_key: {\n    label: 'Hold Key',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaRegKeyboard, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 71\n    }, this)\n  },\n  key_combo: {\n    label: 'Key Combo',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaRegKeyboard, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 73\n    }, this)\n  },\n  wait: {\n    label: 'Wait',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaPause, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 63\n    }, this)\n  },\n  launch_browser: {\n    label: 'Launch Browser',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdOutlineOpenInBrowser, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 83\n    }, this)\n  },\n  launch_app: {\n    label: 'Launch App',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdApps, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 75\n    }, this)\n  },\n  focus_app: {\n    label: 'Switch To App',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdApps, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 77\n    }, this)\n  },\n  request_screenshot: {\n    label: 'Request Screenshot',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdScreenShare, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 91\n    }, this)\n  },\n  tool_use: {\n    label: 'Tool Use',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaRegKeyboard, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 71\n    }, this)\n  },\n  subtask_completed: {\n    label: 'Step Completed',\n    color: 'var(--primary-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 86\n    }, this)\n  },\n  subtask_failed: {\n    label: 'Step Failed',\n    color: 'var(--danger-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdError, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 79\n    }, this)\n  },\n  task_completed: {\n    label: 'Task Completed',\n    color: 'var(--success-color)',\n    icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 83\n    }, this)\n  },\n  task_canceled: {\n    label: 'Task Canceled',\n    color: 'var(--danger-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdCancel, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 80\n    }, this)\n  },\n  task_failed: {\n    label: 'Task Failed',\n    color: 'var(--danger-color)',\n    icon: /*#__PURE__*/_jsxDEV(MdError, {\n      style: iconStyle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 76\n    }, this)\n  }\n};\nexport default function ChatMessage({\n  message\n}) {\n  const isUser = message.thread_chat_from !== 'from_ai';\n  const role = isUser ? 'user' : 'assistant';\n  const getContent = () => {\n    const type = message.thread_chat_type;\n    const raw = message.text;\n    if (type === 'normal_message') return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: raw\n    }, void 0, false);\n    let parsed;\n    try {\n      parsed = JSON.parse(raw);\n    } catch {\n      return '[Failed to parse message]';\n    }\n    if (type === 'classification') {\n      const isDesktop = parsed.type === 'desktop_task';\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          children: [isDesktop ? /*#__PURE__*/_jsxDEV(MdOutlineOpenInBrowser, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 26\n          }, this) : /*#__PURE__*/_jsxDEV(FiCornerDownRight, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 73\n          }, this), isDesktop ? 'Desktop Task' : 'Inquiry']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Spacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: parsed.response\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true);\n    }\n    if (type === 'action') {\n      const actionMeta = actionMap[parsed.action] || {\n        label: parsed.action,\n        icon: /*#__PURE__*/_jsxDEV(FaMousePointer, {\n          style: iconStyle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this)\n      };\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          color: actionMeta.color,\n          children: [actionMeta.icon, actionMeta.label]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), parsed.action === 'tool_use' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [parsed.tool && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Tool:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 33\n            }, this), \" \", parsed.tool]\n          }, void 0, true), parsed.args && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Arguments:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 19\n            }, this), \" \", JSON.stringify(parsed.args)]\n          }, void 0, true)]\n        }, void 0, true), parsed.text && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Text:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 29\n          }, this), \" \", parsed.text]\n        }, void 0, true), parsed.url && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"URL:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 28\n          }, this), \" \", parsed.url]\n        }, void 0, true), parsed.app_name && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"App Name:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 33\n          }, this), \" \", parsed.app_name]\n        }, void 0, true), parsed.coordinate && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Coordinate:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), \" (\", parsed.coordinate.x, \", \", parsed.coordinate.y, \")\"]\n        }, void 0, true), parsed.from && parsed.to && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Drag:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), \" From (\", parsed.from.x, \", \", parsed.from.y, \") \\u2192 (\", parsed.to.x, \", \", parsed.to.y, \")\"]\n        }, void 0, true), parsed.duration && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Duration:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 33\n          }, this), \" \", parsed.duration, \"s\"]\n        }, void 0, true), parsed.reasoning && /*#__PURE__*/_jsxDEV(ThoughtBox, {\n          children: parsed.reasoning\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 32\n        }, this)]\n      }, void 0, true);\n    }\n    if (type === 'browser_use' || type === 'bg_mode_browser') {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          children: [/*#__PURE__*/_jsxDEV(MdOutlineOpenInBrowser, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 16\n          }, this), \"Using The Browser\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Spacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), parsed.current_state && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Evaluation:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.evaluation_previous_goal, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 91\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Memory:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.memory, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 69\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Next Goal:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.next_goal]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), parsed.action && Array.isArray(parsed.action) && parsed.action.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Next Actions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n            style: {\n              marginTop: '8px',\n              paddingLeft: '20px'\n            },\n            children: parsed.action.map((act, idx) => {\n              const actionType = Object.keys(act)[0];\n              const params = act[actionType];\n              return /*#__PURE__*/_jsxDEV(\"li\", {\n                style: {\n                  marginBottom: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Action:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this), \" \", actionType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 60\n                }, this), params && Object.keys(params).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [key, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 27\n                  }, this), \" \", typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key]]\n                }, key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 25\n                }, this))]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true);\n    }\n    if (type === 'desktop_use') {\n      var _actionMap$parsed$act, _actionMap$parsed$act2, _actionMap$parsed$act3;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          children: [/*#__PURE__*/_jsxDEV(MdApps, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 16\n          }, this), \"Using The Desktop\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Spacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), parsed.current_state && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Evaluation:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.current_evaluation, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 85\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Memory:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.memory, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 69\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Next Goal:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.next_steps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), parsed.action && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Next Action:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 44\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: ((_actionMap$parsed$act = actionMap[parsed.action]) === null || _actionMap$parsed$act === void 0 ? void 0 : _actionMap$parsed$act.color) || 'var(--primary-color)',\n            children: [((_actionMap$parsed$act2 = actionMap[parsed.action]) === null || _actionMap$parsed$act2 === void 0 ? void 0 : _actionMap$parsed$act2.icon) || /*#__PURE__*/_jsxDEV(FaMousePointer, {\n              style: iconStyle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 52\n            }, this), ((_actionMap$parsed$act3 = actionMap[parsed.action]) === null || _actionMap$parsed$act3 === void 0 ? void 0 : _actionMap$parsed$act3.label) || parsed.action]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px'\n            },\n            children: Object.entries(parsed).filter(([key]) => key !== 'action' && key !== 'current_state').map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [key, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 36\n              }, this), \" \", typeof value === 'object' ? JSON.stringify(value) : value]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true);\n    }\n    if (type === 'desktop_use_v2' || type === 'bg_mode_browser_v2') {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [type === 'desktop_use_v2' ? /*#__PURE__*/_jsxDEV(Tag, {\n          children: [/*#__PURE__*/_jsxDEV(MdApps, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 46\n          }, this), \"Using The Desktop\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 41\n        }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n          children: [/*#__PURE__*/_jsxDEV(MdOutlineOpenInBrowser, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 105\n          }, this), \"Using The Background Browser\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 100\n        }, this), /*#__PURE__*/_jsxDEV(Spacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), parsed.current_state && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Evaluation:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.evaluation_previous_goal, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 91\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Memory:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.memory, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 69\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Next Goal:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), \" \", parsed.current_state.next_goal]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), parsed.actions && parsed.actions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Next Actions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n            style: {\n              marginTop: '8px',\n              paddingLeft: '20px'\n            },\n            children: parsed.actions.map((actionObj, idx) => {\n              var _actionMap$actionObj$, _actionMap$actionObj$2, _actionMap$actionObj$3;\n              return /*#__PURE__*/_jsxDEV(\"li\", {\n                style: {\n                  marginBottom: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: ((_actionMap$actionObj$ = actionMap[actionObj.action]) === null || _actionMap$actionObj$ === void 0 ? void 0 : _actionMap$actionObj$.color) || 'var(--primary-color)',\n                  children: [((_actionMap$actionObj$2 = actionMap[actionObj.action]) === null || _actionMap$actionObj$2 === void 0 ? void 0 : _actionMap$actionObj$2.icon) || /*#__PURE__*/_jsxDEV(FaMousePointer, {\n                    style: iconStyle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 61\n                  }, this), ((_actionMap$actionObj$3 = actionMap[actionObj.action]) === null || _actionMap$actionObj$3 === void 0 ? void 0 : _actionMap$actionObj$3.label) || actionObj.action]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this), actionObj.params && Object.entries(actionObj.params).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [key, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 38\n                  }, this), \" \", typeof value === 'object' ? JSON.stringify(value) : value]\n                }, key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this))]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true);\n    }\n    if (type === 'plan') {\n      var _parsed$subtasks;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          children: [/*#__PURE__*/_jsxDEV(FiCornerDownRight, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 16\n          }, this), \"Plan\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Spacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), ((_parsed$subtasks = parsed.subtasks) === null || _parsed$subtasks === void 0 ? void 0 : _parsed$subtasks.length) > 0 ? /*#__PURE__*/_jsxDEV(\"ol\", {\n          style: {\n            margin: 0,\n            paddingLeft: 20\n          },\n          children: parsed.subtasks.map((step, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Step:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 24\n              }, this), \" \", step.subtask]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 24\n              }, this), \" \", step.type === 'browser_subtask' ? 'Browser' : 'Desktop']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this)]\n          }, idx, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"[Empty plan]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true);\n    }\n    if (type === 'thinking' && message.chain_of_thought) {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          children: [/*#__PURE__*/_jsxDEV(GiBrain, {\n            style: iconStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 16\n          }, this), \"Thinking\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Spacer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ThoughtBox, {\n          children: message.chain_of_thought\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true);\n    }\n    return '[Unknown message type]';\n  };\n  return /*#__PURE__*/_jsxDEV(MessageContainer, {\n    role: role,\n    children: /*#__PURE__*/_jsxDEV(Bubble, {\n      role: role,\n      children: getContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n}\n_c6 = ChatMessage;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"MessageContainer\");\n$RefreshReg$(_c2, \"Bubble\");\n$RefreshReg$(_c3, \"ThoughtBox\");\n$RefreshReg$(_c4, \"Label\");\n$RefreshReg$(_c5, \"Spacer\");\n$RefreshReg$(_c6, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "styled", "Tag", "FaMousePoint<PERSON>", "FaRegKeyboard", "FaCheckCircle", "FaScroll", "FaPause", "MdOutlineOpenInBrowser", "MdDragIndicator", "MdCancel", "Md<PERSON><PERSON><PERSON>", "MdApps", "MdScreenShare", "FiCornerDownRight", "GiBrain", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessageContainer", "div", "role", "_c", "Bubble", "_c2", "ThoughtBox", "_c3", "Label", "_c4", "Spacer", "_c5", "iconStyle", "color", "fontSize", "actionMap", "mouse_move", "label", "icon", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "left_click", "right_click", "double_click", "triple_click", "left_click_drag", "left_mouse_down", "left_mouse_up", "scroll", "type", "key", "hold_key", "key_combo", "wait", "launch_browser", "launch_app", "focus_app", "request_screenshot", "tool_use", "subtask_completed", "subtask_failed", "task_completed", "task_canceled", "task_failed", "ChatMessage", "message", "isUser", "thread_chat_from", "get<PERSON>ontent", "thread_chat_type", "raw", "text", "children", "parsed", "JSON", "parse", "isDesktop", "response", "actionMeta", "action", "tool", "args", "stringify", "url", "app_name", "coordinate", "x", "y", "from", "to", "duration", "reasoning", "current_state", "marginBottom", "evaluation_previous_goal", "memory", "next_goal", "Array", "isArray", "length", "marginTop", "paddingLeft", "map", "act", "idx", "actionType", "Object", "keys", "params", "_actionMap$parsed$act", "_actionMap$parsed$act2", "_actionMap$parsed$act3", "current_evaluation", "next_steps", "entries", "filter", "value", "actions", "actionObj", "_actionMap$actionObj$", "_actionMap$actionObj$2", "_actionMap$actionObj$3", "_parsed$subtasks", "subtasks", "margin", "step", "subtask", "chain_of_thought", "_c6", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/ChatMessage.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { Tag } from './Elements/Tag';\n\nimport {\n  Fa<PERSON>ousePointer,\n  FaRegKeyboard,\n  FaCheckCircle,\n  FaScroll,\n  FaPause\n} from 'react-icons/fa';\nimport {\n  MdOutline<PERSON>pen<PERSON>nB<PERSON>er,\n  MdDragIndicator,\n  MdCancel,\n  MdError,\n  MdApps,\n  MdScreenShare\n} from 'react-icons/md';\nimport { FiCornerDownRight } from 'react-icons/fi';\nimport { GiBrain } from 'react-icons/gi';\n\nconst MessageContainer = styled.div`\n  display: flex;\n  justify-content: ${({ role }) => (role === 'user' ? 'flex-end' : 'flex-start')};\n  padding: 10px 18px;\n`;\n\nconst Bubble = styled.div`\n  background-color: ${({ role }) =>\n    role === 'user' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.05)'};\n  color: #fff;\n  padding: 14px 18px;\n  border-radius: 16px;\n  max-width: 65%;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.35);\n  font-size: 14.5px;\n  line-height: 1.6;\n  white-space: pre-wrap;\n  word-break: break-word;\n  border-bottom-left-radius: ${({ role }) => (role === 'user' ? '16px' : '0')};\n  border-bottom-right-radius: ${({ role }) => (role === 'user' ? '0' : '16px')};\n`;\n\nconst ThoughtBox = styled.div`\n  font-style: italic;\n  opacity: 0.9;\n  padding: 10px;\n  background-color: #2a2a2a;\n  border-left: 3px solid #4ade80;\n  border-radius: 6px;\n  margin-top: 6px;\n  font-size: 13px;\n`;\n\nconst Label = styled.div`\n  font-size: 13px;\n  color: #ccc;\n  margin-top: 8px;\n`;\n\nconst Spacer = styled.div`\n  height: 6px;\n`;\n\nconst iconStyle = { color: '#fff', fontSize: '15px' };\n\nconst actionMap = {\n  mouse_move: { label: 'Move Cursor', color: 'var(--primary-color)', icon: <FaMousePointer style={iconStyle} /> },\n  left_click: { label: 'Click', color: 'var(--primary-color)', icon: <FaMousePointer style={iconStyle} /> },\n  right_click: { label: 'Right Click', color: 'var(--primary-color)', icon: <FaMousePointer style={iconStyle} /> },\n  double_click: { label: 'Double Click', color: 'var(--primary-color)', icon: <FaMousePointer style={iconStyle} /> },\n  triple_click: { label: 'Triple Click', color: 'var(--primary-color)', icon: <FaMousePointer style={iconStyle} /> },\n  left_click_drag: { label: 'Click & Drag', color: 'var(--primary-color)', icon: <MdDragIndicator style={iconStyle} /> },\n  left_mouse_down: { label: 'Mouse Down', color: 'var(--primary-color)', icon: <MdDragIndicator style={iconStyle} /> },\n  left_mouse_up: { label: 'Mouse Up', color: 'var(--primary-color)', icon: <MdDragIndicator style={iconStyle} /> },\n  scroll: { label: 'Scroll', color: 'var(--primary-color)', icon: <FaScroll style={iconStyle} /> },\n  type: { label: 'Type Text', color: 'var(--primary-color)', icon: <FaRegKeyboard style={iconStyle} /> },\n  key: { label: 'Press Key', color: 'var(--primary-color)', icon: <FaRegKeyboard style={iconStyle} /> },\n  hold_key: { label: 'Hold Key', color: 'var(--primary-color)', icon: <FaRegKeyboard style={iconStyle} /> },\n  key_combo: { label: 'Key Combo', color: 'var(--primary-color)', icon: <FaRegKeyboard style={iconStyle} /> },\n  wait: { label: 'Wait', color: 'var(--primary-color)', icon: <FaPause style={iconStyle} /> },\n  launch_browser: { label: 'Launch Browser', color: 'var(--primary-color)', icon: <MdOutlineOpenInBrowser style={iconStyle} /> },\n  launch_app: { label: 'Launch App', color: 'var(--primary-color)', icon: <MdApps style={iconStyle} /> },\n  focus_app: { label: 'Switch To App', color: 'var(--primary-color)', icon: <MdApps style={iconStyle} /> },\n  request_screenshot: { label: 'Request Screenshot', color: 'var(--primary-color)', icon: <MdScreenShare style={iconStyle} /> },\n  tool_use: { label: 'Tool Use', color: 'var(--primary-color)', icon: <FaRegKeyboard style={iconStyle} /> },\n  subtask_completed: { label: 'Step Completed', color: 'var(--primary-color)', icon: <FaCheckCircle style={iconStyle} /> },\n  subtask_failed: { label: 'Step Failed', color: 'var(--danger-color)', icon: <MdError style={iconStyle} /> },\n  task_completed: { label: 'Task Completed', color: 'var(--success-color)', icon: <FaCheckCircle style={iconStyle} /> },\n  task_canceled: { label: 'Task Canceled', color: 'var(--danger-color)', icon: <MdCancel style={iconStyle} /> },\n  task_failed: { label: 'Task Failed', color: 'var(--danger-color)', icon: <MdError style={iconStyle} /> }\n};\n\n\nexport default function ChatMessage({ message }) {\n  const isUser = message.thread_chat_from !== 'from_ai';\n  const role = isUser ? 'user' : 'assistant';\n\n  const getContent = () => {\n    const type = message.thread_chat_type;\n    const raw = message.text;\n\n    if (type === 'normal_message') return <>{raw}</>;\n\n    let parsed;\n    try {\n      parsed = JSON.parse(raw);\n    } catch {\n      return '[Failed to parse message]';\n    }\n\n    if (type === 'classification') {\n      const isDesktop = parsed.type === 'desktop_task';\n      return (\n        <>\n          <Tag>\n            {isDesktop ? <MdOutlineOpenInBrowser style={iconStyle} /> : <FiCornerDownRight style={iconStyle} />}\n            {isDesktop ? 'Desktop Task' : 'Inquiry'}\n          </Tag>\n          <Spacer />\n          <div>{parsed.response}</div>\n        </>\n      );\n    }\n\n    if (type === 'action') {\n      const actionMeta = actionMap[parsed.action] || {\n        label: parsed.action,\n        icon: <FaMousePointer style={iconStyle} />\n      };\n\n      return (\n        <>\n          <Tag color={actionMeta.color}>\n            {actionMeta.icon}\n            {actionMeta.label}\n          </Tag>\n\n          {parsed.action === 'tool_use' && (\n            <>\n              {parsed.tool && <><Label>Tool:</Label> {parsed.tool}</>}\n              {parsed.args && (\n                <>\n                  <Label>Arguments:</Label> {JSON.stringify(parsed.args)}\n                </>\n              )}\n            </>\n          )}\n\n          {parsed.text && <><Label>Text:</Label> {parsed.text}</>}\n          {parsed.url && <><Label>URL:</Label> {parsed.url}</>}\n          {parsed.app_name && <><Label>App Name:</Label> {parsed.app_name}</>}\n          {parsed.coordinate && (\n            <>\n              <Label>Coordinate:</Label> ({parsed.coordinate.x}, {parsed.coordinate.y})\n            </>\n          )}\n          {parsed.from && parsed.to && (\n            <>\n              <Label>Drag:</Label> From ({parsed.from.x}, {parsed.from.y}) → ({parsed.to.x}, {parsed.to.y})\n            </>\n          )}\n          {parsed.duration && <><Label>Duration:</Label> {parsed.duration}s</>}\n          {parsed.reasoning && <ThoughtBox>{parsed.reasoning}</ThoughtBox>}\n        </>\n      );\n    }\n\n    if (type === 'browser_use' || type === 'bg_mode_browser') {\n      return (\n        <>\n          <Tag><MdOutlineOpenInBrowser style={iconStyle} />Using The Browser</Tag>\n          <Spacer />\n    \n          {parsed.current_state && (\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Evaluation:</strong> {parsed.current_state.evaluation_previous_goal}<br />\n              <strong>Memory:</strong> {parsed.current_state.memory}<br />\n              <strong>Next Goal:</strong> {parsed.current_state.next_goal}\n            </div>\n          )}\n    \n          {parsed.action && Array.isArray(parsed.action) && parsed.action.length > 0 && (\n            <div>\n              <strong>Next Actions:</strong>\n              <ol style={{ marginTop: '8px', paddingLeft: '20px' }}>\n                {parsed.action.map((act, idx) => {\n                  const actionType = Object.keys(act)[0];\n                  const params = act[actionType];\n    \n                  return (\n                    <li key={idx} style={{ marginBottom: '8px' }}>\n                      <strong>Action:</strong> {actionType}<br />\n                      {params && Object.keys(params).map((key) => (\n                        <div key={key}>\n                          <strong>{key}:</strong> {typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key]}\n                        </div>\n                      ))}\n                    </li>\n                  );\n                })}\n              </ol>\n            </div>\n          )}\n        </>\n      );\n    }\n\n    if (type === 'desktop_use') {\n      return (\n        <>\n          <Tag><MdApps style={iconStyle} />Using The Desktop</Tag>\n          <Spacer />\n    \n          {parsed.current_state && (\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Evaluation:</strong> {parsed.current_state.current_evaluation}<br />\n              <strong>Memory:</strong> {parsed.current_state.memory}<br />\n              <strong>Next Goal:</strong> {parsed.current_state.next_steps}\n            </div>\n          )}\n    \n          {parsed.action && (\n            <div>\n              <strong>Next Action:</strong><br />\n              <Tag color={actionMap[parsed.action]?.color || 'var(--primary-color)'}>\n                {actionMap[parsed.action]?.icon || <FaMousePointer style={iconStyle} />}\n                {actionMap[parsed.action]?.label || parsed.action}\n              </Tag>\n    \n              <div style={{ marginTop: '6px' }}>\n                {Object.entries(parsed)\n                  .filter(([key]) => key !== 'action' && key !== 'current_state')\n                  .map(([key, value]) => (\n                    <div key={key}><strong>{key}:</strong> {typeof value === 'object' ? JSON.stringify(value) : value}</div>\n                  ))}\n              </div>\n            </div>\n          )}\n        </>\n      );\n    }\n\n    if (type === 'desktop_use_v2' || type === 'bg_mode_browser_v2') {\n      return (\n        <>\n          {\n            type === 'desktop_use_v2' ? <Tag><MdApps style={iconStyle} />Using The Desktop</Tag> : <Tag><MdOutlineOpenInBrowser style={iconStyle} />Using The Background Browser</Tag>\n          }\n          <Spacer />\n\n          {parsed.current_state && (\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Evaluation:</strong> {parsed.current_state.evaluation_previous_goal}<br />\n              <strong>Memory:</strong> {parsed.current_state.memory}<br />\n              <strong>Next Goal:</strong> {parsed.current_state.next_goal}\n            </div>\n          )}\n\n          {parsed.actions && parsed.actions.length > 0 && (\n            <div>\n              <strong>Next Actions:</strong>\n              <ol style={{ marginTop: '8px', paddingLeft: '20px' }}>\n                {parsed.actions.map((actionObj, idx) => (\n                  <li key={idx} style={{ marginBottom: '8px' }}>\n                    <Tag color={actionMap[actionObj.action]?.color || 'var(--primary-color)'}>\n                      {actionMap[actionObj.action]?.icon || <FaMousePointer style={iconStyle} />}\n                      {actionMap[actionObj.action]?.label || actionObj.action}\n                    </Tag>\n                    {actionObj.params && Object.entries(actionObj.params).map(([key, value]) => (\n                      <div key={key}><strong>{key}:</strong> {typeof value === 'object' ? JSON.stringify(value) : value}</div>\n                    ))}\n                  </li>\n                ))}\n              </ol>\n            </div>\n          )}\n        </>\n      );\n    }\n\n    if (type === 'plan') {\n      return (\n        <>\n          <Tag><FiCornerDownRight style={iconStyle} />Plan</Tag>\n          <Spacer />\n          {parsed.subtasks?.length > 0 ? (\n            <ol style={{ margin: 0, paddingLeft: 20 }}>\n              {parsed.subtasks.map((step, idx) => (\n                <li key={idx}>\n                  <div><strong>Step:</strong> {step.subtask}</div>\n                  <div><strong>Type:</strong> {step.type === 'browser_subtask' ? 'Browser' : 'Desktop'}</div>\n                </li>\n              ))}\n            </ol>\n          ) : (\n            <div>[Empty plan]</div>\n          )}\n        </>\n      );\n    }\n\n    if (type === 'thinking' && message.chain_of_thought) {\n      return (\n        <>\n          <Tag><GiBrain style={iconStyle} />Thinking</Tag>\n          <Spacer />\n          <ThoughtBox>{message.chain_of_thought}</ThoughtBox>\n        </>\n      );\n    }\n\n    return '[Unknown message type]';\n  };\n\n  return (\n    <MessageContainer role={role}>\n      <Bubble role={role}>\n        {getContent()}\n      </Bubble>\n    </MessageContainer>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,GAAG,QAAQ,gBAAgB;AAEpC,SACEC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,OAAO,QACF,gBAAgB;AACvB,SACEC,sBAAsB,EACtBC,eAAe,EACfC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,aAAa,QACR,gBAAgB;AACvB,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,gBAAgB,GAAGnB,MAAM,CAACoB,GAAG;AACnC;AACA,qBAAqB,CAAC;EAAEC;AAAK,CAAC,KAAMA,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAa;AAChF;AACA,CAAC;AAACC,EAAA,GAJIH,gBAAgB;AAMtB,MAAMI,MAAM,GAAGvB,MAAM,CAACoB,GAAG;AACzB,sBAAsB,CAAC;EAAEC;AAAK,CAAC,KAC3BA,IAAI,KAAK,MAAM,GAAG,0BAA0B,GAAG,2BAA2B;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,CAAC;EAAEA;AAAK,CAAC,KAAMA,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,GAAI;AAC7E,gCAAgC,CAAC;EAAEA;AAAK,CAAC,KAAMA,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,MAAO;AAC9E,CAAC;AAACG,GAAA,GAdID,MAAM;AAgBZ,MAAME,UAAU,GAAGzB,MAAM,CAACoB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GATID,UAAU;AAWhB,MAAME,KAAK,GAAG3B,MAAM,CAACoB,GAAG;AACxB;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,KAAK;AAMX,MAAME,MAAM,GAAG7B,MAAM,CAACoB,GAAG;AACzB;AACA,CAAC;AAACU,GAAA,GAFID,MAAM;AAIZ,MAAME,SAAS,GAAG;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAO,CAAC;AAErD,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IAAEC,KAAK,EAAE,aAAa;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACd,cAAc;MAACoC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC/GC,UAAU,EAAE;IAAEP,KAAK,EAAE,OAAO;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACd,cAAc;MAACoC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACzGE,WAAW,EAAE;IAAER,KAAK,EAAE,aAAa;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACd,cAAc;MAACoC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAChHG,YAAY,EAAE;IAAET,KAAK,EAAE,cAAc;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACd,cAAc;MAACoC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAClHI,YAAY,EAAE;IAAEV,KAAK,EAAE,cAAc;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACd,cAAc;MAACoC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAClHK,eAAe,EAAE;IAAEX,KAAK,EAAE,cAAc;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACR,eAAe;MAAC8B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACtHM,eAAe,EAAE;IAAEZ,KAAK,EAAE,YAAY;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACR,eAAe;MAAC8B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACpHO,aAAa,EAAE;IAAEb,KAAK,EAAE,UAAU;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACR,eAAe;MAAC8B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAChHQ,MAAM,EAAE;IAAEd,KAAK,EAAE,QAAQ;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACX,QAAQ;MAACiC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAChGS,IAAI,EAAE;IAAEf,KAAK,EAAE,WAAW;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACb,aAAa;MAACmC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACtGU,GAAG,EAAE;IAAEhB,KAAK,EAAE,WAAW;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACb,aAAa;MAACmC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACrGW,QAAQ,EAAE;IAAEjB,KAAK,EAAE,UAAU;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACb,aAAa;MAACmC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACzGY,SAAS,EAAE;IAAElB,KAAK,EAAE,WAAW;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACb,aAAa;MAACmC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC3Ga,IAAI,EAAE;IAAEnB,KAAK,EAAE,MAAM;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACV,OAAO;MAACgC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC3Fc,cAAc,EAAE;IAAEpB,KAAK,EAAE,gBAAgB;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACT,sBAAsB;MAAC+B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC9He,UAAU,EAAE;IAAErB,KAAK,EAAE,YAAY;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACL,MAAM;MAAC2B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACtGgB,SAAS,EAAE;IAAEtB,KAAK,EAAE,eAAe;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACL,MAAM;MAAC2B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACxGiB,kBAAkB,EAAE;IAAEvB,KAAK,EAAE,oBAAoB;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACJ,aAAa;MAAC0B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC7HkB,QAAQ,EAAE;IAAExB,KAAK,EAAE,UAAU;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACb,aAAa;MAACmC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACzGmB,iBAAiB,EAAE;IAAEzB,KAAK,EAAE,gBAAgB;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACZ,aAAa;MAACkC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACxHoB,cAAc,EAAE;IAAE1B,KAAK,EAAE,aAAa;IAAEJ,KAAK,EAAE,qBAAqB;IAAEK,IAAI,eAAErB,OAAA,CAACN,OAAO;MAAC4B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC3GqB,cAAc,EAAE;IAAE3B,KAAK,EAAE,gBAAgB;IAAEJ,KAAK,EAAE,sBAAsB;IAAEK,IAAI,eAAErB,OAAA,CAACZ,aAAa;MAACkC,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EACrHsB,aAAa,EAAE;IAAE5B,KAAK,EAAE,eAAe;IAAEJ,KAAK,EAAE,qBAAqB;IAAEK,IAAI,eAAErB,OAAA,CAACP,QAAQ;MAAC6B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAC7GuB,WAAW,EAAE;IAAE7B,KAAK,EAAE,aAAa;IAAEJ,KAAK,EAAE,qBAAqB;IAAEK,IAAI,eAAErB,OAAA,CAACN,OAAO;MAAC4B,KAAK,EAAEP;IAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;AACzG,CAAC;AAGD,eAAe,SAASwB,WAAWA,CAAC;EAAEC;AAAQ,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAGD,OAAO,CAACE,gBAAgB,KAAK,SAAS;EACrD,MAAMhD,IAAI,GAAG+C,MAAM,GAAG,MAAM,GAAG,WAAW;EAE1C,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMnB,IAAI,GAAGgB,OAAO,CAACI,gBAAgB;IACrC,MAAMC,GAAG,GAAGL,OAAO,CAACM,IAAI;IAExB,IAAItB,IAAI,KAAK,gBAAgB,EAAE,oBAAOnC,OAAA,CAAAE,SAAA;MAAAwD,QAAA,EAAGF;IAAG,gBAAG,CAAC;IAEhD,IAAIG,MAAM;IACV,IAAI;MACFA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC;IAC1B,CAAC,CAAC,MAAM;MACN,OAAO,2BAA2B;IACpC;IAEA,IAAIrB,IAAI,KAAK,gBAAgB,EAAE;MAC7B,MAAM2B,SAAS,GAAGH,MAAM,CAACxB,IAAI,KAAK,cAAc;MAChD,oBACEnC,OAAA,CAAAE,SAAA;QAAAwD,QAAA,gBACE1D,OAAA,CAACf,GAAG;UAAAyE,QAAA,GACDI,SAAS,gBAAG9D,OAAA,CAACT,sBAAsB;YAAC+B,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG1B,OAAA,CAACH,iBAAiB;YAACyB,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClGoC,SAAS,GAAG,cAAc,GAAG,SAAS;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACN1B,OAAA,CAACa,MAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACV1B,OAAA;UAAA0D,QAAA,EAAMC,MAAM,CAACI;QAAQ;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eAC5B,CAAC;IAEP;IAEA,IAAIS,IAAI,KAAK,QAAQ,EAAE;MACrB,MAAM6B,UAAU,GAAG9C,SAAS,CAACyC,MAAM,CAACM,MAAM,CAAC,IAAI;QAC7C7C,KAAK,EAAEuC,MAAM,CAACM,MAAM;QACpB5C,IAAI,eAAErB,OAAA,CAACd,cAAc;UAACoC,KAAK,EAAEP;QAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC3C,CAAC;MAED,oBACE1B,OAAA,CAAAE,SAAA;QAAAwD,QAAA,gBACE1D,OAAA,CAACf,GAAG;UAAC+B,KAAK,EAAEgD,UAAU,CAAChD,KAAM;UAAA0C,QAAA,GAC1BM,UAAU,CAAC3C,IAAI,EACf2C,UAAU,CAAC5C,KAAK;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,EAELiC,MAAM,CAACM,MAAM,KAAK,UAAU,iBAC3BjE,OAAA,CAAAE,SAAA;UAAAwD,QAAA,GACGC,MAAM,CAACO,IAAI,iBAAIlE,OAAA,CAAAE,SAAA;YAAAwD,QAAA,gBAAE1D,OAAA,CAACW,KAAK;cAAA+C,QAAA,EAAC;YAAK;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,KAAC,EAACiC,MAAM,CAACO,IAAI;UAAA,eAAG,CAAC,EACtDP,MAAM,CAACQ,IAAI,iBACVnE,OAAA,CAAAE,SAAA;YAAAwD,QAAA,gBACE1D,OAAA,CAACW,KAAK;cAAA+C,QAAA,EAAC;YAAU;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,KAAC,EAACkC,IAAI,CAACQ,SAAS,CAACT,MAAM,CAACQ,IAAI,CAAC;UAAA,eACtD,CACH;QAAA,eACD,CACH,EAEAR,MAAM,CAACF,IAAI,iBAAIzD,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBAAE1D,OAAA,CAACW,KAAK;YAAA+C,QAAA,EAAC;UAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KAAC,EAACiC,MAAM,CAACF,IAAI;QAAA,eAAG,CAAC,EACtDE,MAAM,CAACU,GAAG,iBAAIrE,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBAAE1D,OAAA,CAACW,KAAK;YAAA+C,QAAA,EAAC;UAAI;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KAAC,EAACiC,MAAM,CAACU,GAAG;QAAA,eAAG,CAAC,EACnDV,MAAM,CAACW,QAAQ,iBAAItE,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBAAE1D,OAAA,CAACW,KAAK;YAAA+C,QAAA,EAAC;UAAS;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KAAC,EAACiC,MAAM,CAACW,QAAQ;QAAA,eAAG,CAAC,EAClEX,MAAM,CAACY,UAAU,iBAChBvE,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBACE1D,OAAA,CAACW,KAAK;YAAA+C,QAAA,EAAC;UAAW;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,MAAE,EAACiC,MAAM,CAACY,UAAU,CAACC,CAAC,EAAC,IAAE,EAACb,MAAM,CAACY,UAAU,CAACE,CAAC,EAAC,GAC1E;QAAA,eAAE,CACH,EACAd,MAAM,CAACe,IAAI,IAAIf,MAAM,CAACgB,EAAE,iBACvB3E,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBACE1D,OAAA,CAACW,KAAK;YAAA+C,QAAA,EAAC;UAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,WAAO,EAACiC,MAAM,CAACe,IAAI,CAACF,CAAC,EAAC,IAAE,EAACb,MAAM,CAACe,IAAI,CAACD,CAAC,EAAC,YAAK,EAACd,MAAM,CAACgB,EAAE,CAACH,CAAC,EAAC,IAAE,EAACb,MAAM,CAACgB,EAAE,CAACF,CAAC,EAAC,GAC9F;QAAA,eAAE,CACH,EACAd,MAAM,CAACiB,QAAQ,iBAAI5E,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBAAE1D,OAAA,CAACW,KAAK;YAAA+C,QAAA,EAAC;UAAS;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KAAC,EAACiC,MAAM,CAACiB,QAAQ,EAAC,GAAC;QAAA,eAAE,CAAC,EACnEjB,MAAM,CAACkB,SAAS,iBAAI7E,OAAA,CAACS,UAAU;UAAAiD,QAAA,EAAEC,MAAM,CAACkB;QAAS;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA,eAChE,CAAC;IAEP;IAEA,IAAIS,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,EAAE;MACxD,oBACEnC,OAAA,CAAAE,SAAA;QAAAwD,QAAA,gBACE1D,OAAA,CAACf,GAAG;UAAAyE,QAAA,gBAAC1D,OAAA,CAACT,sBAAsB;YAAC+B,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxE1B,OAAA,CAACa,MAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAETiC,MAAM,CAACmB,aAAa,iBACnB9E,OAAA;UAAKsB,KAAK,EAAE;YAAEyD,YAAY,EAAE;UAAO,CAAE;UAAArB,QAAA,gBACnC1D,OAAA;YAAA0D,QAAA,EAAQ;UAAW;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACE,wBAAwB,eAAChF,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClF1B,OAAA;YAAA0D,QAAA,EAAQ;UAAO;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACG,MAAM,eAACjF,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5D1B,OAAA;YAAA0D,QAAA,EAAQ;UAAU;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACI,SAAS;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEAiC,MAAM,CAACM,MAAM,IAAIkB,KAAK,CAACC,OAAO,CAACzB,MAAM,CAACM,MAAM,CAAC,IAAIN,MAAM,CAACM,MAAM,CAACoB,MAAM,GAAG,CAAC,iBACxErF,OAAA;UAAA0D,QAAA,gBACE1D,OAAA;YAAA0D,QAAA,EAAQ;UAAa;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B1B,OAAA;YAAIsB,KAAK,EAAE;cAAEgE,SAAS,EAAE,KAAK;cAAEC,WAAW,EAAE;YAAO,CAAE;YAAA7B,QAAA,EAClDC,MAAM,CAACM,MAAM,CAACuB,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;cAC/B,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC;cACtC,MAAMK,MAAM,GAAGL,GAAG,CAACE,UAAU,CAAC;cAE9B,oBACE3F,OAAA;gBAAcsB,KAAK,EAAE;kBAAEyD,YAAY,EAAE;gBAAM,CAAE;gBAAArB,QAAA,gBAC3C1D,OAAA;kBAAA0D,QAAA,EAAQ;gBAAO;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACiE,UAAU,eAAC3F,OAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC1CoE,MAAM,IAAIF,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAACN,GAAG,CAAEpD,GAAG,iBACrCpC,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAA0D,QAAA,GAAStB,GAAG,EAAC,GAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,OAAOoE,MAAM,CAAC1D,GAAG,CAAC,KAAK,QAAQ,GAAGwB,IAAI,CAACQ,SAAS,CAAC0B,MAAM,CAAC1D,GAAG,CAAC,CAAC,GAAG0D,MAAM,CAAC1D,GAAG,CAAC;gBAAA,GAD5FA,GAAG;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACN,CAAC;cAAA,GANKgE,GAAG;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOR,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA,eACD,CAAC;IAEP;IAEA,IAAIS,IAAI,KAAK,aAAa,EAAE;MAAA,IAAA4D,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC1B,oBACEjG,OAAA,CAAAE,SAAA;QAAAwD,QAAA,gBACE1D,OAAA,CAACf,GAAG;UAAAyE,QAAA,gBAAC1D,OAAA,CAACL,MAAM;YAAC2B,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxD1B,OAAA,CAACa,MAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAETiC,MAAM,CAACmB,aAAa,iBACnB9E,OAAA;UAAKsB,KAAK,EAAE;YAAEyD,YAAY,EAAE;UAAO,CAAE;UAAArB,QAAA,gBACnC1D,OAAA;YAAA0D,QAAA,EAAQ;UAAW;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACoB,kBAAkB,eAAClG,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5E1B,OAAA;YAAA0D,QAAA,EAAQ;UAAO;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACG,MAAM,eAACjF,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5D1B,OAAA;YAAA0D,QAAA,EAAQ;UAAU;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACqB,UAAU;QAAA;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CACN,EAEAiC,MAAM,CAACM,MAAM,iBACZjE,OAAA;UAAA0D,QAAA,gBACE1D,OAAA;YAAA0D,QAAA,EAAQ;UAAY;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAAA1B,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnC1B,OAAA,CAACf,GAAG;YAAC+B,KAAK,EAAE,EAAA+E,qBAAA,GAAA7E,SAAS,CAACyC,MAAM,CAACM,MAAM,CAAC,cAAA8B,qBAAA,uBAAxBA,qBAAA,CAA0B/E,KAAK,KAAI,sBAAuB;YAAA0C,QAAA,GACnE,EAAAsC,sBAAA,GAAA9E,SAAS,CAACyC,MAAM,CAACM,MAAM,CAAC,cAAA+B,sBAAA,uBAAxBA,sBAAA,CAA0B3E,IAAI,kBAAIrB,OAAA,CAACd,cAAc;cAACoC,KAAK,EAAEP;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACtE,EAAAuE,sBAAA,GAAA/E,SAAS,CAACyC,MAAM,CAACM,MAAM,CAAC,cAAAgC,sBAAA,uBAAxBA,sBAAA,CAA0B7E,KAAK,KAAIuC,MAAM,CAACM,MAAM;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAEN1B,OAAA;YAAKsB,KAAK,EAAE;cAAEgE,SAAS,EAAE;YAAM,CAAE;YAAA5B,QAAA,EAC9BkC,MAAM,CAACQ,OAAO,CAACzC,MAAM,CAAC,CACpB0C,MAAM,CAAC,CAAC,CAACjE,GAAG,CAAC,KAAKA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,eAAe,CAAC,CAC9DoD,GAAG,CAAC,CAAC,CAACpD,GAAG,EAAEkE,KAAK,CAAC,kBAChBtG,OAAA;cAAA0D,QAAA,gBAAe1D,OAAA;gBAAA0D,QAAA,GAAStB,GAAG,EAAC,GAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,OAAO4E,KAAK,KAAK,QAAQ,GAAG1C,IAAI,CAACQ,SAAS,CAACkC,KAAK,CAAC,GAAGA,KAAK;YAAA,GAAvFlE,GAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0F,CACxG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD,CAAC;IAEP;IAEA,IAAIS,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK,oBAAoB,EAAE;MAC9D,oBACEnC,OAAA,CAAAE,SAAA;QAAAwD,QAAA,GAEIvB,IAAI,KAAK,gBAAgB,gBAAGnC,OAAA,CAACf,GAAG;UAAAyE,QAAA,gBAAC1D,OAAA,CAACL,MAAM;YAAC2B,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAAG1B,OAAA,CAACf,GAAG;UAAAyE,QAAA,gBAAC1D,OAAA,CAACT,sBAAsB;YAAC+B,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gCAA4B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAE5K1B,OAAA,CAACa,MAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAETiC,MAAM,CAACmB,aAAa,iBACnB9E,OAAA;UAAKsB,KAAK,EAAE;YAAEyD,YAAY,EAAE;UAAO,CAAE;UAAArB,QAAA,gBACnC1D,OAAA;YAAA0D,QAAA,EAAQ;UAAW;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACE,wBAAwB,eAAChF,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClF1B,OAAA;YAAA0D,QAAA,EAAQ;UAAO;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACG,MAAM,eAACjF,OAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5D1B,OAAA;YAAA0D,QAAA,EAAQ;UAAU;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACiC,MAAM,CAACmB,aAAa,CAACI,SAAS;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEAiC,MAAM,CAAC4C,OAAO,IAAI5C,MAAM,CAAC4C,OAAO,CAAClB,MAAM,GAAG,CAAC,iBAC1CrF,OAAA;UAAA0D,QAAA,gBACE1D,OAAA;YAAA0D,QAAA,EAAQ;UAAa;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B1B,OAAA;YAAIsB,KAAK,EAAE;cAAEgE,SAAS,EAAE,KAAK;cAAEC,WAAW,EAAE;YAAO,CAAE;YAAA7B,QAAA,EAClDC,MAAM,CAAC4C,OAAO,CAACf,GAAG,CAAC,CAACgB,SAAS,EAAEd,GAAG;cAAA,IAAAe,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACjC3G,OAAA;gBAAcsB,KAAK,EAAE;kBAAEyD,YAAY,EAAE;gBAAM,CAAE;gBAAArB,QAAA,gBAC3C1D,OAAA,CAACf,GAAG;kBAAC+B,KAAK,EAAE,EAAAyF,qBAAA,GAAAvF,SAAS,CAACsF,SAAS,CAACvC,MAAM,CAAC,cAAAwC,qBAAA,uBAA3BA,qBAAA,CAA6BzF,KAAK,KAAI,sBAAuB;kBAAA0C,QAAA,GACtE,EAAAgD,sBAAA,GAAAxF,SAAS,CAACsF,SAAS,CAACvC,MAAM,CAAC,cAAAyC,sBAAA,uBAA3BA,sBAAA,CAA6BrF,IAAI,kBAAIrB,OAAA,CAACd,cAAc;oBAACoC,KAAK,EAAEP;kBAAU;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzE,EAAAiF,sBAAA,GAAAzF,SAAS,CAACsF,SAAS,CAACvC,MAAM,CAAC,cAAA0C,sBAAA,uBAA3BA,sBAAA,CAA6BvF,KAAK,KAAIoF,SAAS,CAACvC,MAAM;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,EACL8E,SAAS,CAACV,MAAM,IAAIF,MAAM,CAACQ,OAAO,CAACI,SAAS,CAACV,MAAM,CAAC,CAACN,GAAG,CAAC,CAAC,CAACpD,GAAG,EAAEkE,KAAK,CAAC,kBACrEtG,OAAA;kBAAA0D,QAAA,gBAAe1D,OAAA;oBAAA0D,QAAA,GAAStB,GAAG,EAAC,GAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,OAAO4E,KAAK,KAAK,QAAQ,GAAG1C,IAAI,CAACQ,SAAS,CAACkC,KAAK,CAAC,GAAGA,KAAK;gBAAA,GAAvFlE,GAAG;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA0F,CACxG,CAAC;cAAA,GAPKgE,GAAG;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQR,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA,eACD,CAAC;IAEP;IAEA,IAAIS,IAAI,KAAK,MAAM,EAAE;MAAA,IAAAyE,gBAAA;MACnB,oBACE5G,OAAA,CAAAE,SAAA;QAAAwD,QAAA,gBACE1D,OAAA,CAACf,GAAG;UAAAyE,QAAA,gBAAC1D,OAAA,CAACH,iBAAiB;YAACyB,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,QAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtD1B,OAAA,CAACa,MAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACT,EAAAkF,gBAAA,GAAAjD,MAAM,CAACkD,QAAQ,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBvB,MAAM,IAAG,CAAC,gBAC1BrF,OAAA;UAAIsB,KAAK,EAAE;YAAEwF,MAAM,EAAE,CAAC;YAAEvB,WAAW,EAAE;UAAG,CAAE;UAAA7B,QAAA,EACvCC,MAAM,CAACkD,QAAQ,CAACrB,GAAG,CAAC,CAACuB,IAAI,EAAErB,GAAG,kBAC7B1F,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAA0D,QAAA,gBAAK1D,OAAA;gBAAA0D,QAAA,EAAQ;cAAK;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACqF,IAAI,CAACC,OAAO;YAAA;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChD1B,OAAA;cAAA0D,QAAA,gBAAK1D,OAAA;gBAAA0D,QAAA,EAAQ;cAAK;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACqF,IAAI,CAAC5E,IAAI,KAAK,iBAAiB,GAAG,SAAS,GAAG,SAAS;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAFpFgE,GAAG;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGR,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,gBAEL1B,OAAA;UAAA0D,QAAA,EAAK;QAAY;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACvB;MAAA,eACD,CAAC;IAEP;IAEA,IAAIS,IAAI,KAAK,UAAU,IAAIgB,OAAO,CAAC8D,gBAAgB,EAAE;MACnD,oBACEjH,OAAA,CAAAE,SAAA;QAAAwD,QAAA,gBACE1D,OAAA,CAACf,GAAG;UAAAyE,QAAA,gBAAC1D,OAAA,CAACF,OAAO;YAACwB,KAAK,EAAEP;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAAQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChD1B,OAAA,CAACa,MAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACV1B,OAAA,CAACS,UAAU;UAAAiD,QAAA,EAAEP,OAAO,CAAC8D;QAAgB;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA,eACnD,CAAC;IAEP;IAEA,OAAO,wBAAwB;EACjC,CAAC;EAED,oBACE1B,OAAA,CAACG,gBAAgB;IAACE,IAAI,EAAEA,IAAK;IAAAqD,QAAA,eAC3B1D,OAAA,CAACO,MAAM;MAACF,IAAI,EAAEA,IAAK;MAAAqD,QAAA,EAChBJ,UAAU,CAAC;IAAC;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEvB;AAACwF,GAAA,GApOuBhE,WAAW;AAAA,IAAA5C,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAoG,GAAA;AAAAC,YAAA,CAAA7G,EAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}