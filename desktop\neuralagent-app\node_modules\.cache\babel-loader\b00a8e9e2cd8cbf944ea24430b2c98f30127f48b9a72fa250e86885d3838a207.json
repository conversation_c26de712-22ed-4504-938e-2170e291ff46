{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\FullLoading.js\";\nimport React from 'react';\nimport ClipLoader from \"react-spinners/ClipLoader\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction FullLoading() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100%',\n      zIndex: '2000',\n      position: 'fixed',\n      width: '100%',\n      background: 'var(--primary-color)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      top: '0',\n      bottom: '0',\n      left: '0',\n      right: '0'\n    },\n    children: /*#__PURE__*/_jsxDEV(ClipLoader, {\n      color: \"#fff\",\n      size: 150\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = FullLoading;\nexport default FullLoading;\nvar _c;\n$RefreshReg$(_c, \"FullLoading\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "FullLoading", "style", "height", "zIndex", "position", "width", "background", "display", "alignItems", "justifyContent", "top", "bottom", "left", "right", "children", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/FullLoading.js"], "sourcesContent": ["import React from 'react';\nimport ClipLoader from \"react-spinners/ClipLoader\";\n\nfunction FullLoading() {\n  return (\n    <div style={{height: '100%', zIndex: '2000', position: 'fixed', \n      width: '100%', background: 'var(--primary-color)', display: 'flex', alignItems: 'center',\n      justifyContent: 'center', top: '0', bottom: '0', left: '0', right: '0'}}>\n        <ClipLoader\n          color=\"#fff\"\n          size={150}\n        />\n    </div>\n  );\n}\n\nexport default FullLoading;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,WAAWA,CAAA,EAAG;EACrB,oBACED,OAAA;IAAKE,KAAK,EAAE;MAACC,MAAM,EAAE,MAAM;MAAEC,MAAM,EAAE,MAAM;MAAEC,QAAQ,EAAE,OAAO;MAC5DC,KAAK,EAAE,MAAM;MAAEC,UAAU,EAAE,sBAAsB;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MACxFC,cAAc,EAAE,QAAQ;MAAEC,GAAG,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAG,CAAE;IAAAC,QAAA,eACtEf,OAAA,CAACF,UAAU;MACTkB,KAAK,EAAC,MAAM;MACZC,IAAI,EAAE;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV;AAACC,EAAA,GAXQrB,WAAW;AAapB,eAAeA,WAAW;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}