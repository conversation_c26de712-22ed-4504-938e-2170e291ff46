{"version": 3, "file": "no-unsafe-return.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-return.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AAEnC,8CAAgC;AAChC,kCAA4C;AAE5C,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,6CAA6C;YAC3D,gBAAgB,EAAE;gBAChB,uEAAuE;gBACvE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,sBAAsB,EACpB,mFAAmF;SACtF;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACrD,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,SAAS,qBAAqB,CAC5B,IAAmB;YAMnB,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,OAAO,OAAO,EAAE;gBACd,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;oBACvD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;oBACnD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAClD;oBACA,OAAO,OAAO,CAAC;iBAChB;gBAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;aAC1B;YAED,mFAAmF;YACnF,qDAAqD;YACrD,0BAA0B,CAAC,OAAO,IAAI,CAAC;QACzC,CAAC;QAED,SAAS,WAAW,CAClB,UAAyB,EACzB,gBAA+B,UAAU;YAEzC,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACvE,MAAM,YAAY,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;YACvD,wBAAwB,CAAC,IAAI,CAAC,YAAY,EAAE;gBAC1C,OAAO;aACR;YAED,qEAAqE;YACrE,MAAM,cAAc,GAAG,IAAI,CAAC,4BAA4B,CACtD,OAAO,EACP,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CACtC,CAAC;YACF,MAAM,cAAc,GAAG,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAE/D,yFAAyF;YACzF,+DAA+D;YAC/D,wDAAwD;YACxD,qGAAqG;YACrG,IAAI,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC;gBACrD,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC;gBACjD,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC9C,IAAI,CAAC,YAAY,EAAE;gBACjB,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;aAC1D;YAED,6EAA6E;YAC7E,iFAAiF;YACjF,IAAI,cAAc,CAAC,IAAI,EAAE;gBACvB,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE;oBACxD,IAAI,cAAc,KAAK,SAAS,CAAC,aAAa,EAAE,EAAE;wBAChD,OAAO;qBACR;iBACF;aACF;YAED,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;gBACjC,2FAA2F;gBAC3F,8CAA8C;gBAC9C,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE;oBACxD,MAAM,kBAAkB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;oBACrD,IACE,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG;wBAC5B,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,EAC1C;wBACA,OAAO;qBACR;oBACD,IACE,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;wBACjC,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,OAAO,CAAC,EACxD;wBACA,OAAO;qBACR;iBACF;gBAED,IAAI,SAAS,GAAwC,cAAc,CAAC;gBAEpE,IAAI,CAAC,gBAAgB,EAAE;oBACrB,gBAAgB;oBAChB,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,UAAU,CAAC,CAAC;oBACrD,IACE,cAAc;wBACd,IAAI,CAAC,aAAa,CAChB,IAAI,CAAC,4BAA4B,CAC/B,OAAO,EACP,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,CAC1C,CACF,EACD;wBACA,SAAS,GAAG,kBAAkB,CAAC;qBAChC;iBACF;gBAED,qFAAqF;gBACrF,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO;qBACrD;iBACF,CAAC,CAAC;aACJ;YAED,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE;gBACxD,MAAM,kBAAkB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBACrD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CACpC,cAAc,EACd,kBAAkB,EAClB,OAAO,EACP,UAAU,CACX,CAAC;gBACF,IAAI,CAAC,MAAM,EAAE;oBACX,OAAO;iBACR;gBAED,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;gBACpC,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,wBAAwB;oBACnC,IAAI,EAAE;wBACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;wBACpC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC;qBACzC;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO;iBACR;gBAED,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;YACD,qDAAqD,EAAE,WAAW;SACnE,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}