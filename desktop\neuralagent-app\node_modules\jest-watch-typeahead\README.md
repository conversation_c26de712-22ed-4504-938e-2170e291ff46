[![Build Status](https://github.com/jest-community/jest-watch-typeahead/actions/workflows/nodejs.yml/badge.svg?branch=main)](https://github.com/jest-community/jest-watch-typeahead/actions/workflows/nodejs.yml) [![npm version](https://badge.fury.io/js/jest-watch-typeahead.svg)](https://badge.fury.io/js/jest-watch-typeahead)

<div align="center">
  <a href="https://jestjs.io/">
    <img width="150" height="150" vspace="" hspace="25" src="https://jestjs.io/img/jest.png">
  </a>
  <h1>jest-watch-typeahead</h1>
  <p>Filter your tests by file name or test name</p>
</div>

![watch](https://user-images.githubusercontent.com/574806/40672937-25dab91a-6325-11e8-965d-4e55ef23e135.gif)

## Usage

### Install

Install `jest`_(it needs Jest 27+)_ and `jest-watch-typeahead`

```bash
yarn add --dev jest jest-watch-typeahead

# or with NPM

npm install --save-dev jest jest-watch-typeahead
```

### Add it to your Jest config

In your `package.json`

```json
{
  "jest": {
    "watchPlugins": [
      "jest-watch-typeahead/filename",
      "jest-watch-typeahead/testname"
    ]
  }
}
```

Or in `jest.config.js`

```js
module.exports = {
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname',
  ],
};
```

### Configuring your key and prompt name

```js
module.exports = {
  watchPlugins: [
    [
      'jest-watch-typeahead/filename',
      {
        key: 'k',
        prompt: 'do something with my custom prompt',
      },
    ],
  ],
};
```

### Run Jest in watch mode

```bash
yarn jest --watch
```
