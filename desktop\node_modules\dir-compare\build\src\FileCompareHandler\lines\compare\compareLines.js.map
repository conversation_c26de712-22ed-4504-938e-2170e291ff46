{"version": 3, "file": "compareLines.js", "sourceRoot": "", "sources": ["../../../../../src/FileCompareHandler/lines/compare/compareLines.ts"], "names": [], "mappings": ";;;AAGA,MAAM,wBAAwB,GAAG,oJAAoJ,CAAA;AACrL,MAAM,uBAAuB,GAAG,WAAW,CAAA;AAC3C,MAAM,0BAA0B,GAAG,yEAAyE,CAAA;AAE5G,SAAgB,YAAY,CAAC,MAAgB,EAAE,MAAgB,EAAE,OAAgB;IAC7E,IAAI,OAAO,CAAC,gBAAgB,EAAE;QAC1B,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAA;KACpC;IACD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;IAClD,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QACjB,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAA;SAC5D;KACJ;IACD,OAAO;QACH,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3B,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;KAC9B,CAAA;AACL,CAAC;AAlBD,oCAkBC;AAGD,SAAS,WAAW,CAAC,OAAgB,EAAE,KAAa,EAAE,KAAa;IAC/D,IAAI,OAAO,CAAC,gBAAgB,EAAE;QAC1B,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAA;QAC7B,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAA;KAChC;IACD,IAAI,OAAO,CAAC,iBAAiB,EAAE;QAC3B,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;QACzB,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;KAC5B;IACD,IAAI,OAAO,CAAC,oBAAoB,EAAE;QAC9B,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;QAC3B,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;KAC9B;IACD,OAAO,KAAK,KAAK,KAAK,CAAA;AAC1B,CAAC;AAED,+CAA+C;AAC/C,SAAS,UAAU,CAAC,CAAS;IACzB,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;IAC9C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAA;IAC7D,OAAO,OAAO,GAAG,UAAU,CAAA;AAC/B,CAAC;AAED,SAAS,cAAc,CAAC,CAAS;IAC7B,OAAO,CAAC,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;AACjD,CAAC;AAED,SAAS,YAAY,CAAC,CAAS;IAC3B,OAAO,CAAC,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAA;AACpD,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAe;IACrC,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;AACnD,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC7B,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,CAAA;AAC3C,CAAC;AAED,SAAS,WAAW,CAAC,CAAS;IAC1B,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,CAAA;IACpB,IAAI,UAAU,GAAG,EAAE,CAAA;IACnB,IAAI,OAAO,GAAG,CAAC,CAAA;IACf,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;QACrB,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;YACrB,OAAO;gBACH,UAAU,EAAE,MAAM;gBAClB,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;aAC/B,CAAA;SACJ;QAED;YACI,UAAU,GAAG,IAAI,CAAA;YACjB,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAA;SAChC;KACJ;IACD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,CAAA;AAClC,CAAC"}