{"ast": null, "code": "const size = {\n  xs: '600px',\n  sm: '960px',\n  md: '1264px',\n  lg: '1904px'\n};\nconst int_size = {\n  xs: 600,\n  sm: 960,\n  md: 1264,\n  lg: 1904\n};\nconst checkers = {\n  xsOnly: () => {\n    return window.innerWidth <= int_size.xs;\n  },\n  smAndDown: () => {\n    return window.innerWidth <= int_size.sm;\n  },\n  smAndUp: () => {\n    return window.innerWidth > int_size.xs;\n  },\n  mdAndDown: () => {\n    return window.innerWidth <= int_size.md;\n  },\n  mdAndUp: () => {\n    return window.innerWidth >= int_size.md;\n  },\n  lgAndUp: () => {\n    return window.innerWidth >= int_size.lg;\n  },\n  getFlexWidth(col) {\n    if (col === 1) {\n      return '8.33333333333333%';\n    } else if (col === 2) {\n      return '16.66666666666666%';\n    } else if (col === 3) {\n      return '25%';\n    } else if (col === 4) {\n      return '33.33333333333333%';\n    } else if (col === 5) {\n      return '41.66666666666666%';\n    } else if (col === 6) {\n      return '50%';\n    } else if (col === 7) {\n      return '58.33333333333333%';\n    } else if (col === 8) {\n      return '66.66666666666666%';\n    } else if (col === 9) {\n      return '75%';\n    } else if (col === 10) {\n      return '83.33333333333333%';\n    } else if (col === 11) {\n      return '91.66666666666666%';\n    } else if (col === 12) {\n      return '100%';\n    }\n  }\n};\nconst breakpoint = {\n  size: size,\n  devices_max: {\n    xs: `max-width: ${size.xs}`,\n    sm: `max-width: ${size.sm}`,\n    md: `max-width: ${size.md}`,\n    lg: `max-width: ${size.lg}`\n  },\n  devices_min: {\n    xs: `min-width: ${size.xs}`,\n    sm: `min-width: ${size.sm}`,\n    md: `min-width: ${size.md}`,\n    lg: `min-width: ${size.lg}`\n  },\n  checkers: checkers\n};\nexport default breakpoint;", "map": {"version": 3, "names": ["size", "xs", "sm", "md", "lg", "int_size", "checkers", "xsOnly", "window", "innerWidth", "smAndDown", "smAndUp", "mdAndDown", "mdAndUp", "lgAndUp", "getFlexWidth", "col", "breakpoint", "devices_max", "devices_min"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/utils/breakpoint.js"], "sourcesContent": ["const size = {\n  xs: '600px',\n  sm: '960px',\n  md: '1264px',\n  lg: '1904px',\n};\n\nconst int_size = {\n  xs: 600,\n  sm: 960,\n  md: 1264,\n  lg: 1904,\n};\n\nconst checkers = {\n  xsOnly: () => {\n    return window.innerWidth <= int_size.xs;\n  },\n  smAndDown: () => {\n    return window.innerWidth <= int_size.sm;\n  },\n  smAndUp: () => {\n    return window.innerWidth > int_size.xs;\n  },\n  mdAndDown: () => {\n    return window.innerWidth <= int_size.md;\n  },\n  mdAndUp: () => {\n    return window.innerWidth >= int_size.md;\n  },\n  lgAndUp: () => {\n    return window.innerWidth >= int_size.lg;\n  },\n  getFlexWidth (col) {\n    if (col === 1) {\n      return '8.33333333333333%';\n    } else if (col === 2) {\n      return '16.66666666666666%';\n    } else if (col === 3) {\n      return '25%';\n    } else if (col === 4) {\n      return '33.33333333333333%';\n    } else if (col === 5) {\n      return '41.66666666666666%';\n    } else if (col === 6) {\n      return '50%';\n    } else if (col === 7) {\n      return '58.33333333333333%';\n    } else if (col === 8) {\n      return '66.66666666666666%';\n    } else if (col === 9) {\n      return '75%';\n    } else if (col === 10) {\n      return '83.33333333333333%';\n    } else if (col === 11) {\n      return '91.66666666666666%';\n    } else if (col === 12) {\n      return '100%';\n    }\n  }\n};\n\nconst breakpoint = {\n  size: size,\n  devices_max: {\n    xs: `max-width: ${size.xs}`,\n    sm: `max-width: ${size.sm}`,\n    md: `max-width: ${size.md}`,\n    lg: `max-width: ${size.lg}`,\n  },\n  devices_min: {\n    xs: `min-width: ${size.xs}`,\n    sm: `min-width: ${size.sm}`,\n    md: `min-width: ${size.md}`,\n    lg: `min-width: ${size.lg}`,\n  },\n  checkers: checkers,\n}\n\nexport default breakpoint;"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACXC,EAAE,EAAE,OAAO;EACXC,EAAE,EAAE,OAAO;EACXC,EAAE,EAAE,QAAQ;EACZC,EAAE,EAAE;AACN,CAAC;AAED,MAAMC,QAAQ,GAAG;EACfJ,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE;AACN,CAAC;AAED,MAAME,QAAQ,GAAG;EACfC,MAAM,EAAEA,CAAA,KAAM;IACZ,OAAOC,MAAM,CAACC,UAAU,IAAIJ,QAAQ,CAACJ,EAAE;EACzC,CAAC;EACDS,SAAS,EAAEA,CAAA,KAAM;IACf,OAAOF,MAAM,CAACC,UAAU,IAAIJ,QAAQ,CAACH,EAAE;EACzC,CAAC;EACDS,OAAO,EAAEA,CAAA,KAAM;IACb,OAAOH,MAAM,CAACC,UAAU,GAAGJ,QAAQ,CAACJ,EAAE;EACxC,CAAC;EACDW,SAAS,EAAEA,CAAA,KAAM;IACf,OAAOJ,MAAM,CAACC,UAAU,IAAIJ,QAAQ,CAACF,EAAE;EACzC,CAAC;EACDU,OAAO,EAAEA,CAAA,KAAM;IACb,OAAOL,MAAM,CAACC,UAAU,IAAIJ,QAAQ,CAACF,EAAE;EACzC,CAAC;EACDW,OAAO,EAAEA,CAAA,KAAM;IACb,OAAON,MAAM,CAACC,UAAU,IAAIJ,QAAQ,CAACD,EAAE;EACzC,CAAC;EACDW,YAAYA,CAAEC,GAAG,EAAE;IACjB,IAAIA,GAAG,KAAK,CAAC,EAAE;MACb,OAAO,mBAAmB;IAC5B,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;MACpB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,GAAG,KAAK,EAAE,EAAE;MACrB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,EAAE,EAAE;MACrB,OAAO,oBAAoB;IAC7B,CAAC,MAAM,IAAIA,GAAG,KAAK,EAAE,EAAE;MACrB,OAAO,MAAM;IACf;EACF;AACF,CAAC;AAED,MAAMC,UAAU,GAAG;EACjBjB,IAAI,EAAEA,IAAI;EACVkB,WAAW,EAAE;IACXjB,EAAE,EAAE,cAAcD,IAAI,CAACC,EAAE,EAAE;IAC3BC,EAAE,EAAE,cAAcF,IAAI,CAACE,EAAE,EAAE;IAC3BC,EAAE,EAAE,cAAcH,IAAI,CAACG,EAAE,EAAE;IAC3BC,EAAE,EAAE,cAAcJ,IAAI,CAACI,EAAE;EAC3B,CAAC;EACDe,WAAW,EAAE;IACXlB,EAAE,EAAE,cAAcD,IAAI,CAACC,EAAE,EAAE;IAC3BC,EAAE,EAAE,cAAcF,IAAI,CAACE,EAAE,EAAE;IAC3BC,EAAE,EAAE,cAAcH,IAAI,CAACG,EAAE,EAAE;IAC3BC,EAAE,EAAE,cAAcJ,IAAI,CAACI,EAAE;EAC3B,CAAC;EACDE,QAAQ,EAAEA;AACZ,CAAC;AAED,eAAeW,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}