{"version": 3, "file": "workbox-core.prod.js", "sources": ["../_version.js", "../_private/logger.js", "../models/messages/messageGenerator.js", "../_private/WorkboxError.js", "../_private/assert.js", "../models/quotaErrorCallbacks.js", "../_private/cacheNames.js", "../_private/cacheMatchIgnoreParams.js", "../_private/canConstructReadableStream.js", "../_private/canConstructResponseFromBodyStream.js", "../_private/timeout.js", "../_private/dontWaitFor.js", "../_private/Deferred.js", "../_private/executeQuotaErrorCallbacks.js", "../_private/getFriendlyURL.js", "../_private/resultingClientExists.js", "../_private/waitUntil.js", "../cacheNames.js", "../clientsClaim.js", "../copyResponse.js", "../registerQuotaErrorCallback.js", "../setCacheNameDetails.js", "../skipWaiting.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-560470923\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messages } from './messages.js';\nimport '../../_version.js';\nconst fallback = (code, ...args) => {\n    let msg = code;\n    if (args.length > 0) {\n        msg += ` :: ${JSON.stringify(args)}`;\n    }\n    return msg;\n};\nconst generatorFunction = (code, details = {}) => {\n    const message = messages[code];\n    if (!message) {\n        throw new Error(`Unable to find message for code '${code}'.`);\n    }\n    return message(details);\n};\nexport const messageGenerator = process.env.NODE_ENV === 'production' ? fallback : generatorFunction;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messageGenerator } from '../models/messages/messageGenerator.js';\nimport '../_version.js';\n/**\n * Workbox errors should be thrown with this class.\n * This allows use to ensure the type easily in tests,\n * helps developers identify errors from workbox\n * easily and allows use to optimise error\n * messages correctly.\n *\n * @private\n */\nclass WorkboxError extends Error {\n    /**\n     *\n     * @param {string} errorCode The error code that\n     * identifies this particular error.\n     * @param {Object=} details Any relevant arguments\n     * that will help developers identify issues should\n     * be added as a key on the context object.\n     */\n    constructor(errorCode, details) {\n        const message = messageGenerator(errorCode, details);\n        super(message);\n        this.name = errorCode;\n        this.details = details;\n    }\n}\nexport { WorkboxError };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from '../_private/WorkboxError.js';\nimport '../_version.js';\n/*\n * This method throws if the supplied value is not an array.\n * The destructed values are required to produce a meaningful error for users.\n * The destructed and restructured object is so it's clear what is\n * needed.\n */\nconst isArray = (value, details) => {\n    if (!Array.isArray(value)) {\n        throw new WorkboxError('not-an-array', details);\n    }\n};\nconst hasMethod = (object, expectedMethod, details) => {\n    const type = typeof object[expectedMethod];\n    if (type !== 'function') {\n        details['expectedMethod'] = expectedMethod;\n        throw new WorkboxError('missing-a-method', details);\n    }\n};\nconst isType = (object, expectedType, details) => {\n    if (typeof object !== expectedType) {\n        details['expectedType'] = expectedType;\n        throw new WorkboxError('incorrect-type', details);\n    }\n};\nconst isInstance = (object, \n// Need the general type to do the check later.\n// eslint-disable-next-line @typescript-eslint/ban-types\nexpectedClass, details) => {\n    if (!(object instanceof expectedClass)) {\n        details['expectedClassName'] = expectedClass.name;\n        throw new WorkboxError('incorrect-class', details);\n    }\n};\nconst isOneOf = (value, validValues, details) => {\n    if (!validValues.includes(value)) {\n        details['validValueDescription'] = `Valid values are ${JSON.stringify(validValues)}.`;\n        throw new WorkboxError('invalid-value', details);\n    }\n};\nconst isArrayOfClass = (value, \n// Need general type to do check later.\nexpectedClass, // eslint-disable-line\ndetails) => {\n    const error = new WorkboxError('not-array-of-class', details);\n    if (!Array.isArray(value)) {\n        throw error;\n    }\n    for (const item of value) {\n        if (!(item instanceof expectedClass)) {\n            throw error;\n        }\n    }\n};\nconst finalAssertExports = process.env.NODE_ENV === 'production'\n    ? null\n    : {\n        hasMethod,\n        isArray,\n        isInstance,\n        isOneOf,\n        isType,\n        isArrayOfClass,\n    };\nexport { finalAssertExports as assert };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\n// Can't change Function type right now.\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nfunction stripParams(fullURL, ignoreParams) {\n    const strippedURL = new URL(fullURL);\n    for (const param of ignoreParams) {\n        strippedURL.searchParams.delete(param);\n    }\n    return strippedURL.href;\n}\n/**\n * Matches an item in the cache, ignoring specific URL params. This is similar\n * to the `ignoreSearch` option, but it allows you to ignore just specific\n * params (while continuing to match on the others).\n *\n * @private\n * @param {Cache} cache\n * @param {Request} request\n * @param {Object} matchOptions\n * @param {Array<string>} ignoreParams\n * @return {Promise<Response|undefined>}\n */\nasync function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {\n    const strippedRequestURL = stripParams(request.url, ignoreParams);\n    // If the request doesn't include any ignored params, match as normal.\n    if (request.url === strippedRequestURL) {\n        return cache.match(request, matchOptions);\n    }\n    // Otherwise, match by comparing keys\n    const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });\n    const cacheKeys = await cache.keys(request, keysOptions);\n    for (const cacheKey of cacheKeys) {\n        const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);\n        if (strippedRequestURL === strippedCacheKeyURL) {\n            return cache.match(cacheKey, matchOptions);\n        }\n    }\n    return;\n}\nexport { cacheMatchIgnoreParams };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * object.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `ReadableStream`, `false` otherwise.\n *\n * @private\n */\nfunction canConstructReadableStream() {\n    if (supportStatus === undefined) {\n        // See https://github.com/GoogleChrome/workbox/issues/1473\n        try {\n            new ReadableStream({ start() { } });\n            supportStatus = true;\n        }\n        catch (error) {\n            supportStatus = false;\n        }\n    }\n    return supportStatus;\n}\nexport { canConstructReadableStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns a promise that resolves and the passed number of milliseconds.\n * This utility is an async/await-friendly version of `setTimeout`.\n *\n * @param {number} ms\n * @return {Promise}\n * @private\n */\nexport function timeout(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst getFriendlyURL = (url) => {\n    const urlObj = new URL(String(url), location.href);\n    // See https://github.com/GoogleChrome/workbox/issues/2323\n    // We want to include everything, except for the origin if it's same-origin.\n    return urlObj.href.replace(new RegExp(`^${location.origin}`), '');\n};\nexport { getFriendlyURL };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { timeout } from './timeout.js';\nimport '../_version.js';\nconst MAX_RETRY_TIME = 2000;\n/**\n * Returns a promise that resolves to a window client matching the passed\n * `resultingClientId`. For browsers that don't support `resultingClientId`\n * or if waiting for the resulting client to apper takes too long, resolve to\n * `undefined`.\n *\n * @param {string} [resultingClientId]\n * @return {Promise<Client|undefined>}\n * @private\n */\nexport async function resultingClientExists(resultingClientId) {\n    if (!resultingClientId) {\n        return;\n    }\n    let existingWindows = await self.clients.matchAll({ type: 'window' });\n    const existingWindowIds = new Set(existingWindows.map((w) => w.id));\n    let resultingWindow;\n    const startTime = performance.now();\n    // Only wait up to `MAX_RETRY_TIME` to find a matching client.\n    while (performance.now() - startTime < MAX_RETRY_TIME) {\n        existingWindows = await self.clients.matchAll({ type: 'window' });\n        resultingWindow = existingWindows.find((w) => {\n            if (resultingClientId) {\n                // If we have a `resultingClientId`, we can match on that.\n                return w.id === resultingClientId;\n            }\n            else {\n                // Otherwise match on finding a window not in `existingWindowIds`.\n                return !existingWindowIds.has(w.id);\n            }\n        });\n        if (resultingWindow) {\n            break;\n        }\n        // Sleep for 100ms and retry.\n        await timeout(100);\n    }\n    return resultingWindow;\n}\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A utility method that makes it easier to use `event.waitUntil` with\n * async functions and return the result.\n *\n * @param {ExtendableEvent} event\n * @param {Function} asyncFn\n * @return {Function}\n * @private\n */\nfunction waitUntil(event, asyncFn) {\n    const returnPromise = asyncFn();\n    event.waitUntil(returnPromise);\n    return returnPromise;\n}\nexport { waitUntil };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames as _cacheNames } from './_private/cacheNames.js';\nimport './_version.js';\n/**\n * Get the current cache names and prefix/suffix used by Workbox.\n *\n * `cacheNames.precache` is used for precached assets,\n * `cacheNames.googleAnalytics` is used by `workbox-google-analytics` to\n * store `analytics.js`, and `cacheNames.runtime` is used for everything else.\n *\n * `cacheNames.prefix` can be used to retrieve just the current prefix value.\n * `cacheNames.suffix` can be used to retrieve just the current suffix value.\n *\n * @return {Object} An object with `precache`, `runtime`, `prefix`, and\n *     `googleAnalytics` properties.\n *\n * @memberof workbox-core\n */\nconst cacheNames = {\n    get googleAnalytics() {\n        return _cacheNames.getGoogleAnalyticsName();\n    },\n    get precache() {\n        return _cacheNames.getPrecacheName();\n    },\n    get prefix() {\n        return _cacheNames.getPrefix();\n    },\n    get runtime() {\n        return _cacheNames.getRuntimeName();\n    },\n    get suffix() {\n        return _cacheNames.getSuffix();\n    },\n};\nexport { cacheNames };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Claim any currently available clients once the service worker\n * becomes active. This is normally used in conjunction with `skipWaiting()`.\n *\n * @memberof workbox-core\n */\nfunction clientsClaim() {\n    self.addEventListener('activate', () => self.clients.claim());\n}\nexport { clientsClaim };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * This method is intentionally limited to same-origin responses, regardless of\n * whether CORS was used or not.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof workbox-core\n */\nasync function copyResponse(response, modifier) {\n    let origin = null;\n    // If response.url isn't set, assume it's cross-origin and keep origin null.\n    if (response.url) {\n        const responseURL = new URL(response.url);\n        origin = responseURL.origin;\n    }\n    if (origin !== self.location.origin) {\n        throw new WorkboxError('cross-origin-copy-response', { origin });\n    }\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream()\n        ? clonedResponse.body\n        : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport { assert } from './_private/assert.js';\nimport { quotaErrorCallbacks } from './models/quotaErrorCallbacks.js';\nimport './_version.js';\n/**\n * Adds a function to the set of quotaErrorCallbacks that will be executed if\n * there's a quota error.\n *\n * @param {Function} callback\n * @memberof workbox-core\n */\n// Can't change Function type\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction registerQuotaErrorCallback(callback) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(callback, 'function', {\n            moduleName: 'workbox-core',\n            funcName: 'register',\n            paramName: 'callback',\n        });\n    }\n    quotaErrorCallbacks.add(callback);\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Registered a callback to respond to quota errors.', callback);\n    }\n}\nexport { registerQuotaErrorCallback };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from './_private/assert.js';\nimport { cacheNames } from './_private/cacheNames.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Modifies the default cache names used by the Workbox packages.\n * Cache names are generated as `<prefix>-<Cache Name>-<suffix>`.\n *\n * @param {Object} details\n * @param {Object} [details.prefix] The string to add to the beginning of\n *     the precache and runtime cache names.\n * @param {Object} [details.suffix] The string to add to the end of\n *     the precache and runtime cache names.\n * @param {Object} [details.precache] The cache name to use for precache\n *     caching.\n * @param {Object} [details.runtime] The cache name to use for runtime caching.\n * @param {Object} [details.googleAnalytics] The cache name to use for\n *     `workbox-google-analytics` caching.\n *\n * @memberof workbox-core\n */\nfunction setCacheNameDetails(details) {\n    if (process.env.NODE_ENV !== 'production') {\n        Object.keys(details).forEach((key) => {\n            assert.isType(details[key], 'string', {\n                moduleName: 'workbox-core',\n                funcName: 'setCacheNameDetails',\n                paramName: `details.${key}`,\n            });\n        });\n        if ('precache' in details && details['precache'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'precache',\n                value: details['precache'],\n            });\n        }\n        if ('runtime' in details && details['runtime'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'runtime',\n                value: details['runtime'],\n            });\n        }\n        if ('googleAnalytics' in details &&\n            details['googleAnalytics'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'googleAnalytics',\n                value: details['googleAnalytics'],\n            });\n        }\n    }\n    cacheNames.updateDetails(details);\n}\nexport { setCacheNameDetails };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport './_version.js';\n/**\n * This method is deprecated, and will be removed in Workbox v7.\n *\n * Calling self.skipWaiting() is equivalent, and should be used instead.\n *\n * @memberof workbox-core\n */\nfunction skipWaiting() {\n    // Just call self.skipWaiting() directly.\n    // See https://github.com/GoogleChrome/workbox/issues/2525\n    if (process.env.NODE_ENV !== 'production') {\n        logger.warn(`skipWaiting() from workbox-core is no longer recommended ` +\n            `and will be removed in Workbox v7. Using self.skipWaiting() instead ` +\n            `is equivalent.`);\n    }\n    void self.skipWaiting();\n}\nexport { skipWaiting };\n"], "names": ["self", "_", "e", "messageGenerator", "code", "args", "msg", "length", "JSON", "stringify", "WorkboxError", "Error", "constructor", "errorCode", "details", "name", "quotaErrorCallbacks", "Set", "_cacheNameDetails", "googleAnalytics", "precache", "prefix", "runtime", "suffix", "registration", "scope", "_createCacheName", "cacheName", "filter", "value", "join", "cacheNames", "updateDetails", "fn", "key", "Object", "keys", "eachCacheNameDetail", "getGoogleAnalyticsName", "userCacheName", "getPrecacheName", "getPrefix", "getRuntimeName", "getSuffix", "stripParams", "fullURL", "ignoreParams", "strippedURL", "URL", "param", "searchParams", "delete", "href", "supportStatus", "canConstructResponseFromBodyStream", "undefined", "testResponse", "Response", "body", "error", "timeout", "ms", "Promise", "resolve", "setTimeout", "async", "cache", "request", "matchOptions", "strippedRequestURL", "url", "match", "keysOptions", "assign", "ignoreSearch", "cacheKeys", "cache<PERSON>ey", "ReadableStream", "start", "promise", "then", "reject", "callback", "String", "location", "replace", "RegExp", "origin", "resultingClientId", "existingWindows", "clients", "matchAll", "type", "existingWindowIds", "map", "w", "id", "resultingWindow", "startTime", "performance", "now", "find", "has", "event", "asyncFn", "returnPromise", "waitUntil", "_cacheNames", "addEventListener", "claim", "response", "modifier", "clonedResponse", "clone", "responseInit", "headers", "Headers", "status", "statusText", "modifiedResponseInit", "blob", "add", "skipWaiting"], "mappings": "yEAEA,IACIA,KAAK,uBAAyBC,GACjC,CACD,MAAOC,ICEP,MCgBaC,EAdI,CAACC,KAASC,SACnBC,EAAMF,SACNC,EAAKE,OAAS,IACdD,GAAQ,OAAME,KAAKC,UAAUJ,MAE1BC,CAAP,ECIJ,MAAMI,UAAqBC,MASvBC,YAAYC,EAAWC,SACHX,EAAiBU,EAAWC,SAEvCC,KAAOF,OACPC,QAAUA,GC+BvB,MCnDME,EAAsB,IAAIC,ICHhC,MAAMC,EAAoB,CACtBC,gBAAiB,kBACjBC,SAAU,cACVC,OAAQ,UACRC,QAAS,UACTC,OAAgC,oBAAjBC,aAA+BA,aAAaC,MAAQ,IAEjEC,EAAoBC,GACf,CAACT,EAAkBG,OAAQM,EAAWT,EAAkBK,QAC1DK,QAAQC,GAAUA,GAASA,EAAMtB,OAAS,IAC1CuB,KAAK,KAODC,EAAa,CACtBC,cAAgBlB,IANSmB,SACpB,MAAMC,KAAOC,OAAOC,KAAKlB,GAC1Be,EAAGC,IAKHG,EAAqBH,IACW,iBAAjBpB,EAAQoB,KACfhB,EAAkBgB,GAAOpB,EAAQoB,MAFzC,EAMJI,uBAAyBC,GACdA,GAAiBb,EAAiBR,EAAkBC,iBAE/DqB,gBAAkBD,GACPA,GAAiBb,EAAiBR,EAAkBE,UAE/DqB,UAAW,IACAvB,EAAkBG,OAE7BqB,eAAiBH,GACNA,GAAiBb,EAAiBR,EAAkBI,SAE/DqB,UAAW,IACAzB,EAAkBK,QCvCjC,SAASqB,EAAYC,EAASC,SACpBC,EAAc,IAAIC,IAAIH,OACvB,MAAMI,KAASH,EAChBC,EAAYG,aAAaC,OAAOF,UAE7BF,EAAYK,IACtB,CCLD,IAAIC,ECAAA,EAUJ,SAASC,YACiBC,IAAlBF,EAA6B,OACvBG,EAAe,IAAIC,SAAS,OAC9B,SAAUD,UAEFC,SAASD,EAAaE,MAC1BL,GAAgB,EAEpB,MAAOM,GACHN,GAAgB,EAGxBA,GAAgB,SAEbA,CACV,CClBM,SAASO,EAAQC,UACb,IAAIC,SAASC,GAAYC,WAAWD,EAASF,IACvD,4CN8CK,4BGrCNI,eAAsCC,EAAOC,EAASrB,EAAcsB,SAC1DC,EAAqBzB,EAAYuB,EAAQG,IAAKxB,MAEhDqB,EAAQG,MAAQD,SACTH,EAAMK,MAAMJ,EAASC,SAG1BI,EAAcrC,OAAOsC,OAAOtC,OAAOsC,OAAO,GAAIL,GAAe,CAAEM,cAAc,IAC7EC,QAAkBT,EAAM9B,KAAK+B,EAASK,OACvC,MAAMI,KAAYD,EAAW,IAE1BN,IADwBzB,EAAYgC,EAASN,IAAKxB,UAE3CoB,EAAMK,MAAMK,EAAUR,GAIxC,0CCvBD,mBAC0Bb,IAAlBF,UAGQwB,eAAe,CAAEC,YACrBzB,GAAgB,EAEpB,MAAOM,GACHN,GAAgB,SAGjBA,CACV,mDGnBM,SAAqB0B,GAEnBA,EAAQC,MAAK,QACrB,WCCD,MAIIpE,mBACSmE,QAAU,IAAIjB,SAAQ,CAACC,EAASkB,UAC5BlB,QAAUA,OACVkB,OAASA,CAAd,iCCNZhB,qBAKS,MAAMiB,KAAYlE,QACbkE,GAQb,iBCvBuBZ,GACL,IAAItB,IAAImC,OAAOb,GAAMc,SAAShC,MAG/BA,KAAKiC,QAAQ,IAAIC,OAAQ,IAAGF,SAASG,UAAW,WbJ5D,2BcWCtB,eAAqCuB,OACnCA,aAGDC,QAAwBzF,KAAK0F,QAAQC,SAAS,CAAEC,KAAM,iBACpDC,EAAoB,IAAI5E,IAAIwE,EAAgBK,KAAKC,GAAMA,EAAEC,UAC3DC,QACEC,EAAYC,YAAYC,WAEvBD,YAAYC,MAAQF,EApBR,MAqBfT,QAAwBzF,KAAK0F,QAAQC,SAAS,CAAEC,KAAM,WACtDK,EAAkBR,EAAgBY,MAAMN,GAChCP,EAEOO,EAAEC,KAAOR,GAIRK,EAAkBS,IAAIP,EAAEC,OAGpCC,UAIErC,EAAQ,YAEXqC,CACV,sBC/BD,SAAmBM,EAAOC,SAChBC,EAAgBD,WACtBD,EAAMG,UAAUD,GACTA,CACV,yBCIK1E,EAAa,CACXZ,6BACOwF,EAAYrE,0BAEnBlB,sBACOuF,EAAYnE,mBAEnBnB,oBACOsF,EAAYlE,aAEnBnB,qBACOqF,EAAYjE,kBAEnBnB,oBACOoF,EAAYhE,gECxB3B,WACI3C,KAAK4G,iBAAiB,YAAY,IAAM5G,KAAK0F,QAAQmB,SACxD,iBCaD5C,eAA4B6C,EAAUC,OAC9BxB,EAAS,QAETuB,EAASxC,IAAK,CAEdiB,EADoB,IAAIvC,IAAI8D,EAASxC,KAChBiB,UAErBA,IAAWvF,KAAKoF,SAASG,aACnB,IAAI7E,EAAa,6BAA8B,CAAE6E,iBAErDyB,EAAiBF,EAASG,QAE1BC,EAAe,CACjBC,QAAS,IAAIC,QAAQJ,EAAeG,SACpCE,OAAQL,EAAeK,OACvBC,WAAYN,EAAeM,YAGzBC,EAAuBR,EAAWA,EAASG,GAAgBA,EAI3DxD,EAAOJ,IACP0D,EAAetD,WACTsD,EAAeQ,cACpB,IAAI/D,SAASC,EAAM6D,EAC7B,+BCnCD,SAAoCrC,GAQhClE,EAAoByG,IAAIvC,EAI3B,wBCJD,SAA6BpE,GA6BzBiB,EAAWC,cAAclB,EAC5B,gBC1CD,WAQSd,KAAK0H,aACb"}