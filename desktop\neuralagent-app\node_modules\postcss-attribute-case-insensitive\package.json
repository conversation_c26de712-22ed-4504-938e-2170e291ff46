{"name": "postcss-attribute-case-insensitive", "description": "Enable support for case insensitive attribute matching in selectors", "version": "5.0.2", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE", "README.md", "dist"], "dependencies": {"postcss-selector-parser": "^6.0.10"}, "peerDependencies": {"postcss": "^8.2"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-attribute-case-insensitive#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-attribute-case-insensitive"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["CSS4", "attribute", "css", "insensitive", "postcss", "postcss-plugin", "sensitive"], "csstools": {"cssdbId": "case-insensitive-attributes", "exportName": "postcssAttributeCaseInsensitive", "humanReadableName": "PostCSS Attribute Case Insensitive", "specUrl": "https://www.w3.org/TR/selectors4/#attribute-case"}, "volta": {"extends": "../../package.json"}}