{"version": 3, "sources": ["../src/index.ts"], "names": ["cosmiconfig", "moduleName", "options", "normalizedOptions", "normalizeOptions", "explorer", "Explorer", "search", "bind", "load", "clearLoadCache", "clearSearchCache", "clearCaches", "cosmiconfigSync", "explorerSync", "ExplorerSync", "searchSync", "loadSync", "defaultLoaders", "Object", "freeze", "loaders", "loadJs", "loadJson", "loadYaml", "noExt", "defaults", "packageProp", "searchPlaces", "ignoreEmptySearchPlaces", "stopDir", "os", "homedir", "cache", "transform", "identity", "x"], "mappings": ";;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAyCA;AACA,SAASA,WAAT,CAAqBC,UAArB,EAAyCC,OAAgB,GAAG,EAA5D,EAAgE;AAC9D,QAAMC,iBAAkC,GAAGC,gBAAgB,CACzDH,UADyD,EAEzDC,OAFyD,CAA3D;AAKA,QAAMG,QAAQ,GAAG,IAAIC,kBAAJ,CAAaH,iBAAb,CAAjB;AAEA,SAAO;AACLI,IAAAA,MAAM,EAAEF,QAAQ,CAACE,MAAT,CAAgBC,IAAhB,CAAqBH,QAArB,CADH;AAELI,IAAAA,IAAI,EAAEJ,QAAQ,CAACI,IAAT,CAAcD,IAAd,CAAmBH,QAAnB,CAFD;AAGLK,IAAAA,cAAc,EAAEL,QAAQ,CAACK,cAAT,CAAwBF,IAAxB,CAA6BH,QAA7B,CAHX;AAILM,IAAAA,gBAAgB,EAAEN,QAAQ,CAACM,gBAAT,CAA0BH,IAA1B,CAA+BH,QAA/B,CAJb;AAKLO,IAAAA,WAAW,EAAEP,QAAQ,CAACO,WAAT,CAAqBJ,IAArB,CAA0BH,QAA1B;AALR,GAAP;AAOD,C,CAED;;;AACA,SAASQ,eAAT,CAAyBZ,UAAzB,EAA6CC,OAAoB,GAAG,EAApE,EAAwE;AACtE,QAAMC,iBAAsC,GAAGC,gBAAgB,CAC7DH,UAD6D,EAE7DC,OAF6D,CAA/D;AAKA,QAAMY,YAAY,GAAG,IAAIC,0BAAJ,CAAiBZ,iBAAjB,CAArB;AAEA,SAAO;AACLI,IAAAA,MAAM,EAAEO,YAAY,CAACE,UAAb,CAAwBR,IAAxB,CAA6BM,YAA7B,CADH;AAELL,IAAAA,IAAI,EAAEK,YAAY,CAACG,QAAb,CAAsBT,IAAtB,CAA2BM,YAA3B,CAFD;AAGLJ,IAAAA,cAAc,EAAEI,YAAY,CAACJ,cAAb,CAA4BF,IAA5B,CAAiCM,YAAjC,CAHX;AAILH,IAAAA,gBAAgB,EAAEG,YAAY,CAACH,gBAAb,CAA8BH,IAA9B,CAAmCM,YAAnC,CAJb;AAKLF,IAAAA,WAAW,EAAEE,YAAY,CAACF,WAAb,CAAyBJ,IAAzB,CAA8BM,YAA9B;AALR,GAAP;AAOD,C,CAED;;;AACA,MAAMI,cAAc,GAAGC,MAAM,CAACC,MAAP,CAAc;AACnC,SAAOC,iBAAQC,MADoB;AAEnC,WAASD,iBAAQE,QAFkB;AAGnC,WAASF,iBAAQG,QAHkB;AAInC,UAAQH,iBAAQG,QAJmB;AAKnCC,EAAAA,KAAK,EAAEJ,iBAAQG;AALoB,CAAd,CAAvB;;;AAgBA,SAASpB,gBAAT,CACEH,UADF,EAEEC,OAFF,EAGyC;AACvC,QAAMwB,QAA+C,GAAG;AACtDC,IAAAA,WAAW,EAAE1B,UADyC;AAEtD2B,IAAAA,YAAY,EAAE,CACZ,cADY,EAEX,IAAG3B,UAAW,IAFH,EAGX,IAAGA,UAAW,SAHH,EAIX,IAAGA,UAAW,SAJH,EAKX,IAAGA,UAAW,QALH,EAMX,IAAGA,UAAW,OANH,EAOX,GAAEA,UAAW,YAPF,CAFwC;AAWtD4B,IAAAA,uBAAuB,EAAE,IAX6B;AAYtDC,IAAAA,OAAO,EAAEC,YAAGC,OAAH,EAZ6C;AAatDC,IAAAA,KAAK,EAAE,IAb+C;AActDC,IAAAA,SAAS,EAAEC,QAd2C;AAetDd,IAAAA,OAAO,EAAEH;AAf6C,GAAxD;AAkBA,QAAMf,iBAAwD,GAAG,EAC/D,GAAGuB,QAD4D;AAE/D,OAAGxB,OAF4D;AAG/DmB,IAAAA,OAAO,EAAE,EACP,GAAGK,QAAQ,CAACL,OADL;AAEP,SAAGnB,OAAO,CAACmB;AAFJ;AAHsD,GAAjE;AASA,SAAOlB,iBAAP;AACD;;AAED,MAAMgC,QAAuB,GAAG,SAASA,QAAT,CAAkBC,CAAlB,EAAqB;AACnD,SAAOA,CAAP;AACD,CAFD", "sourcesContent": ["import os from 'os';\nimport { Explorer } from './Explorer';\nimport { ExplorerSync } from './ExplorerSync';\nimport { loaders } from './loaders';\nimport {\n  Config,\n  CosmiconfigResult,\n  ExplorerOptions,\n  ExplorerOptionsSync,\n  Loaders,\n  LoadersSync,\n} from './types';\n\ntype LoaderResult = Config | null;\nexport type Loader =\n  | ((filepath: string, content: string) => Promise<LoaderResult>)\n  | LoaderSync;\nexport type LoaderSync = (filepath: string, content: string) => LoaderResult;\n\nexport type Transform =\n  | ((CosmiconfigResult: CosmiconfigResult) => Promise<CosmiconfigResult>)\n  | TransformSync;\n\nexport type TransformSync = (\n  CosmiconfigResult: CosmiconfigResult,\n) => CosmiconfigResult;\n\ninterface OptionsBase {\n  packageProp?: string;\n  searchPlaces?: Array<string>;\n  ignoreEmptySearchPlaces?: boolean;\n  stopDir?: string;\n  cache?: boolean;\n}\n\nexport interface Options extends OptionsBase {\n  loaders?: Loaders;\n  transform?: Transform;\n}\n\nexport interface OptionsSync extends OptionsBase {\n  loaders?: LoadersSync;\n  transform?: TransformSync;\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nfunction cosmiconfig(moduleName: string, options: Options = {}) {\n  const normalizedOptions: ExplorerOptions = normalizeOptions(\n    moduleName,\n    options,\n  );\n\n  const explorer = new Explorer(normalizedOptions);\n\n  return {\n    search: explorer.search.bind(explorer),\n    load: explorer.load.bind(explorer),\n    clearLoadCache: explorer.clearLoadCache.bind(explorer),\n    clearSearchCache: explorer.clearSearchCache.bind(explorer),\n    clearCaches: explorer.clearCaches.bind(explorer),\n  } as const;\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nfunction cosmiconfigSync(moduleName: string, options: OptionsSync = {}) {\n  const normalizedOptions: ExplorerOptionsSync = normalizeOptions(\n    moduleName,\n    options,\n  );\n\n  const explorerSync = new ExplorerSync(normalizedOptions);\n\n  return {\n    search: explorerSync.searchSync.bind(explorerSync),\n    load: explorerSync.loadSync.bind(explorerSync),\n    clearLoadCache: explorerSync.clearLoadCache.bind(explorerSync),\n    clearSearchCache: explorerSync.clearSearchCache.bind(explorerSync),\n    clearCaches: explorerSync.clearCaches.bind(explorerSync),\n  } as const;\n}\n\n// do not allow mutation of default loaders. Make sure it is set inside options\nconst defaultLoaders = Object.freeze({\n  '.js': loaders.loadJs,\n  '.json': loaders.loadJson,\n  '.yaml': loaders.loadYaml,\n  '.yml': loaders.loadYaml,\n  noExt: loaders.loadYaml,\n} as const);\n\nfunction normalizeOptions(\n  moduleName: string,\n  options: OptionsSync,\n): ExplorerOptionsSync;\nfunction normalizeOptions(\n  moduleName: string,\n  options: Options,\n): ExplorerOptions;\nfunction normalizeOptions(\n  moduleName: string,\n  options: Options | OptionsSync,\n): ExplorerOptions | ExplorerOptionsSync {\n  const defaults: ExplorerOptions | ExplorerOptionsSync = {\n    packageProp: moduleName,\n    searchPlaces: [\n      'package.json',\n      `.${moduleName}rc`,\n      `.${moduleName}rc.json`,\n      `.${moduleName}rc.yaml`,\n      `.${moduleName}rc.yml`,\n      `.${moduleName}rc.js`,\n      `${moduleName}.config.js`,\n    ],\n    ignoreEmptySearchPlaces: true,\n    stopDir: os.homedir(),\n    cache: true,\n    transform: identity,\n    loaders: defaultLoaders,\n  };\n\n  const normalizedOptions: ExplorerOptions | ExplorerOptionsSync = {\n    ...defaults,\n    ...options,\n    loaders: {\n      ...defaults.loaders,\n      ...options.loaders,\n    },\n  };\n\n  return normalizedOptions;\n}\n\nconst identity: TransformSync = function identity(x) {\n  return x;\n};\n\nexport { cosmiconfig, cosmiconfigSync, defaultLoaders };\n"], "file": "index.js"}