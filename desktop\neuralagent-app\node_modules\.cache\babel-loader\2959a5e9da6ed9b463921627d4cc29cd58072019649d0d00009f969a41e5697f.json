{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\Elements\\\\YesNoDialog.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport Dialog from './Dialog';\nimport { Text } from './Typography';\nimport { Button } from './Button';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction YesNoDialog({\n  isOpen,\n  setOpen,\n  title,\n  text,\n  isDarkMode = false,\n  onYesClicked\n}) {\n  _s();\n  const isRTL = useSelector(state => state.isRTL);\n  const yesClicked = () => {\n    setOpen(false);\n    if (onYesClicked !== null) {\n      onYesClicked();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      isOpen: isOpen,\n      setOpen: setOpen,\n      maxWidth: \"500px\",\n      title: title,\n      padding: \"10px 20px\",\n      isDarkMode: isDarkMode,\n      child: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          color: isDarkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.8)',\n          children: text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            display: 'flex',\n            marginLeft: isRTL ? '0' : 'auto',\n            marginRight: isRTL ? 'auto' : '0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"transparent\",\n            padding: \"10px 15px\",\n            onClick: () => setOpen(false),\n            dark: isDarkMode,\n            children: \"No\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"transparent\",\n            padding: \"10px 15px\",\n            onClick: yesClicked,\n            dark: isDarkMode,\n            children: \"Yes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(YesNoDialog, \"4be5AVVFo8sXi1J7oAeipnnv2Bc=\", false, function () {\n  return [useSelector];\n});\n_c = YesNoDialog;\nexport default YesNoDialog;\nvar _c;\n$RefreshReg$(_c, \"YesNoDialog\");", "map": {"version": 3, "names": ["React", "Dialog", "Text", "<PERSON><PERSON>", "useSelector", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "YesNoDialog", "isOpen", "<PERSON><PERSON><PERSON>", "title", "text", "isDarkMode", "onYesClicked", "_s", "isRTL", "state", "yesClicked", "children", "max<PERSON><PERSON><PERSON>", "padding", "child", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginTop", "display", "marginLeft", "marginRight", "onClick", "dark", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/YesNoDialog.js"], "sourcesContent": ["import React from 'react';\nimport Dialog from './Dialog';\nimport { Text } from './Typography';\nimport { Button } from './Button';\nimport { useSelector } from 'react-redux';\n\nfunction YesNoDialog({ isOpen, setOpen, title, text, isDarkMode=false, onYesClicked }) {\n\n  const isRTL = useSelector(state => state.isRTL);\n\n  const yesClicked = () => {\n    setOpen(false);\n    if (onYesClicked !== null) {\n      onYesClicked();\n    }\n  };\n\n  return (\n    <>\n      <Dialog\n        isOpen={isOpen}\n        setOpen={setOpen}\n        maxWidth='500px'\n        title={title}\n        padding=\"10px 20px\"\n        isDarkMode={isDarkMode}\n        child={\n          <>\n            <Text color={isDarkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.8)'}>\n              {text}\n            </Text>\n            <div style={{marginTop: '10px', display: 'flex', marginLeft: isRTL ? '0' : 'auto', marginRight: isRTL ? 'auto' : '0'}}>\n              <Button color=\"transparent\" padding=\"10px 15px\" onClick={() => setOpen(false)} dark={isDarkMode}>\n                No\n              </Button>\n              <Button color=\"transparent\" padding=\"10px 15px\" onClick={yesClicked} dark={isDarkMode}>\n                Yes\n              </Button>\n            </div>\n          </>\n        }\n      />\n    </>\n  );\n}\n\nexport default YesNoDialog;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,SAASC,WAAWA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,KAAK;EAAEC,IAAI;EAAEC,UAAU,GAAC,KAAK;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAErF,MAAMC,KAAK,GAAGb,WAAW,CAACc,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAE/C,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBR,OAAO,CAAC,KAAK,CAAC;IACd,IAAII,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,oBACET,OAAA,CAAAE,SAAA;IAAAY,QAAA,eACEd,OAAA,CAACL,MAAM;MACLS,MAAM,EAAEA,MAAO;MACfC,OAAO,EAAEA,OAAQ;MACjBU,QAAQ,EAAC,OAAO;MAChBT,KAAK,EAAEA,KAAM;MACbU,OAAO,EAAC,WAAW;MACnBR,UAAU,EAAEA,UAAW;MACvBS,KAAK,eACHjB,OAAA,CAAAE,SAAA;QAAAY,QAAA,gBACEd,OAAA,CAACJ,IAAI;UAACsB,KAAK,EAAEV,UAAU,GAAG,0BAA0B,GAAG,oBAAqB;UAAAM,QAAA,EACzEP;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACPtB,OAAA;UAAKuB,KAAK,EAAE;YAACC,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAEf,KAAK,GAAG,GAAG,GAAG,MAAM;YAAEgB,WAAW,EAAEhB,KAAK,GAAG,MAAM,GAAG;UAAG,CAAE;UAAAG,QAAA,gBACpHd,OAAA,CAACH,MAAM;YAACqB,KAAK,EAAC,aAAa;YAACF,OAAO,EAAC,WAAW;YAACY,OAAO,EAAEA,CAAA,KAAMvB,OAAO,CAAC,KAAK,CAAE;YAACwB,IAAI,EAAErB,UAAW;YAAAM,QAAA,EAAC;UAEjG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtB,OAAA,CAACH,MAAM;YAACqB,KAAK,EAAC,aAAa;YAACF,OAAO,EAAC,WAAW;YAACY,OAAO,EAAEf,UAAW;YAACgB,IAAI,EAAErB,UAAW;YAAAM,QAAA,EAAC;UAEvF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACN;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC,gBACF,CAAC;AAEP;AAACZ,EAAA,CAtCQP,WAAW;EAAA,QAEJL,WAAW;AAAA;AAAAgC,EAAA,GAFlB3B,WAAW;AAwCpB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}