# 🚀 NeuralAgent 完整部署教程

> 本教程适合完全没有经验的小白用户，将手把手教你从零开始部署 NeuralAgent AI 桌面助手

## 📋 目录
1. [环境准备](#环境准备)
2. [安装必要软件](#安装必要软件)
3. [安装 Ollama 本地AI模型](#安装-ollama-本地ai模型)
4. [部署 NeuralAgent 项目](#部署-neuralagent-项目)
5. [配置和启动](#配置和启动)
6. [测试验证](#测试验证)
7. [常见问题](#常见问题)

---

## 🛠️ 环境准备

### 系统要求
- **操作系统**: Windows 10/11 (推荐)
- **内存**: 至少 8GB (推荐 16GB+)
- **磁盘空间**: 至少 50GB 可用空间
- **显卡**: NVIDIA 显卡优先 (可选，但会大幅提升性能)

---

## 📥 安装必要软件

### 1. 安装 Python 3.9+

#### 步骤 1.1: 下载 Python
1. 打开浏览器，访问: https://www.python.org/downloads/
2. 点击 "Download Python 3.11.x" (选择最新的 3.9+ 版本)
3. 下载完成后，双击安装包

#### 步骤 1.2: 安装 Python
1. **重要**: 勾选 "Add Python to PATH" (将 Python 添加到系统路径)
2. 选择 "Install Now" (立即安装)
3. 等待安装完成

#### 步骤 1.3: 验证安装
1. 按 `Win + R` 打开运行对话框
2. 输入 `cmd` 并按回车，打开命令提示符
3. 输入以下命令验证:
```bash
python --version
# 应该显示: Python 3.11.x 或类似版本号

pip --version
# 应该显示: pip 23.x.x 或类似版本号
```

### 2. 安装 Node.js

#### 步骤 2.1: 下载 Node.js
1. 访问: https://nodejs.org/
2. 点击 "LTS" 版本下载 (推荐长期支持版本)
3. 下载完成后，双击安装包

#### 步骤 2.2: 安装 Node.js
1. 按照安装向导默认设置安装
2. 确保勾选 "Add to PATH" 选项

#### 步骤 2.3: 验证安装
在命令提示符中输入:
```bash
node --version
# 应该显示: v18.x.x 或更高版本

npm --version
# 应该显示: 9.x.x 或更高版本
```

### 3. 安装 PostgreSQL 数据库

#### 步骤 3.1: 下载 PostgreSQL
1. 访问: https://www.postgresql.org/download/windows/
2. 点击 "Download the installer"
3. 选择最新版本 (推荐 15.x)

#### 步骤 3.2: 安装 PostgreSQL
1. 双击安装包，按照向导安装
2. **重要**: 记住你设置的数据库密码 (例如: `123456`)
3. 端口保持默认 `5432`
4. 安装完成后，记住以下信息:
   - 用户名: `postgres`
   - 密码: `你设置的密码`
   - 端口: `5432`
   - 主机: `localhost`

#### 步骤 3.3: 创建项目数据库
1. 打开 "pgAdmin" (PostgreSQL 管理工具)
2. 连接到本地服务器，输入你设置的密码
3. 右键点击 "Databases" → "Create" → "Database"
4. 数据库名称输入: `neuralagent`
5. 点击 "Save" 创建数据库

---

## 🤖 安装 Ollama 本地AI模型

### 步骤 4.1: 下载 Ollama
1. 访问: https://ollama.ai/
2. 点击 "Download for Windows"
3. 下载完成后，双击安装包安装

### 步骤 4.2: 验证 Ollama 安装
打开命令提示符，输入:
```bash
ollama --version
# 应该显示版本信息
```

### 步骤 4.3: 下载推荐的AI模型
根据你的电脑配置，选择合适的模型:

#### 对于 8GB+ 内存 + NVIDIA 显卡:
```bash
# 下载中文优化的 3B 模型 (推荐)
ollama pull qwen2.5:3b
# 解释: qwen2.5 是阿里巴巴开发的中文友好模型，3b 表示30亿参数

# 下载通用 3B 模型 (备选)
ollama pull llama3.1:3b
# 解释: Meta 开发的通用模型，英文表现优秀
```

#### 对于内存较少的电脑:
```bash
# 下载超轻量模型
ollama pull tinyllama:1.1b
# 解释: 最小的可用模型，适合低配置电脑
```

### 步骤 4.4: 测试模型
```bash
# 测试模型是否正常工作
ollama run qwen2.5:3b
# 解释: 启动模型进行对话测试

# 在出现提示符后，输入: 你好，请介绍一下自己
# 如果模型正常回复，说明安装成功
# 输入 /bye 退出对话
```

---

## 📦 部署 NeuralAgent 项目

### 步骤 5.1: 下载项目代码
1. 如果你已经有项目文件夹，跳过此步骤
2. 如果没有，可以从 GitHub 下载:
   - 访问: https://github.com/withneural/neuralagent
   - 点击绿色的 "Code" 按钮
   - 选择 "Download ZIP"
   - 解压到你想要的位置 (例如: `D:\neuralagent`)

### 步骤 5.2: 打开项目文件夹
1. 按 `Win + R`，输入 `cmd` 打开命令提示符
2. 使用 `cd` 命令进入项目目录:
```bash
# 替换为你的实际路径
cd D:\neuralagent
# 解释: cd 是 change directory 的缩写，用于切换目录

# 查看当前目录内容
dir
# 解释: 显示当前目录下的文件和文件夹
```

---

## ⚙️ 配置和启动

### 步骤 6.1: 配置后端 (Backend)

#### 6.1.1: 进入后端目录
```bash
cd backend
# 解释: 进入后端代码目录
```

#### 6.1.2: 创建 Python 虚拟环境
```bash
python -m venv venv
# 解释: 创建一个名为 venv 的虚拟环境，用于隔离项目依赖

# 激活虚拟环境 (Windows)
venv\Scripts\activate
# 解释: 激活虚拟环境，之后安装的包只会在这个环境中

# 你会看到命令提示符前面出现 (venv) 标识
```

#### 6.1.3: 安装后端依赖
```bash
pip install -r requirements.txt
# 解释: 安装 requirements.txt 文件中列出的所有 Python 包
# 这可能需要几分钟时间
```

#### 6.1.4: 配置环境变量
```bash
# 复制配置文件模板
copy .env.example .env
# 解释: 复制环境变量模板文件，我们需要修改其中的配置
```

现在用记事本打开 `.env` 文件，修改以下配置:
```env
# 数据库配置 (根据你的 PostgreSQL 设置修改)
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=neuralagent
DB_USERNAME=postgres
DB_PASSWORD=你的数据库密码

# JWT 配置
JWT_ISS=NeuralAgentBackend
JWT_SECRET=your_random_secret_key_here_123456

# Ollama 配置
OLLAMA_URL=http://127.0.0.1:11434

# 模型配置 (使用 Ollama)
CLASSIFIER_AGENT_MODEL_TYPE=ollama
CLASSIFIER_AGENT_MODEL_ID=qwen2.5:3b

TITLE_AGENT_MODEL_TYPE=ollama
TITLE_AGENT_MODEL_ID=qwen2.5:3b

SUGGESTOR_AGENT_MODEL_TYPE=ollama
SUGGESTOR_AGENT_MODEL_ID=qwen2.5:3b

PLANNER_AGENT_MODEL_TYPE=ollama
PLANNER_AGENT_MODEL_ID=qwen2.5:3b

COMPUTER_USE_AGENT_MODEL_TYPE=ollama
COMPUTER_USE_AGENT_MODEL_ID=qwen2.5:3b

SUMMARIZER_AGENT_MODEL_TYPE=ollama
SUMMARIZER_AGENT_MODEL_ID=qwen2.5:3b

# 其他配置保持默认
ENABLE_SCREENSHOT_LOGGING_FOR_TRAINING=false
LANGCHAIN_TRACING_V2=false
```

#### 6.1.5: 初始化数据库
```bash
# 运行数据库迁移
alembic upgrade head
# 解释: 创建数据库表结构
```

#### 6.1.6: 启动后端服务
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
# 解释: 启动 FastAPI 后端服务
# --reload: 代码改变时自动重启
# --host 0.0.0.0: 允许所有IP访问
# --port 8000: 使用8000端口

# 看到 "Uvicorn running on http://0.0.0.0:8000" 表示启动成功
# 保持这个窗口打开，不要关闭
```

### 步骤 6.2: 配置前端 (Frontend)

#### 6.2.1: 打开新的命令提示符窗口
1. 按 `Win + R`，输入 `cmd` 打开新的命令提示符
2. 进入项目目录:
```bash
cd D:\neuralagent\desktop
# 解释: 进入前端代码目录
```

#### 6.2.2: 安装 Electron 依赖
```bash
npm install
# 解释: 安装 package.json 中定义的 Node.js 依赖包
# 这可能需要几分钟时间
```

#### 6.2.3: 配置 React 应用
```bash
cd neuralagent-app
# 解释: 进入 React 应用目录

npm install
# 解释: 安装 React 应用的依赖

# 复制配置文件
copy .env.example .env
# 解释: 复制前端配置文件模板
```

用记事本打开 `neuralagent-app\.env` 文件，确保内容如下:
```env
REACT_APP_PROTOCOL=http
REACT_APP_WEBSOCKET_PROTOCOL=ws
REACT_APP_DNS=127.0.0.1:8000
REACT_APP_API_KEY=
```

#### 6.2.4: 配置 AI 代理服务
```bash
cd ..\aiagent
# 解释: 返回上级目录，然后进入 aiagent 目录

python -m venv venv
# 解释: 为 AI 代理创建独立的虚拟环境

venv\Scripts\activate
# 解释: 激活 AI 代理的虚拟环境

pip install -r requirements.txt
# 解释: 安装 AI 代理所需的依赖包

deactivate
# 解释: 退出虚拟环境
```

#### 6.2.5: 启动前端应用
```bash
cd ..
# 解释: 返回到 desktop 目录

npm start
# 解释: 启动 Electron 桌面应用
# 这会自动打开 NeuralAgent 的桌面应用窗口
```

---

## ✅ 测试验证

### 步骤 7.1: 检查服务状态
1. **后端服务**: 访问 http://localhost:8000/docs 应该能看到 API 文档
2. **Ollama 服务**: 在命令提示符中运行 `ollama list` 应该能看到已安装的模型
3. **前端应用**: 应该能看到 NeuralAgent 的桌面应用界面

### 步骤 7.2: 测试 AI 功能
1. 在 NeuralAgent 应用中输入简单指令，例如: "帮我打开记事本"
2. 观察 AI 是否能正确理解并执行任务

---

## 🔧 常见问题

### Q1: Python 命令不被识别
**解决方案**: 重新安装 Python，确保勾选 "Add Python to PATH"

### Q2: 数据库连接失败
**解决方案**: 
1. 确保 PostgreSQL 服务正在运行
2. 检查 `.env` 文件中的数据库配置是否正确
3. 确保数据库 `neuralagent` 已创建

### Q3: Ollama 模型下载失败
**解决方案**:
1. 检查网络连接
2. 尝试使用代理或更换网络
3. 可以尝试下载更小的模型: `ollama pull tinyllama:1.1b`

### Q4: 前端应用无法启动
**解决方案**:
1. 确保后端服务 (端口8000) 正在运行
2. 检查 Node.js 和 npm 是否正确安装
3. 删除 `node_modules` 文件夹，重新运行 `npm install`

### Q5: AI 响应很慢
**解决方案**:
1. 如果有 NVIDIA 显卡，确保显卡驱动是最新的
2. 尝试使用更小的模型
3. 关闭其他占用内存的程序

---

## 🎉 恭喜！

如果你完成了以上所有步骤，你的 NeuralAgent AI 桌面助手应该已经成功运行了！

现在你可以开始体验 AI 自动化操作你的电脑了。记住要负责任地使用这个工具！

---

## 📞 获取帮助

如果遇到问题，可以:
1. 查看项目的 GitHub Issues: https://github.com/withneural/neuralagent/issues
2. 加入官方 Discord: https://discord.gg/eGyW3kPcUs
3. 访问官网: https://www.getneuralagent.com
