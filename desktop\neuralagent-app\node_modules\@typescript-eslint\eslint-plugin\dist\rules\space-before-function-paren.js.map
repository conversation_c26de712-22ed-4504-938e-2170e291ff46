{"version": 3, "file": "space-before-function-paren.js", "sourceRoot": "", "sources": ["../../src/rules/space-before-function-paren.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAehC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,wDAAwD;YACrE,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,SAAS,EAAE;gCACT,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;6BACpC;4BACD,KAAK,EAAE;gCACL,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;6BACpC;4BACD,UAAU,EAAE;gCACV,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;6BACpC;yBACF;wBACD,oBAAoB,EAAE,KAAK;qBAC5B;iBACF;aACF;SACF;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,+CAA+C;YAC3D,OAAO,EAAE,4CAA4C;SACtD;KACF;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAE1B,MAAM,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC;QAC3B,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC5E,MAAM,cAAc,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;QAE1E;;;;WAIG;QACH,SAAS,eAAe,CACtB,IAK8B;YAE9B,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE;gBACnB,OAAO,IAAI,CAAC;aACb;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC;YAE5B,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;gBACzD,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;oBACtC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CACrE,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,oBAAoB,CAC3B,IAK8B;;YAE9B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE;gBACxD,8FAA8F;gBAC9F,IACE,IAAI,CAAC,KAAK;oBACV,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,CAAE,CAAC,EACtE;oBACA,OAAO,MAAA,cAAc,CAAC,UAAU,mCAAI,UAAU,CAAC;iBAChD;aACF;iBAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE;gBAChC,OAAO,MAAA,cAAc,CAAC,KAAK,mCAAI,UAAU,CAAC;gBAE1C,oFAAoF;aACrF;iBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBAC1B,OAAO,MAAA,cAAc,CAAC,SAAS,mCAAI,UAAU,CAAC;aAC/C;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED;;;;WAIG;QACH,SAAS,aAAa,CACpB,IAK8B;YAE9B,MAAM,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAElD,IAAI,cAAc,KAAK,QAAQ,EAAE;gBAC/B,OAAO;aACR;YAED,IAAI,SAAyB,CAAC;YAC9B,IAAI,UAA0B,CAAC;YAC/B,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAE,CAAC;gBAC1D,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC;aACnD;iBAAM;gBACL,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAE,CAAC;gBACvE,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC;aACpD;YACD,yGAAyG;YACzG,MAAM,UAAU,GAAG,UAAU,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAE1E,IAAI,UAAU,IAAI,cAAc,KAAK,OAAO,EAAE;gBAC5C,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE;wBACH,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG;wBACxB,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK;qBAC1B;oBACD,SAAS,EAAE,YAAY;oBACvB,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC/D,CAAC,CAAC;aACJ;iBAAM,IACL,CAAC,UAAU;gBACX,cAAc,KAAK,QAAQ;gBAC3B,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,EAAE,CAAC,EACjC;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE,UAAU,CAAC,GAAG;oBACnB,SAAS,EAAE,SAAS;oBACpB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC;iBACpD,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,uBAAuB,EAAE,aAAa;YACtC,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,6BAA6B,EAAE,aAAa;YAC5C,iBAAiB,EAAE,aAAa;SACjC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}