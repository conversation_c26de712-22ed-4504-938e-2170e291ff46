{"ast": null, "code": "import styled from 'styled-components';\nimport breakpoint from '../../../utils/breakpoint';\nexport const LabeledTAContainer = styled.div`\n  display: flex;\n\n  @media screen and (${breakpoint.devices_max.xs}) {\n    flex-direction: column;\n  }\n`;\nexport const VerticalLabeledTAContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\nexport const TextAreaLabel = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '500'};\n  color: ${props => props.isDarkMode ? '#fff' : '#000'};\n  padding-right: 15px;\n  flex: 1 1 25%;\n  margin-bottom: ${props => props.verticalLabel ? '7px' : '0px'};\n\n  @media screen and (${breakpoint.devices_max.xs}) {\n    padding-right: 0px;\n    margin-bottom: 7px;\n  }\n`;\nexport const TextArea = styled.textarea`\n  background: ${props => props.background ? props.background : '#fff'};\n  padding: ${props => props.padding ? props.padding : '10px 8px'};\n  color: ${props => props.isDarkMode ? '#fff' : '#000'};\n  width: 100%;\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  border-radius: ${props => props.borderRadius ? props.borderRadius : '7px'};\n  font-family: inherit;\n  resize: none;\n  outline: ${props => props.outlined ? props.isDarkMode ? 'rgba(255, 255, 255, 0.9) solid 1px' : 'rgba(0, 0, 0, 0.9) solid 1px' : 'none'};\n  border: none;\n\n  &::placeholder {\n    color: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'};\n    font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n    font-weight: 300;\n  }\n\n  &:focus {\n    outline: ${props => props.outlined ? 'var(--primary-color) solid 2px' : 'none'};\n  }\n`;\nexport const TextAreaError = styled.div`\n  margin-top: 2px;\n  color: var(--danger-color);\n  font-size: 16px;\n`;", "map": {"version": 3, "names": ["styled", "breakpoint", "LabeledTAContainer", "div", "devices_max", "xs", "VerticalLabeledTAContainer", "TextAreaLabel", "props", "fontSize", "fontWeight", "isDarkMode", "verticalLabel", "TextArea", "textarea", "background", "padding", "borderRadius", "outlined", "TextAreaError"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/TextAreas/Elements.js"], "sourcesContent": ["import styled from 'styled-components';\nimport breakpoint from '../../../utils/breakpoint';\n\nexport const LabeledTAContainer = styled.div`\n  display: flex;\n\n  @media screen and (${breakpoint.devices_max.xs}) {\n    flex-direction: column;\n  }\n`\n\nexport const VerticalLabeledTAContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`\n\nexport const TextAreaLabel = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '500'};\n  color: ${props => props.isDarkMode ? '#fff' : '#000'};\n  padding-right: 15px;\n  flex: 1 1 25%;\n  margin-bottom: ${props => props.verticalLabel ? '7px' : '0px'};\n\n  @media screen and (${breakpoint.devices_max.xs}) {\n    padding-right: 0px;\n    margin-bottom: 7px;\n  }\n`\n\nexport const TextArea = styled.textarea`\n  background: ${props => props.background ? props.background : '#fff'};\n  padding: ${props => props.padding ? props.padding : '10px 8px'};\n  color: ${props => props.isDarkMode ? '#fff' : '#000'};\n  width: 100%;\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  border-radius: ${props => props.borderRadius ? props.borderRadius : '7px'};\n  font-family: inherit;\n  resize: none;\n  outline: ${props => props.outlined ? props.isDarkMode ? 'rgba(255, 255, 255, 0.9) solid 1px' : 'rgba(0, 0, 0, 0.9) solid 1px' : 'none'};\n  border: none;\n\n  &::placeholder {\n    color: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'};\n    font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n    font-weight: 300;\n  }\n\n  &:focus {\n    outline: ${props => props.outlined ? 'var(--primary-color) solid 2px' : 'none'};\n  }\n`\n\nexport const TextAreaError = styled.div`\n  margin-top: 2px;\n  color: var(--danger-color);\n  font-size: 16px;\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,OAAOC,UAAU,MAAM,2BAA2B;AAElD,OAAO,MAAMC,kBAAkB,GAAGF,MAAM,CAACG,GAAG;AAC5C;AACA;AACA,uBAAuBF,UAAU,CAACG,WAAW,CAACC,EAAE;AAChD;AACA;AACA,CAAC;AAED,OAAO,MAAMC,0BAA0B,GAAGN,MAAM,CAACG,GAAG;AACpD;AACA;AACA,CAAC;AAED,OAAO,MAAMI,aAAa,GAAGP,MAAM,CAACG,GAAG;AACvC,eAAeK,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,GAAG,MAAM;AAChE,iBAAiBD,KAAK,IAAIA,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU,GAAG,KAAK;AACrE,WAAWF,KAAK,IAAIA,KAAK,CAACG,UAAU,GAAG,MAAM,GAAG,MAAM;AACtD;AACA;AACA,mBAAmBH,KAAK,IAAIA,KAAK,CAACI,aAAa,GAAG,KAAK,GAAG,KAAK;AAC/D;AACA,uBAAuBX,UAAU,CAACG,WAAW,CAACC,EAAE;AAChD;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMQ,QAAQ,GAAGb,MAAM,CAACc,QAAQ;AACvC,gBAAgBN,KAAK,IAAIA,KAAK,CAACO,UAAU,GAAGP,KAAK,CAACO,UAAU,GAAG,MAAM;AACrE,aAAaP,KAAK,IAAIA,KAAK,CAACQ,OAAO,GAAGR,KAAK,CAACQ,OAAO,GAAG,UAAU;AAChE,WAAWR,KAAK,IAAIA,KAAK,CAACG,UAAU,GAAG,MAAM,GAAG,MAAM;AACtD;AACA,eAAeH,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,GAAG,MAAM;AAChE,mBAAmBD,KAAK,IAAIA,KAAK,CAACS,YAAY,GAAGT,KAAK,CAACS,YAAY,GAAG,KAAK;AAC3E;AACA;AACA,aAAaT,KAAK,IAAIA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACG,UAAU,GAAG,oCAAoC,GAAG,8BAA8B,GAAG,MAAM;AACxI;AACA;AACA;AACA,aAAaH,KAAK,IAAIA,KAAK,CAACG,UAAU,GAAG,0BAA0B,GAAG,oBAAoB;AAC1F,iBAAiBH,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,GAAG,MAAM;AAClE;AACA;AACA;AACA;AACA,eAAeD,KAAK,IAAIA,KAAK,CAACU,QAAQ,GAAG,gCAAgC,GAAG,MAAM;AAClF;AACA,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGnB,MAAM,CAACG,GAAG;AACvC;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}