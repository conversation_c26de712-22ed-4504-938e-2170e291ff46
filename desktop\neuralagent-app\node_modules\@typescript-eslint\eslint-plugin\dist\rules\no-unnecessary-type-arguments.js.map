{"version": 3, "file": "no-unnecessary-type-arguments.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-arguments.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAChC,kCAA0C;AAe1C,kBAAe,IAAI,CAAC,UAAU,CAAiB;IAC7C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,uDAAuD;YACpE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,wBAAwB,EACtB,0EAA0E;SAC7E;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,SAAS,oBAAoB,CAAC,IAAa;YAIzC,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;gBAClC,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,MAAM;oBACjB,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;iBACpD,CAAC;aACH;YACD,OAAO;gBACL,IAAI;gBACJ,aAAa,EAAE,EAAE;aAClB,CAAC;QACJ,CAAC;QAED,SAAS,wBAAwB,CAC/B,YAAmD,EACnD,cAAsD;YAEtD,+FAA+F;YAC/F,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACzC,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAA,EAAE;gBACnB,OAAO;aACR;YAED,oGAAoG;YACpG,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACrD,uFAAuF;YACvF,IAAI,WAAW,KAAK,OAAO,EAAE;gBAC3B,2FAA2F;gBAC3F,0FAA0F;gBAC1F,gDAAgD;gBAChD,iFAAiF;gBACjF,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;gBAC9D,MAAM,eAAe,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBACtD;gBACE,+DAA+D;gBAC/D,mBAAmB,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI;oBACjD,mBAAmB,CAAC,aAAa,CAAC,MAAM;wBACtC,eAAe,CAAC,aAAa,CAAC,MAAM;oBACtC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CACpC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CACjD,EACD;oBACA,OAAO;iBACR;aACF;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,GAAG;gBACT,SAAS,EAAE,0BAA0B;gBACrC,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,WAAW,CACf,CAAC,KAAK,CAAC;oBACL,CAAC,CAAC,YAAY,CAAC,KAAK;oBACpB,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACxD;aACJ,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,4BAA4B,CAAC,IAAI;gBAC/B,MAAM,UAAU,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAElE,MAAM,cAAc,GAAG,yBAAyB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACtE,IAAI,cAAc,EAAE;oBAClB,wBAAwB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;iBAChD;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,yBAAyB,CAChC,IAA4B,EAC5B,OAAuB;IAEvB,IAAI,EAAE,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;QAC1C,OAAO,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KAC5D;IAED,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KAC1D;IAED,IAAI,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;QACzD,OAAO,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACjD;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB,CAChC,IAAyD,EACzD,OAAuB;IAEvB,MAAM,aAAa,GAAG,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACxD,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,GAAG,GAAG,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACrD,MAAM,YAAY,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IAE3C,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,IAAA,sBAAe,EAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAC1C,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;QACpB,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC;QAC/B,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC;QAC7B,CAAC,CAAC,IAAI,CAAC,cAAc;QACrB,CAAC,CAAC,SAAS,CACd,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAChC,IAA0C,EAC1C,OAAuB;IAEvB,MAAM,GAAG,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,cAAc,EAAE,CAAC;IACtC,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAC7B,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;KACf;IAED,OAAO,OAAO,CAAC,cAAc,CAAC;AAChC,CAAC;AAED,SAAS,gBAAgB,CACvB,MAAiB,EACjB,OAAuB;IAEvB,OAAO,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;QAC1D,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAClC,CAAC,CAAC,MAAM,CAAC;AACb,CAAC"}