{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n/*#__PURE__*/\n// <React.StrictMode>\n_jsxDEV(Provider, {\n  store: store,\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 5\n}, this)\n// </React.StrictMode>\n);", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "Provider", "store", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  // <React.StrictMode>\n    <Provider store={store}>\n      <App />\n    </Provider>\n  // </React.StrictMode>\n);\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,IAAI,GAAGN,QAAQ,CAACO,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM;AAAA;AACT;AACEL,OAAA,CAACH,QAAQ;EAACC,KAAK,EAAEA,KAAM;EAAAQ,QAAA,eACrBN,OAAA,CAACJ,GAAG;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACC;AACZ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}