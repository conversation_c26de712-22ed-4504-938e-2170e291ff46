{"generatedAt": 1719250170266, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@types/jest": "29.5.12", "eslint-plugin-jest-dom": "5.4.0", "eslint-plugin-testing-library": "6.2.2", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "ts-jest": "29.1.5"}, "packageManager": {"type": "yarn", "version": "4.3.1"}, "typescriptOptions": {"reactDocgen": "react-docgen-typescript"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "8.1.10", "storybookVersionSpecifier": "^8.1.10", "language": "typescript", "storybookPackages": {"@storybook/addon-actions": {"version": "8.1.10"}, "@storybook/addon-docs": {"version": "8.1.10"}, "@storybook/addons": {"version": "7.6.17"}, "@storybook/react": {"version": "8.1.10"}, "@storybook/react-vite": {"version": "8.1.10"}, "eslint-plugin-storybook": {"version": "0.8.0"}, "storybook": {"version": "8.1.10"}}, "addons": {"@storybook/addon-links": {"version": "8.1.10"}, "@storybook/addon-essentials": {"version": "8.1.10"}, "@storybook/addon-interactions": {"version": "8.1.10"}, "storybook-dark-mode": {"version": "4.0.2"}, "./google-analytics-v4": {"version": null}, "./canonical-link": {"version": null}, "@storybook/addon-webpack5-compiler-swc": {"version": "1.0.4"}}}