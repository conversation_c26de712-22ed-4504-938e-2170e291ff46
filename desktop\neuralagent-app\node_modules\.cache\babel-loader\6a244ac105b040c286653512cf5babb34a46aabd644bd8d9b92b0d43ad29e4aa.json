{"ast": null, "code": "\"use strict\";\n\nvar __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n    desc = {\n      enumerable: true,\n      get: function () {\n        return m[k];\n      }\n    };\n  }\n  Object.defineProperty(o, k2, desc);\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\nvar __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n  Object.defineProperty(o, \"default\", {\n    enumerable: true,\n    value: v\n  });\n} : function (o, v) {\n  o[\"default\"] = v;\n});\nvar __importStar = this && this.__importStar || function (mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar React = __importStar(require(\"react\"));\nvar unitConverter_1 = require(\"./helpers/unitConverter\");\nvar animation_1 = require(\"./helpers/animation\");\nvar clip = (0, animation_1.createAnimation)(\"ClipLoader\", \"0% {transform: rotate(0deg) scale(1)} 50% {transform: rotate(180deg) scale(0.8)} 100% {transform: rotate(360deg) scale(1)}\", \"clip\");\nfunction ClipLoader(_a) {\n  var _b = _a.loading,\n    loading = _b === void 0 ? true : _b,\n    _c = _a.color,\n    color = _c === void 0 ? \"#000000\" : _c,\n    _d = _a.speedMultiplier,\n    speedMultiplier = _d === void 0 ? 1 : _d,\n    _e = _a.cssOverride,\n    cssOverride = _e === void 0 ? {} : _e,\n    _f = _a.size,\n    size = _f === void 0 ? 35 : _f,\n    additionalprops = __rest(_a, [\"loading\", \"color\", \"speedMultiplier\", \"cssOverride\", \"size\"]);\n  var style = __assign({\n    background: \"transparent !important\",\n    width: (0, unitConverter_1.cssValue)(size),\n    height: (0, unitConverter_1.cssValue)(size),\n    borderRadius: \"100%\",\n    border: \"2px solid\",\n    borderTopColor: color,\n    borderBottomColor: \"transparent\",\n    borderLeftColor: color,\n    borderRightColor: color,\n    display: \"inline-block\",\n    animation: \"\".concat(clip, \" \").concat(0.75 / speedMultiplier, \"s 0s infinite linear\"),\n    animationFillMode: \"both\"\n  }, cssOverride);\n  if (!loading) {\n    return null;\n  }\n  return React.createElement(\"span\", __assign({\n    style: style\n  }, additionalprops));\n}\nexports.default = ClipLoader;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__createBinding", "create", "o", "m", "k", "k2", "undefined", "desc", "getOwnPropertyDescriptor", "__esModule", "writable", "configurable", "enumerable", "get", "defineProperty", "__setModuleDefault", "v", "value", "__importStar", "mod", "result", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "exports", "React", "require", "unitConverter_1", "animation_1", "clip", "createAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "_b", "loading", "_c", "color", "_d", "speedMultiplier", "_e", "cssOverride", "_f", "size", "additionalprops", "style", "background", "width", "cssValue", "height", "borderRadius", "border", "borderTopColor", "borderBottomColor", "borderLeftColor", "borderRightColor", "display", "animation", "concat", "animationFillMode", "createElement", "default"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/react-spinners/ClipLoader.js"], "sourcesContent": ["\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar React = __importStar(require(\"react\"));\nvar unitConverter_1 = require(\"./helpers/unitConverter\");\nvar animation_1 = require(\"./helpers/animation\");\nvar clip = (0, animation_1.createAnimation)(\"ClipLoader\", \"0% {transform: rotate(0deg) scale(1)} 50% {transform: rotate(180deg) scale(0.8)} 100% {transform: rotate(360deg) scale(1)}\", \"clip\");\nfunction ClipLoader(_a) {\n    var _b = _a.loading, loading = _b === void 0 ? true : _b, _c = _a.color, color = _c === void 0 ? \"#000000\" : _c, _d = _a.speedMultiplier, speedMultiplier = _d === void 0 ? 1 : _d, _e = _a.cssOverride, cssOverride = _e === void 0 ? {} : _e, _f = _a.size, size = _f === void 0 ? 35 : _f, additionalprops = __rest(_a, [\"loading\", \"color\", \"speedMultiplier\", \"cssOverride\", \"size\"]);\n    var style = __assign({ background: \"transparent !important\", width: (0, unitConverter_1.cssValue)(size), height: (0, unitConverter_1.cssValue)(size), borderRadius: \"100%\", border: \"2px solid\", borderTopColor: color, borderBottomColor: \"transparent\", borderLeftColor: color, borderRightColor: color, display: \"inline-block\", animation: \"\".concat(clip, \" \").concat(0.75 / speedMultiplier, \"s 0s infinite linear\"), animationFillMode: \"both\" }, cssOverride);\n    if (!loading) {\n        return null;\n    }\n    return React.createElement(\"span\", __assign({ style: style }, additionalprops));\n}\nexports.default = ClipLoader;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAASC,CAAC,EAAE;IACpC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAC3DN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IACnB;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AACD,IAAIO,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,KAAMb,MAAM,CAACc,MAAM,GAAI,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EAC5F,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5B,IAAIG,IAAI,GAAGpB,MAAM,CAACqB,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC;EAChD,IAAI,CAACG,IAAI,KAAK,KAAK,IAAIA,IAAI,GAAG,CAACJ,CAAC,CAACM,UAAU,GAAGF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACI,YAAY,CAAC,EAAE;IACjFJ,IAAI,GAAG;MAAEK,UAAU,EAAE,IAAI;MAAEC,GAAG,EAAE,SAAAA,CAAA,EAAW;QAAE,OAAOV,CAAC,CAACC,CAAC,CAAC;MAAE;IAAE,CAAC;EAC/D;EACAjB,MAAM,CAAC2B,cAAc,CAACZ,CAAC,EAAEG,EAAE,EAAEE,IAAI,CAAC;AACtC,CAAC,GAAK,UAASL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EACxB,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5BF,CAAC,CAACG,EAAE,CAAC,GAAGF,CAAC,CAACC,CAAC,CAAC;AAChB,CAAE,CAAC;AACH,IAAIW,kBAAkB,GAAI,IAAI,IAAI,IAAI,CAACA,kBAAkB,KAAM5B,MAAM,CAACc,MAAM,GAAI,UAASC,CAAC,EAAEc,CAAC,EAAE;EAC3F7B,MAAM,CAAC2B,cAAc,CAACZ,CAAC,EAAE,SAAS,EAAE;IAAEU,UAAU,EAAE,IAAI;IAAEK,KAAK,EAAED;EAAE,CAAC,CAAC;AACvE,CAAC,GAAI,UAASd,CAAC,EAAEc,CAAC,EAAE;EAChBd,CAAC,CAAC,SAAS,CAAC,GAAGc,CAAC;AACpB,CAAC,CAAC;AACF,IAAIE,YAAY,GAAI,IAAI,IAAI,IAAI,CAACA,YAAY,IAAK,UAAUC,GAAG,EAAE;EAC7D,IAAIA,GAAG,IAAIA,GAAG,CAACV,UAAU,EAAE,OAAOU,GAAG;EACrC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAID,GAAG,IAAI,IAAI,EAAE,KAAK,IAAIf,CAAC,IAAIe,GAAG,EAAE,IAAIf,CAAC,KAAK,SAAS,IAAIjB,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACqB,GAAG,EAAEf,CAAC,CAAC,EAAEJ,eAAe,CAACoB,MAAM,EAAED,GAAG,EAAEf,CAAC,CAAC;EACxIW,kBAAkB,CAACK,MAAM,EAAED,GAAG,CAAC;EAC/B,OAAOC,MAAM;AACjB,CAAC;AACD,IAAIC,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAU/B,CAAC,EAAEgC,CAAC,EAAE;EAClD,IAAIjC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIM,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,IAAI2B,CAAC,CAACC,OAAO,CAAC5B,CAAC,CAAC,GAAG,CAAC,EAC/EN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EACf,IAAIL,CAAC,IAAI,IAAI,IAAI,OAAOH,MAAM,CAACqC,qBAAqB,KAAK,UAAU,EAC/D,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAGR,MAAM,CAACqC,qBAAqB,CAAClC,CAAC,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACD,MAAM,EAAEH,CAAC,EAAE,EAAE;IACpE,IAAI+B,CAAC,CAACC,OAAO,CAAC5B,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIJ,MAAM,CAACS,SAAS,CAAC6B,oBAAoB,CAAC3B,IAAI,CAACR,CAAC,EAAEK,CAAC,CAACJ,CAAC,CAAC,CAAC,EAC1EF,CAAC,CAACM,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACK,CAAC,CAACJ,CAAC,CAAC,CAAC;EACzB;EACJ,OAAOF,CAAC;AACZ,CAAC;AACDF,MAAM,CAAC2B,cAAc,CAACY,OAAO,EAAE,YAAY,EAAE;EAAET,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,IAAIU,KAAK,GAAGT,YAAY,CAACU,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1C,IAAIC,eAAe,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACxD,IAAIE,WAAW,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAChD,IAAIG,IAAI,GAAG,CAAC,CAAC,EAAED,WAAW,CAACE,eAAe,EAAE,YAAY,EAAE,4HAA4H,EAAE,MAAM,CAAC;AAC/L,SAASC,UAAUA,CAACC,EAAE,EAAE;EACpB,IAAIC,EAAE,GAAGD,EAAE,CAACE,OAAO;IAAEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IAAEE,EAAE,GAAGH,EAAE,CAACI,KAAK;IAAEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,EAAE;IAAEE,EAAE,GAAGL,EAAE,CAACM,eAAe;IAAEA,eAAe,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;IAAEE,EAAE,GAAGP,EAAE,CAACQ,WAAW;IAAEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;IAAEE,EAAE,GAAGT,EAAE,CAACU,IAAI;IAAEA,IAAI,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEE,eAAe,GAAGxB,MAAM,CAACa,EAAE,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;EAC1X,IAAIY,KAAK,GAAG5D,QAAQ,CAAC;IAAE6D,UAAU,EAAE,wBAAwB;IAAEC,KAAK,EAAE,CAAC,CAAC,EAAEnB,eAAe,CAACoB,QAAQ,EAAEL,IAAI,CAAC;IAAEM,MAAM,EAAE,CAAC,CAAC,EAAErB,eAAe,CAACoB,QAAQ,EAAEL,IAAI,CAAC;IAAEO,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEC,cAAc,EAAEf,KAAK;IAAEgB,iBAAiB,EAAE,aAAa;IAAEC,eAAe,EAAEjB,KAAK;IAAEkB,gBAAgB,EAAElB,KAAK;IAAEmB,OAAO,EAAE,cAAc;IAAEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC5B,IAAI,EAAE,GAAG,CAAC,CAAC4B,MAAM,CAAC,IAAI,GAAGnB,eAAe,EAAE,sBAAsB,CAAC;IAAEoB,iBAAiB,EAAE;EAAO,CAAC,EAAElB,WAAW,CAAC;EACrc,IAAI,CAACN,OAAO,EAAE;IACV,OAAO,IAAI;EACf;EACA,OAAOT,KAAK,CAACkC,aAAa,CAAC,MAAM,EAAE3E,QAAQ,CAAC;IAAE4D,KAAK,EAAEA;EAAM,CAAC,EAAED,eAAe,CAAC,CAAC;AACnF;AACAnB,OAAO,CAACoC,OAAO,GAAG7B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}