import React from 'react';
import { AnyComponent, ExecutionProps } from '../types';
export default function withTheme<T extends AnyComponent>(Component: T): React.ForwardRefExoticComponent<React.PropsWithoutRef<JSX.LibraryManagedAttributes<T, ExecutionProps>> & React.RefAttributes<T>> & { [key in Exclude<keyof T, T extends React.MemoExoticComponent<any> ? "propTypes" | "defaultProps" | "displayName" | "$$typeof" | "type" | "compare" : T extends React.ForwardRefExoticComponent<any> ? "propTypes" | "defaultProps" | "displayName" | "$$typeof" | "render" : "length" | "propTypes" | "contextType" | "contextTypes" | "childContextTypes" | "defaultProps" | "displayName" | "getDerivedStateFromProps" | "getDerivedStateFromError" | "name" | "type" | "getDefaultProps" | "mixins" | "prototype" | "caller" | "callee" | "arguments" | "arity">]: T[key]; };
