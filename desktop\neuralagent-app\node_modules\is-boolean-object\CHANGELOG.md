# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.2](https://github.com/inspect-js/is-boolean-object/compare/v1.2.1...v1.2.2) - 2025-02-04

### Fixed

- [Fix] do not be tricked by fake Booleans [`#25`](https://github.com/inspect-js/is-boolean-object/issues/25)

### Commits

- [Dev <PERSON>] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape`, `core-js` [`a27608b`](https://github.com/inspect-js/is-boolean-object/commit/a27608b83f154875736bb5e77bf1a70da307b64f)
- [Deps] update `call-bound` [`b19953f`](https://github.com/inspect-js/is-boolean-object/commit/b19953f90f88435a0b0888692f065c959812f710)

## [v1.2.1](https://github.com/inspect-js/is-boolean-object/compare/v1.2.0...v1.2.1) - 2024-12-12

### Commits

- [Refactor] use `call-bound` directly [`bb5aa26`](https://github.com/inspect-js/is-boolean-object/commit/bb5aa266f9da864b59f58f1f61d807268f00e227)

## [v1.2.0](https://github.com/inspect-js/is-boolean-object/compare/v1.1.2...v1.2.0) - 2024-12-01

### Commits

- [actions] reuse common workflows [`380fa25`](https://github.com/inspect-js/is-boolean-object/commit/380fa254d963699ba7e1b7bfaee3cd4c50142f1a)
- [meta] use `npmignore` to autogenerate an npmignore file [`befa203`](https://github.com/inspect-js/is-boolean-object/commit/befa203ffa0e94c70d5b35aae407ea93e0bbc117)
- [actions] split out node 10-20, and 20+ [`ca31663`](https://github.com/inspect-js/is-boolean-object/commit/ca31663ef1e4195ffeef125fb337c5e58bf878ca)
- [New] add types [`6d58609`](https://github.com/inspect-js/is-boolean-object/commit/6d58609867b97b832ff5e73941b4164f0a9a78ec)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `core-js`, `safe-publish-latest`, `tape` [`06cc67e`](https://github.com/inspect-js/is-boolean-object/commit/06cc67eed7647dc9094611f03bd2802fb3695e37)
- [actions] update codecov uploader [`0722346`](https://github.com/inspect-js/is-boolean-object/commit/0722346b425c46e50864d76507c3d3a97678273b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`100acdf`](https://github.com/inspect-js/is-boolean-object/commit/100acdf9405f8496bdc71b7c383ab9e2119560af)
- [actions] update rebase action to use reusable workflow [`26333ff`](https://github.com/inspect-js/is-boolean-object/commit/26333ffc7bf92b7d751a68721cd7b27f8c59a250)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `core-js`, `tape` [`fde97ee`](https://github.com/inspect-js/is-boolean-object/commit/fde97eed23a8caa95beaa6fc710adf9526b99155)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `npmignore`, `tape` [`f5ed3c8`](https://github.com/inspect-js/is-boolean-object/commit/f5ed3c8b871451b5dac2f11e16ba3e35e5fdf82e)
- [Deps] update `call-bind`, `has-tostringtag` [`61912e2`](https://github.com/inspect-js/is-boolean-object/commit/61912e211369447287c5cbba952303e1897440bf)
- [Tests] replace `aud` with `npm audit` [`c6a0db5`](https://github.com/inspect-js/is-boolean-object/commit/c6a0db56cb39bd99255589c13c2dc3dde922c755)
- [meta] better `eccheck` command [`3a59ec6`](https://github.com/inspect-js/is-boolean-object/commit/3a59ec6a40479dd4d742531042797e2b79acb898)
- [Dev Deps] add missing peer dep [`c0e10db`](https://github.com/inspect-js/is-boolean-object/commit/c0e10db845b7e7329e6347d9de5fe0190276433c)

## [v1.1.2](https://github.com/inspect-js/is-boolean-object/compare/v1.1.1...v1.1.2) - 2021-08-05

### Commits

- [Refactor] use `has-tostringtag` to behave correctly in the presence of symbol shams [`6d319ea`](https://github.com/inspect-js/is-boolean-object/commit/6d319eac0ba237f7ba440a1fc4b32d007b1b0cf3)
- [Dev Deps] update `auto-changelog`, `core-js`, `eslint`, `tape` [`4f85bef`](https://github.com/inspect-js/is-boolean-object/commit/4f85bef244f8fdd9ab99db0afe0b8fa00c853709)

## [v1.1.1](https://github.com/inspect-js/is-boolean-object/compare/v1.1.0...v1.1.1) - 2021-05-07

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`7201c41`](https://github.com/inspect-js/is-boolean-object/commit/7201c41fc1fd9d64b51716b80fc63d95064a4a59)
- [Tests] run tests with core-js as well [`9590e61`](https://github.com/inspect-js/is-boolean-object/commit/9590e6135505e2e3f69c6d8785a539fca1b1e594)
- [meta] do not publish github action workflow files [`341472b`](https://github.com/inspect-js/is-boolean-object/commit/341472bbe9855030c7eda9340ee4284244f0a4ad)
- [readme] update repo URLs; remove travis badge [`9fdbbc6`](https://github.com/inspect-js/is-boolean-object/commit/9fdbbc64b2a70ee93fcfd95fc6c94c7ec2bbedd4)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`1cd35c9`](https://github.com/inspect-js/is-boolean-object/commit/1cd35c9b9b0b4af203f20bda0d7fd60798e57f99)
- [readme] add actions and codecov badges [`03769fe`](https://github.com/inspect-js/is-boolean-object/commit/03769feb1466f03b1345882ca0e4f8cacbbce9ce)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`db6598c`](https://github.com/inspect-js/is-boolean-object/commit/db6598c4cabcd0ffd2ba9b4525b53907f8b2ff1f)
- [Fix] do not use `Object.prototype.toString` when `Symbol.toStringTag` is shammed [`e0b8a9f`](https://github.com/inspect-js/is-boolean-object/commit/e0b8a9f0fc1290a0a29c75967d56bc1c17eb8d2d)
- [readme] remove defunct testling badge [`986a621`](https://github.com/inspect-js/is-boolean-object/commit/986a6217da7385f7063e1d4e4bf5be2892d00c20)
- [meta] use `prepublishOnly` script for npm 7+ [`7bb3b29`](https://github.com/inspect-js/is-boolean-object/commit/7bb3b2902008ca07af4185ca98bc41b3222d579f)
- [Deps] update `call-bind` [`3af6a71`](https://github.com/inspect-js/is-boolean-object/commit/3af6a71e1004c79567630ac9944b9f2cc184ac77)
- [meta] do not publish corejs test file [`d911f03`](https://github.com/inspect-js/is-boolean-object/commit/d911f0368b6922645d15b9e28f3ed92e1badcef4)
- [actions] update workflows [`9bb3d90`](https://github.com/inspect-js/is-boolean-object/commit/9bb3d9015f377280324b162a3062d21936707216)

## [v1.1.0](https://github.com/inspect-js/is-boolean-object/compare/v1.0.1...v1.1.0) - 2020-12-05

### Commits

- [Tests] migrate tests to Github Actions [`6cdb652`](https://github.com/inspect-js/is-boolean-object/commit/6cdb652add3c6e44c2f7fe07c5ca4c0d14ddc2c1)
- [Tests] run `nyc` on all tests [`9a33076`](https://github.com/inspect-js/is-boolean-object/commit/9a33076d14869bf5120a6ca3903bcb9a008cf2e5)
- [Tests] add .editorconfig [`bb401c0`](https://github.com/inspect-js/is-boolean-object/commit/bb401c084416b010d64e0c5a74465b37addab31f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`5cb2405`](https://github.com/inspect-js/is-boolean-object/commit/5cb24052ca84d840e929f05cd1fe6c03b85ec032)
- [Robustness] use `call-bind` to avoid a dependency on `.call` [`76d87ae`](https://github.com/inspect-js/is-boolean-object/commit/76d87ae74235a9995d39bcf5783c04c744c34520)
- [actions] add "Allow Edits" workflow [`337206a`](https://github.com/inspect-js/is-boolean-object/commit/337206af74bd7c340bc938ab6dc0535c08490b3d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`11f0481`](https://github.com/inspect-js/is-boolean-object/commit/11f0481efca28a241a35d384e2a302b1bcdc9a37)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`b9602c8`](https://github.com/inspect-js/is-boolean-object/commit/b9602c8ca11be138722187c1fb0a5b25a57a4edc)
- [Dev Deps] update `auto-changelog`, `tape`; add `aud` [`999e9e2`](https://github.com/inspect-js/is-boolean-object/commit/999e9e224d4eec8b20fc9c3431e9ba42caad79c9)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`bbb6728`](https://github.com/inspect-js/is-boolean-object/commit/bbb6728b9410f9d3e2d266523a477127e5e4c16f)

## [v1.0.1](https://github.com/inspect-js/is-boolean-object/compare/v1.0.0...v1.0.1) - 2019-12-18

### Commits

- [Tests] use shared travis-ci configs [`a1778b8`](https://github.com/inspect-js/is-boolean-object/commit/a1778b81ab4fe4479176de854e4e233cc441f183)
- Update `eslint`; use my personal shared `eslint` config. [`2c42c50`](https://github.com/inspect-js/is-boolean-object/commit/2c42c50a0654044b6c7e2a4ab18227e8c275464b)
- [Tests] remove `jscs` [`3807025`](https://github.com/inspect-js/is-boolean-object/commit/380702504fabc47fe22f61c4847379023d31a657)
- [Tests] up to `node` `v8.4`, `v7.10`, `v6.11`, `v5.12`, `v4.8`; improve matrix; newer npm breaks in older node; improve scripts. [`a02b986`](https://github.com/inspect-js/is-boolean-object/commit/a02b98682b285de09e2c5a895627771d6a7f552c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is`, `jscs`, `nsp`, `semver`, `tape` [`d9030a9`](https://github.com/inspect-js/is-boolean-object/commit/d9030a99b8e274c76ae5f23f31d9d085a7e25272)
- Update `eslint`, `nsp` [`a1b6388`](https://github.com/inspect-js/is-boolean-object/commit/a1b6388fabd0de51a02c567953826344ef05890c)
- [Tests] up to `node` `v10.0`, `v9.11`, `v8.11`, `v6.14`, `v4.9`; use `nvm install-latest-npm` [`17a0fd3`](https://github.com/inspect-js/is-boolean-object/commit/17a0fd391e32635ec3434baca0d062f8abeeb592)
- [Tests] up to `node` `v12.10`, `v11.15`, `v10.16`, `v8.16`, `v6.17` [`0b1818f`](https://github.com/inspect-js/is-boolean-object/commit/0b1818fdcaebc6133e515dfe3b5a8930b38999b5)
- [meta] remove unused Makefile and associated utilities [`33dc0ae`](https://github.com/inspect-js/is-boolean-object/commit/33dc0ae35a03e91f81b1bb3db5ba763dacadbfa2)
- Update `covert`, `jscs`, `eslint`, `semver` [`7e513c1`](https://github.com/inspect-js/is-boolean-object/commit/7e513c12998a651c14f62b3ecfb7215a5cc5ee8f)
- [Tests] up to `node` `v11.4`, `v10.14`, `v8.14`, `v6.15` [`992b849`](https://github.com/inspect-js/is-boolean-object/commit/992b84933760e0fe6ba2cee74ad7fff507f28128)
- [meta] add `auto-changelog` [`63d71b8`](https://github.com/inspect-js/is-boolean-object/commit/63d71b8beb9aeb77cab3d2db2cf643f1b8b1a55c)
- Update `tape`, `eslint`, `semver` [`76aea69`](https://github.com/inspect-js/is-boolean-object/commit/76aea699fb315ac460799182d707c388b4a4e017)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`a6cbec0`](https://github.com/inspect-js/is-boolean-object/commit/a6cbec09940b6f2c7cf366526a94c0c4756508f5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `replace`, `semver`, `tape`; add `safe-publish-latest` [`7cf6bb0`](https://github.com/inspect-js/is-boolean-object/commit/7cf6bb05ba7bac504df680dd9ca625fba6dccb5a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest` [`57d713c`](https://github.com/inspect-js/is-boolean-object/commit/57d713cca2ccbbbf1da1142ee5e8236d12551f76)
- [actions] add automatic rebasing / merge commit blocking [`f7a2bdb`](https://github.com/inspect-js/is-boolean-object/commit/f7a2bdb905e07d75c65593359f81bfeda9fe9826)
- [meta] create FUNDING.yml [`9765e73`](https://github.com/inspect-js/is-boolean-object/commit/9765e738cccdd5ff6c89b21324119a2bf4064fbd)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is`, `replace`, `semver`, `tape` [`5c16b56`](https://github.com/inspect-js/is-boolean-object/commit/5c16b56a5a2d36c6a70c1bd396b3a6c931f655db)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`5717aad`](https://github.com/inspect-js/is-boolean-object/commit/5717aadd8b0e8c76ccb194d1845ad8cc120f29c6)
- [Dev Deps] update `is`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`80b924d`](https://github.com/inspect-js/is-boolean-object/commit/80b924dd270188e1e928f4141078bee14d810e9b)
- [Dev Deps] update `jscs` [`2e5479e`](https://github.com/inspect-js/is-boolean-object/commit/2e5479e56b6d33288582cfe8c254ca081e79500c)
- Test up to `io.js` `v2.2` [`93379a4`](https://github.com/inspect-js/is-boolean-object/commit/93379a4b48ba719113006ab08bbe6679c8a27293)
- [Tests] remove `nsp`; use `npm audit`; allow to fail for now [`36ae30a`](https://github.com/inspect-js/is-boolean-object/commit/36ae30acffe4a892ea0882a793b8a90f09d08fdf)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`ef76976`](https://github.com/inspect-js/is-boolean-object/commit/ef76976db22f2867fca2ee377fdbc9da81f0d142)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`a1182bd`](https://github.com/inspect-js/is-boolean-object/commit/a1182bd99e5d31e113107241f0d697e71b27bf7b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`4f79b47`](https://github.com/inspect-js/is-boolean-object/commit/4f79b474f0643fd2c9dbc863949a61cf01255b6f)
- [Tests] up to `node` `v10.3` [`5e96464`](https://github.com/inspect-js/is-boolean-object/commit/5e96464fc08eacc45f5f58fe7800ddbc45e40cea)
- [Dev Deps] update `eslint`, `nsp`, `semver`, `tape` [`e1eb3fa`](https://github.com/inspect-js/is-boolean-object/commit/e1eb3fad8bad65140db3bd2381bb3bea6a9c6242)
- Only apps should have lockfiles. [`c7f301f`](https://github.com/inspect-js/is-boolean-object/commit/c7f301ff368f9e04f7f64a2ce9b6ae1aec803e69)
- [meta] add `funding` field [`fad0366`](https://github.com/inspect-js/is-boolean-object/commit/fad03662becea5db7cef29d816d173ad771be86a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`80d39d9`](https://github.com/inspect-js/is-boolean-object/commit/80d39d90205d3bb564acb80b0625e90c267347fd)
- [Tests] use `eclint` instead of `editorconfig-tools` [`980e91b`](https://github.com/inspect-js/is-boolean-object/commit/980e91b186a728f77366ba16b7bebc813b9cc3b0)
- [Dev Deps] Update `tape`, `eslint` [`9960830`](https://github.com/inspect-js/is-boolean-object/commit/9960830876a3672686c569fde3d43bb7983955f1)
- Test up to `io.js` `v3.0` [`a3c3cd0`](https://github.com/inspect-js/is-boolean-object/commit/a3c3cd087d7fcefbf4f3525c05f4cee3b6e5b0ef)
- [Dev Deps] update `tape` [`217fbd6`](https://github.com/inspect-js/is-boolean-object/commit/217fbd6bb2989f9304ad95cd49697da7fe03b8d5)
- [Tests] only audit prod deps [`89284ee`](https://github.com/inspect-js/is-boolean-object/commit/89284ee17dce1d044df0ca9e006072f25742bbaf)
- [Performance] only use toStringTag code path when the value has that property [`2863bc5`](https://github.com/inspect-js/is-boolean-object/commit/2863bc5b72680f05ace8e66fddcf48966b942d55)
- [Dev Deps] update `replace` [`53e72a5`](https://github.com/inspect-js/is-boolean-object/commit/53e72a5ceca5b3a82e6407829f9227df9df6d329)
- [Enhancement] slight optimization for `null` [`a90a3c4`](https://github.com/inspect-js/is-boolean-object/commit/a90a3c4464d0300e23384d96fb4281b55b7fd723)
- [Dev Deps] update `tape` [`9377bd5`](https://github.com/inspect-js/is-boolean-object/commit/9377bd5110e99d8ec550f24ef3f6ead62a8f1f50)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`3085530`](https://github.com/inspect-js/is-boolean-object/commit/30855304841854f79e406372f524efe4bc7d8c04)
- Test on `io.js` `v2.4` [`8af335c`](https://github.com/inspect-js/is-boolean-object/commit/8af335ca82a0eeba4a0a593775e4caf744834ec4)
- Test on `io.js` `v2.3` [`1eb3424`](https://github.com/inspect-js/is-boolean-object/commit/1eb3424bef528551f5c99a754281a51d92e40ab1)

## v1.0.0 - 2015-04-28

### Commits

- Dotfiles [`6b9b998`](https://github.com/inspect-js/is-boolean-object/commit/6b9b998bb238a32d4829c9f9bf274e5ca15023ee)
- `make release` [`d5e50b3`](https://github.com/inspect-js/is-boolean-object/commit/d5e50b33a3cd8d8abe7de8ae36e2944c24ce76ba)
- package.json [`117676a`](https://github.com/inspect-js/is-boolean-object/commit/117676a48609e636d4257c1b35c695ff20939211)
- Read me [`ef327a7`](https://github.com/inspect-js/is-boolean-object/commit/ef327a74c7f73e64cfa3c20a9620ef7accf8b762)
- Initial commit [`2346886`](https://github.com/inspect-js/is-boolean-object/commit/2346886252b9637c1af6851a3fc2cbc98bc986aa)
- Tests [`67211f8`](https://github.com/inspect-js/is-boolean-object/commit/67211f8bff1a49e5df219935765b83573c097353)
- Implementation [`2d88bd6`](https://github.com/inspect-js/is-boolean-object/commit/2d88bd6e1ef0f07f5a639775eb89f3b78e12eb65)
