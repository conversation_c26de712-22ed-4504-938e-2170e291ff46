{"additionalProperties": false, "type": "object", "properties": {"additionalManifestEntries": {"description": "A list of entries to be precached, in addition to any entries that are\ngenerated as part of the build configuration.", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/ManifestEntry"}, {"type": "string"}]}}, "dontCacheBustURLsMatching": {"description": "Assets that match this will be assumed to be uniquely versioned via their\nURL, and exempted from the normal HTTP cache-busting that's done when\npopulating the precache. While not required, it's recommended that if your\nexisting build process already inserts a `[hash]` value into each filename,\nyou provide a RegExp that will detect that, as it will reduce the bandwidth\nconsumed when precaching.", "$ref": "#/definitions/RegExp"}, "manifestTransforms": {"description": "One or more functions which will be applied sequentially against the\ngenerated manifest. If `modifyURLPrefix` or `dontCacheBustURLsMatching` are\nalso specified, their corresponding transformations will be applied first.", "type": "array", "items": {}}, "maximumFileSizeToCacheInBytes": {"description": "This value can be used to determine the maximum size of files that will be\nprecached. This prevents you from inadvertently precaching very large files\nthat might have accidentally matched one of your patterns.", "default": 2097152, "type": "number"}, "modifyURLPrefix": {"description": "An object mapping string prefixes to replacement string values. This can be\nused to, e.g., remove or add a path prefix from a manifest entry if your\nweb hosting setup doesn't match your local filesystem setup. As an\nalternative with more flexibility, you can use the `manifestTransforms`\noption and provide a function that modifies the entries in the manifest\nusing whatever logic you provide.\n\nExample usage:\n\n```\n// Replace a '/dist/' prefix with '/', and also prepend\n// '/static' to every URL.\nmodifyURLPrefix: {\n  '/dist/': '/',\n  '': '/static',\n}\n```", "type": "object", "additionalProperties": {"type": "string"}}, "chunks": {"description": "One or more chunk names whose corresponding output files should be included\nin the precache manifest.", "type": "array", "items": {"type": "string"}}, "exclude": {"description": "One or more specifiers used to exclude assets from the precache manifest.\nThis is interpreted following\n[the same rules](https://webpack.js.org/configuration/module/#condition)\nas `webpack`'s standard `exclude` option.\nIf not provided, the default value is `[/\\.map$/, /^manifest.*\\.js$]`.", "type": "array", "items": {}}, "excludeChunks": {"description": "One or more chunk names whose corresponding output files should be excluded\nfrom the precache manifest.", "type": "array", "items": {"type": "string"}}, "include": {"description": "One or more specifiers used to include assets in the precache manifest.\nThis is interpreted following\n[the same rules](https://webpack.js.org/configuration/module/#condition)\nas `webpack`'s standard `include` option.", "type": "array", "items": {}}, "mode": {"description": "If set to 'production', then an optimized service worker bundle that\nexcludes debugging info will be produced. If not explicitly configured\nhere, the `process.env.NODE_ENV` value will be used, and failing that, it\nwill fall back to `'production'`.", "default": "production", "type": ["null", "string"]}, "babelPresetEnvTargets": {"description": "The [targets](https://babeljs.io/docs/en/babel-preset-env#targets) to pass\nto `babel-preset-env` when transpiling the service worker bundle.", "default": ["chrome >= 56"], "type": "array", "items": {"type": "string"}}, "cacheId": {"description": "An optional ID to be prepended to cache names. This is primarily useful for\nlocal development where multiple sites may be served from the same\n`http://localhost:port` origin.", "type": ["null", "string"]}, "cleanupOutdatedCaches": {"description": "Whether or not Workbox should attempt to identify and delete any precaches\ncreated by older, incompatible versions.", "default": false, "type": "boolean"}, "clientsClaim": {"description": "Whether or not the service worker should [start controlling](https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#clientsclaim)\nany existing clients as soon as it activates.", "default": false, "type": "boolean"}, "directoryIndex": {"description": "If a navigation request for a URL ending in `/` fails to match a precached\nURL, this value will be appended to the URL and that will be checked for a\nprecache match. This should be set to what your web server is using for its\ndirectory index.", "type": ["null", "string"]}, "disableDevLogs": {"default": false, "type": "boolean"}, "ignoreURLParametersMatching": {"description": "Any search parameter names that match against one of the RegExp in this\narray will be removed before looking for a precache match. This is useful\nif your users might request URLs that contain, for example, URL parameters\nused to track the source of the traffic. If not provided, the default value\nis `[/^utm_/, /^fbclid$/]`.", "type": "array", "items": {"$ref": "#/definitions/RegExp"}}, "importScripts": {"description": "A list of JavaScript files that should be passed to\n[`importScripts()`](https://developer.mozilla.org/en-US/docs/Web/API/WorkerGlobalScope/importScripts)\ninside the generated service worker file. This is  useful when you want to\nlet Workbox create your top-level service worker file, but want to include\nsome additional code, such as a push event listener.", "type": "array", "items": {"type": "string"}}, "inlineWorkboxRuntime": {"description": "Whether the runtime code for the Workbox library should be included in the\ntop-level service worker, or split into a separate file that needs to be\ndeployed alongside the service worker. Keeping the runtime separate means\nthat users will not have to re-download the Workbox code each time your\ntop-level service worker changes.", "default": false, "type": "boolean"}, "navigateFallback": {"description": "If specified, all\n[navigation requests](https://developers.google.com/web/fundamentals/primers/service-workers/high-performance-loading#first_what_are_navigation_requests)\nfor URLs that aren't precached will be fulfilled with the HTML at the URL\nprovided. You must pass in the URL of an HTML document that is listed in\nyour precache manifest. This is meant to be used in a Single Page App\nscenario, in which you want all navigations to use common\n[App Shell HTML](https://developers.google.com/web/fundamentals/architecture/app-shell).", "default": null, "type": ["null", "string"]}, "navigateFallbackAllowlist": {"description": "An optional array of regular expressions that restricts which URLs the\nconfigured `navigateFallback` behavior applies to. This is useful if only a\nsubset of your site's URLs should be treated as being part of a\n[Single Page App](https://en.wikipedia.org/wiki/Single-page_application).\nIf both `navigateFallbackDenylist` and `navigateFallbackAllowlist` are\nconfigured, the denylist takes precedent.\n\n*Note*: These RegExps may be evaluated against every destination URL during\na navigation. Avoid using\n[complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\nor else your users may see delays when navigating your site.", "type": "array", "items": {"$ref": "#/definitions/RegExp"}}, "navigateFallbackDenylist": {"description": "An optional array of regular expressions that restricts which URLs the\nconfigured `navigateFallback` behavior applies to. This is useful if only a\nsubset of your site's URLs should be treated as being part of a\n[Single Page App](https://en.wikipedia.org/wiki/Single-page_application).\nIf both `navigateFallbackDenylist` and `navigateFallbackAllowlist` are\nconfigured, the denylist takes precedence.\n\n*Note*: These RegExps may be evaluated against every destination URL during\na navigation. Avoid using\n[complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\nor else your users may see delays when navigating your site.", "type": "array", "items": {"$ref": "#/definitions/RegExp"}}, "navigationPreload": {"description": "Whether or not to enable\n[navigation preload](https://developers.google.com/web/tools/workbox/modules/workbox-navigation-preload)\nin the generated service worker. When set to true, you must also use\n`runtimeCaching` to set up an appropriate response strategy that will match\nnavigation requests, and make use of the preloaded response.", "default": false, "type": "boolean"}, "offlineGoogleAnalytics": {"description": "Controls whether or not to include support for\n[offline Google Analytics](https://developers.google.com/web/tools/workbox/guides/enable-offline-analytics).\nWhen `true`, the call to `workbox-google-analytics`'s `initialize()` will\nbe added to your generated service worker. When set to an `Object`, that\nobject will be passed in to the `initialize()` call, allowing you to\ncustomize the behavior.", "default": false, "anyOf": [{"$ref": "#/definitions/GoogleAnalyticsInitializeOptions"}, {"type": "boolean"}]}, "runtimeCaching": {"description": "When using Workbox's build tools to generate your service worker, you can\nspecify one or more runtime caching configurations. These are then\ntranslated to {@link workbox-routing.registerRoute} calls using the match\nand handler configuration you define.\n\nFor all of the options, see the {@link workbox-build.RuntimeCaching}\ndocumentation. The example below shows a typical configuration, with two\nruntime routes defined:", "type": "array", "items": {"$ref": "#/definitions/RuntimeCaching"}}, "skipWaiting": {"description": "Whether to add an unconditional call to [`skipWaiting()`](https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#skip_the_waiting_phase)\nto the generated service worker. If `false`, then a `message` listener will\nbe added instead, allowing client pages to trigger `skipWaiting()` by\ncalling `postMessage({type: 'SKIP_WAITING'})` on a waiting service worker.", "default": false, "type": "boolean"}, "sourcemap": {"description": "Whether to create a sourcemap for the generated service worker files.", "default": true, "type": "boolean"}, "importScriptsViaChunks": {"description": "One or more names of webpack chunks. The content of those chunks will be\nincluded in the generated service worker, via a call to `importScripts()`.", "type": "array", "items": {"type": "string"}}, "swDest": {"description": "The asset name of the service worker file created by this plugin.", "default": "service-worker.js", "type": "string"}}, "definitions": {"ManifestEntry": {"type": "object", "properties": {"integrity": {"type": "string"}, "revision": {"type": ["null", "string"]}, "url": {"type": "string"}}, "additionalProperties": false, "required": ["revision", "url"]}, "RegExp": {"type": "object", "properties": {"source": {"type": "string"}, "global": {"type": "boolean"}, "ignoreCase": {"type": "boolean"}, "multiline": {"type": "boolean"}, "lastIndex": {"type": "number"}, "flags": {"type": "string"}, "sticky": {"type": "boolean"}, "unicode": {"type": "boolean"}, "dotAll": {"type": "boolean"}}, "additionalProperties": false, "required": ["dotAll", "flags", "global", "ignoreCase", "lastIndex", "multiline", "source", "sticky", "unicode"]}, "GoogleAnalyticsInitializeOptions": {"type": "object", "properties": {"cacheName": {"type": "string"}, "parameterOverrides": {"type": "object", "additionalProperties": {"type": "string"}}, "hitFilter": {"type": "object", "additionalProperties": false}}, "additionalProperties": false}, "RuntimeCaching": {"type": "object", "properties": {"handler": {"description": "This determines how the runtime route will generate a response.\nTo use one of the built-in {@link workbox-strategies}, provide its name,\nlike `'NetworkFirst'`.\nAlternatively, this can be a {@link workbox-core.RouteHandler} callback\nfunction with custom response logic.", "anyOf": [{"$ref": "#/definitions/RouteHandlerCallback"}, {"$ref": "#/definitions/RouteHandlerObject"}, {"enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NetworkFirst", "NetworkOnly", "StaleWhileRevalidate"], "type": "string"}]}, "method": {"description": "The HTTP method to match against. The default value of `'GET'` is normally\nsufficient, unless you explicitly need to match `'POST'`, `'PUT'`, or\nanother type of request.", "default": "GET", "enum": ["DELETE", "GET", "HEAD", "PATCH", "POST", "PUT"], "type": "string"}, "options": {"type": "object", "properties": {"backgroundSync": {"description": "Configuring this will add a\n{@link workbox-background-sync.BackgroundSyncPlugin} instance to the\n{@link workbox-strategies} configured in `handler`.", "type": "object", "properties": {"name": {"type": "string"}, "options": {"$ref": "#/definitions/QueueOptions"}}, "additionalProperties": false, "required": ["name"]}, "broadcastUpdate": {"description": "Configuring this will add a\n{@link workbox-broadcast-update.BroadcastUpdatePlugin} instance to the\n{@link workbox-strategies} configured in `handler`.", "type": "object", "properties": {"channelName": {"type": "string"}, "options": {"$ref": "#/definitions/BroadcastCacheUpdateOptions"}}, "additionalProperties": false, "required": ["options"]}, "cacheableResponse": {"description": "Configuring this will add a\n{@link workbox-cacheable-response.CacheableResponsePlugin} instance to\nthe {@link workbox-strategies} configured in `handler`.", "$ref": "#/definitions/CacheableResponseOptions"}, "cacheName": {"description": "If provided, this will set the `cacheName` property of the\n{@link workbox-strategies} configured in `handler`.", "type": ["null", "string"]}, "expiration": {"description": "Configuring this will add a\n{@link workbox-expiration.ExpirationPlugin} instance to\nthe {@link workbox-strategies} configured in `handler`.", "$ref": "#/definitions/ExpirationPluginOptions"}, "networkTimeoutSeconds": {"description": "If provided, this will set the `networkTimeoutSeconds` property of the\n{@link workbox-strategies} configured in `handler`. Note that only\n`'NetworkFirst'` and `'NetworkOnly'` support `networkTimeoutSeconds`.", "type": "number"}, "plugins": {"description": "Configuring this allows the use of one or more Workbox plugins that\ndon't have \"shortcut\" options (like `expiration` for\n{@link workbox-expiration.ExpirationPlugin}). The plugins provided here\nwill be added to the {@link workbox-strategies} configured in `handler`.", "type": "array", "items": {"$ref": "#/definitions/WorkboxPlugin"}}, "precacheFallback": {"description": "Configuring this will add a\n{@link workbox-precaching.PrecacheFallbackPlugin} instance to\nthe {@link workbox-strategies} configured in `handler`.", "type": "object", "properties": {"fallbackURL": {"type": "string"}}, "additionalProperties": false, "required": ["fallbackURL"]}, "rangeRequests": {"description": "Enabling this will add a\n{@link workbox-range-requests.RangeRequestsPlugin} instance to\nthe {@link workbox-strategies} configured in `handler`.", "type": "boolean"}, "fetchOptions": {"description": "Configuring this will pass along the `fetchOptions` value to\nthe {@link workbox-strategies} configured in `handler`.", "$ref": "#/definitions/RequestInit"}, "matchOptions": {"description": "Configuring this will pass along the `matchOptions` value to\nthe {@link workbox-strategies} configured in `handler`.", "$ref": "#/definitions/CacheQueryOptions"}}, "additionalProperties": false}, "urlPattern": {"description": "This match criteria determines whether the configured handler will\ngenerate a response for any requests that don't match one of the precached\nURLs. If multiple `RuntimeCaching` routes are defined, then the first one\nwhose `urlPattern` matches will be the one that responds.\n\nThis value directly maps to the first parameter passed to\n{@link workbox-routing.registerRoute}. It's recommended to use a\n{@link workbox-core.RouteMatchCallback} function for greatest flexibility.", "anyOf": [{"$ref": "#/definitions/RegExp"}, {"$ref": "#/definitions/RouteMatchCallback"}, {"type": "string"}]}}, "additionalProperties": false, "required": ["handler", "urlPattern"]}, "RouteHandlerCallback": {}, "RouteHandlerObject": {"description": "An object with a `handle` method of type `RouteHandlerCallback`.\n\nA `Route` object can be created with either an `RouteHandlerCallback`\nfunction or this `RouteHandler` object. The benefit of the `RouteHandler`\nis it can be extended (as is done by the `workbox-strategies` package).", "type": "object", "properties": {"handle": {"$ref": "#/definitions/RouteHandlerCallback"}}, "additionalProperties": false, "required": ["handle"]}, "QueueOptions": {"type": "object", "properties": {"forceSyncFallback": {"type": "boolean"}, "maxRetentionTime": {"type": "number"}, "onSync": {"$ref": "#/definitions/OnSyncCallback"}}, "additionalProperties": false}, "OnSyncCallback": {}, "BroadcastCacheUpdateOptions": {"type": "object", "properties": {"headersToCheck": {"type": "array", "items": {"type": "string"}}, "generatePayload": {"type": "object", "additionalProperties": false}, "notifyAllClients": {"type": "boolean"}}, "additionalProperties": false}, "CacheableResponseOptions": {"type": "object", "properties": {"statuses": {"type": "array", "items": {"type": "number"}}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "ExpirationPluginOptions": {"type": "object", "properties": {"maxEntries": {"type": "number"}, "maxAgeSeconds": {"type": "number"}, "matchOptions": {"$ref": "#/definitions/CacheQueryOptions"}, "purgeOnQuotaError": {"type": "boolean"}}, "additionalProperties": false}, "CacheQueryOptions": {"type": "object", "properties": {"ignoreMethod": {"type": "boolean"}, "ignoreSearch": {"type": "boolean"}, "ignoreVary": {"type": "boolean"}}, "additionalProperties": false}, "WorkboxPlugin": {"description": "An object with optional lifecycle callback properties for the fetch and\ncache operations.", "type": "object", "properties": {"cacheDidUpdate": {}, "cachedResponseWillBeUsed": {}, "cacheKeyWillBeUsed": {}, "cacheWillUpdate": {}, "fetchDidFail": {}, "fetchDidSucceed": {}, "handlerDidComplete": {}, "handlerDidError": {}, "handlerDidRespond": {}, "handlerWillRespond": {}, "handlerWillStart": {}, "requestWillFetch": {}}, "additionalProperties": false}, "CacheDidUpdateCallback": {"type": "object", "additionalProperties": false}, "CachedResponseWillBeUsedCallback": {"type": "object", "additionalProperties": false}, "CacheKeyWillBeUsedCallback": {"type": "object", "additionalProperties": false}, "CacheWillUpdateCallback": {"type": "object", "additionalProperties": false}, "FetchDidFailCallback": {"type": "object", "additionalProperties": false}, "FetchDidSucceedCallback": {"type": "object", "additionalProperties": false}, "HandlerDidCompleteCallback": {"type": "object", "additionalProperties": false}, "HandlerDidErrorCallback": {"type": "object", "additionalProperties": false}, "HandlerDidRespondCallback": {"type": "object", "additionalProperties": false}, "HandlerWillRespondCallback": {"type": "object", "additionalProperties": false}, "HandlerWillStartCallback": {"type": "object", "additionalProperties": false}, "RequestWillFetchCallback": {"type": "object", "additionalProperties": false}, "RequestInit": {"type": "object", "properties": {"body": {"anyOf": [{"$ref": "#/definitions/ArrayBuffer"}, {"$ref": "#/definitions/ArrayBufferView"}, {"$ref": "#/definitions/ReadableStream<any>"}, {"$ref": "#/definitions/Blob"}, {"$ref": "#/definitions/FormData"}, {"$ref": "#/definitions/URLSearchParams"}, {"type": ["null", "string"]}]}, "cache": {"enum": ["default", "force-cache", "no-cache", "no-store", "only-if-cached", "reload"], "type": "string"}, "credentials": {"enum": ["include", "omit", "same-origin"], "type": "string"}, "headers": {"anyOf": [{"$ref": "#/definitions/Record<string,string>"}, {"type": "array", "items": {"type": "array", "items": [{"type": "string"}, {"type": "string"}], "minItems": 2, "maxItems": 2}}, {"$ref": "#/definitions/Headers"}]}, "integrity": {"type": "string"}, "keepalive": {"type": "boolean"}, "method": {"type": "string"}, "mode": {"enum": ["cors", "navigate", "no-cors", "same-origin"], "type": "string"}, "redirect": {"enum": ["error", "follow", "manual"], "type": "string"}, "referrer": {"type": "string"}, "referrerPolicy": {"enum": ["", "no-referrer", "no-referrer-when-downgrade", "origin", "origin-when-cross-origin", "same-origin", "strict-origin", "strict-origin-when-cross-origin", "unsafe-url"], "type": "string"}, "signal": {"anyOf": [{"$ref": "#/definitions/AbortSignal"}, {"type": "null"}]}, "window": {"type": "null"}}, "additionalProperties": false}, "ArrayBuffer": {"type": "object", "properties": {"byteLength": {"type": "number"}, "__@toStringTag@25": {"type": "string"}}, "additionalProperties": false, "required": ["__@toStringTag@25", "byteLength"]}, "ArrayBufferView": {"type": "object", "properties": {"buffer": {"$ref": "#/definitions/ArrayBufferLike"}, "byteLength": {"type": "number"}, "byteOffset": {"type": "number"}}, "additionalProperties": false, "required": ["buffer", "byteLength", "byteOffset"]}, "ArrayBufferLike": {"anyOf": [{"$ref": "#/definitions/ArrayBuffer"}, {"$ref": "#/definitions/SharedArrayBuffer"}]}, "SharedArrayBuffer": {"type": "object", "properties": {"byteLength": {"type": "number"}, "__@species@598": {"$ref": "#/definitions/SharedArrayBuffer"}, "__@toStringTag@25": {"type": "string", "enum": ["SharedArrayBuffer"]}}, "additionalProperties": false, "required": ["__@species@598", "__@toStringTag@25", "byteLength"]}, "ReadableStream<any>": {"type": "object", "properties": {"locked": {"type": "boolean"}}, "additionalProperties": false, "required": ["locked"]}, "Blob": {"type": "object", "properties": {"size": {"type": "number"}, "type": {"type": "string"}}, "additionalProperties": false, "required": ["size", "type"]}, "FormData": {"type": "object", "additionalProperties": false}, "URLSearchParams": {"type": "object", "additionalProperties": false}, "Record<string,string>": {"type": "object", "additionalProperties": false}, "Headers": {"type": "object", "additionalProperties": false}, "AbortSignal": {}, "RouteMatchCallback": {}}, "$schema": "http://json-schema.org/draft-07/schema#"}