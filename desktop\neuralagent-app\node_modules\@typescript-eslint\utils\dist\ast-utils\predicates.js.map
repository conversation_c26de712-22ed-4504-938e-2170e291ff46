{"version": 3, "file": "predicates.js", "sourceRoot": "", "sources": ["../../src/ast-utils/predicates.ts"], "names": [], "mappings": ";;;AACA,4CAA+D;AAC/D,uCAMmB;AAEnB,MAAM,yBAAyB,GAAG,IAAA,qCAA2B,EAC3D,2BAAe,CAAC,UAAU,EAC1B,EAAE,KAAK,EAAE,IAAI,EAAE,CAChB,CAAC;AAqKA,8DAAyB;AAnK3B,MAAM,4BAA4B,GAAG,IAAA,wCAA8B,EACjE,2BAAe,CAAC,UAAU,EAC1B,EAAE,KAAK,EAAE,IAAI,EAAE,CAChB,CAAC;AA+JA,oEAA4B;AA7J9B,MAAM,4BAA4B,GAAG,IAAA,qCAA2B,EAC9D,2BAAe,CAAC,UAAU,EAC1B,EAAE,KAAK,EAAE,GAAG,EAAE,CACf,CAAC;AAwJA,oEAA4B;AAtJ9B,MAAM,+BAA+B,GAAG,IAAA,wCAA8B,EACpE,2BAAe,CAAC,UAAU,EAC1B,EAAE,KAAK,EAAE,GAAG,EAAE,CACf,CAAC;AAoJA,0EAA+B;AAlJjC;;GAEG;AACH,MAAM,wBAAwB,GAAG,IAAA,oCAA0B,EACzD,0BAAc,CAAC,cAAc;AAC7B,uDAAuD;AACvD,4CAA4C;AAC5C,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;AA6IA,4DAAwB;AA3I1B;;GAEG;AACH,MAAM,mBAAmB,GAAG,IAAA,oCAA0B,EACpD,0BAAc,CAAC,iBAAiB,EAChC,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;AAgIA,kDAAmB;AA9HrB;;;;;;GAMG;AACH,MAAM,eAAe,GAAG,IAAA,uBAAa,EAAC;IACpC,0BAAc,CAAC,cAAc;IAC7B,0BAAc,CAAC,eAAe;CACtB,CAAC,CAAC;AA6HV,0CAAe;AA3HjB,MAAM,oBAAoB,GAAG,IAAA,sBAAY,EAAC,0BAAc,CAAC,kBAAkB,CAAC,CAAC;AA6H3E,oDAAoB;AA3HtB,MAAM,aAAa,GAAG;IACpB,0BAAc,CAAC,uBAAuB;IACtC,0BAAc,CAAC,mBAAmB;IAClC,0BAAc,CAAC,kBAAkB;CACzB,CAAC;AACX,MAAM,UAAU,GAAG,IAAA,uBAAa,EAAC,aAAa,CAAC,CAAC;AAqG9C,gCAAU;AAnGZ,MAAM,iBAAiB,GAAG;IACxB,0BAAc,CAAC,0BAA0B;IACzC,0BAAc,CAAC,iBAAiB;IAChC,0BAAc,CAAC,+BAA+B;IAC9C,0BAAc,CAAC,6BAA6B;IAC5C,0BAAc,CAAC,cAAc;IAC7B,0BAAc,CAAC,iBAAiB;CACxB,CAAC;AACX,MAAM,cAAc,GAAG,IAAA,uBAAa,EAAC,iBAAiB,CAAC,CAAC;AA6FtD,wCAAc;AA3FhB,MAAM,wBAAwB,GAAG,IAAA,uBAAa,EAAC;IAC7C,GAAG,aAAa;IAChB,GAAG,iBAAiB;CACZ,CAAC,CAAC;AAuFV,4DAAwB;AArF1B,MAAM,gBAAgB,GAAG,IAAA,sBAAY,EAAC,0BAAc,CAAC,cAAc,CAAC,CAAC;AAkGnE,4CAAgB;AAhGlB,MAAM,mBAAmB,GAAG,IAAA,sBAAY,EAAC,0BAAc,CAAC,iBAAiB,CAAC,CAAC;AA+FzE,kDAAmB;AA7FrB,MAAM,oBAAoB,GAAG,IAAA,uBAAa,EAAC;IACzC,eAAe;IACf,0BAAc,CAAC,kBAAkB;IACjC,0BAAc,CAAC,kBAAkB;IACjC,0BAAc,CAAC,gBAAgB;IAC/B,0BAAc,CAAC,4BAA4B;IAC3C,0BAAc,CAAC,0BAA0B;IACzC,0BAAc,CAAC,6BAA6B;IAC5C,0BAAc,CAAC,gBAAgB;IAC/B,cAAc;IACd,0BAAc,CAAC,0BAA0B;IACzC,0BAAc,CAAC,+BAA+B;IAC9C,mCAAmC;IACnC,0BAAc,CAAC,iBAAiB;IAChC,0BAAc,CAAC,mBAAmB;CAC1B,CAAC,CAAC;AAgEV,oDAAoB;AA9DtB;;GAEG;AACH,MAAM,aAAa,GAAG,IAAA,oCAA0B,EAC9C,0BAAc,CAAC,gBAAgB,EAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,CACxB,CAAC;AAuDA,sCAAa;AArDf;;GAEG;AACH,SAAS,QAAQ,CACf,IAA+B;IAE/B,OAAO,CACL,CAAC,CAAC,IAAI;QACN,CAAC,IAAI,CAAC,IAAI,KAAK,0BAAc,CAAC,gBAAgB;YAC5C,IAAI,CAAC,IAAI,KAAK,0BAAc,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,IAAI,KAAK,KAAK,CACpB,CAAC;AACJ,CAAC;AAuDC,4BAAQ;AArDV,MAAM,YAAY,GAAG,IAAA,sBAAY,EAAC,0BAAc,CAAC,UAAU,CAAC,CAAC;AA4C3D,oCAAY;AA1Cd;;GAEG;AACH,MAAM,iBAAiB,GAAG,IAAA,sBAAY,EAAC,0BAAc,CAAC,eAAe,CAAC,CAAC;AAgCrE,8CAAiB;AA9BnB;;GAEG;AACH,MAAM,cAAc,GAAG,IAAA,qCAA2B,EAAC,2BAAe,CAAC,UAAU,EAAE;IAC7E,KAAK,EAAE,OAAO;CACf,CAAC,CAAC;AA0BD,wCAAc;AAxBhB;;GAEG;AACH,MAAM,aAAa,GAAG,IAAA,qCAA2B,EAAC,2BAAe,CAAC,UAAU,EAAE;IAC5E,KAAK,EAAE,MAAM;CACd,CAAC,CAAC;AAsCD,sCAAa;AApCf;;GAEG;AACH,MAAM,eAAe,GAAG,IAAA,qCAA2B,EAAC,2BAAe,CAAC,OAAO,EAAE;IAC3E,KAAK,EAAE,QAAQ;CAChB,CAAC,CAAC;AAmBD,0CAAe;AAjBjB,MAAM,MAAM,GAAG,IAAA,uBAAa,EAAC;IAC3B,0BAAc,CAAC,gBAAgB;IAC/B,0BAAc,CAAC,YAAY;IAC3B,0BAAc,CAAC,cAAc;IAC7B,0BAAc,CAAC,cAAc;IAC7B,0BAAc,CAAC,cAAc;CACrB,CAAC,CAAC;AAYV,wBAAM"}