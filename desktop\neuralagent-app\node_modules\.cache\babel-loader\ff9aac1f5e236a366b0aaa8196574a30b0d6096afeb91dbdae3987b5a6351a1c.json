{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\Elements\\\\TextFields\\\\index.js\";\nimport React from 'react';\nimport { LabeledTFContainer, VerticalLabeledTFContainer, TextFieldLabel, TextField, TextFieldError } from './Elements';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NATextField = ({\n  label = null,\n  verticalLabel = false,\n  fontSize = '16px',\n  labelFontWeight = '500',\n  error = null,\n  background = '#fff',\n  outlined = false,\n  padding = null,\n  borderRadius = null,\n  placeholder = null,\n  value = null,\n  isDarkMode = false,\n  type = 'text',\n  autoFocus = false,\n  onChange,\n  onFocus\n}) => {\n  if (label !== null) {\n    if (!verticalLabel) {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(LabeledTFContainer, {\n          children: [/*#__PURE__*/_jsxDEV(TextFieldLabel, {\n            fontSize: fontSize,\n            fontWeight: labelFontWeight,\n            isDarkMode: isDarkMode,\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1 1 75%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              background: background,\n              placeholder: placeholder,\n              padding: padding,\n              fontSize: fontSize,\n              borderRadius: borderRadius,\n              outlined: outlined,\n              value: value,\n              onFocus: onFocus,\n              onChange: onChange,\n              type: type,\n              autoFocus: autoFocus,\n              isDarkMode: isDarkMode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), error !== null ? /*#__PURE__*/_jsxDEV(TextFieldError, {\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false);\n    } else {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(VerticalLabeledTFContainer, {\n          children: [/*#__PURE__*/_jsxDEV(TextFieldLabel, {\n            verticalLabel: true,\n            fontSize: fontSize,\n            fontWeight: labelFontWeight,\n            isDarkMode: isDarkMode,\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            style: {\n              marginTop: '6px'\n            },\n            background: background,\n            placeholder: placeholder,\n            padding: padding,\n            fontSize: fontSize,\n            borderRadius: borderRadius,\n            outlined: outlined,\n            value: value,\n            onFocus: onFocus,\n            onChange: onChange,\n            type: type,\n            autoFocus: autoFocus,\n            isDarkMode: isDarkMode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), error !== null ? /*#__PURE__*/_jsxDEV(TextFieldError, {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(TextField, {\n      background: background,\n      placeholder: placeholder,\n      padding: padding,\n      fontSize: fontSize,\n      borderRadius: borderRadius,\n      outlined: outlined,\n      value: value,\n      onFocus: onFocus,\n      onChange: onChange,\n      type: type,\n      autoFocus: autoFocus,\n      isDarkMode: isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextFieldError, {\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = NATextField;\nexport default NATextField;\nvar _c;\n$RefreshReg$(_c, \"NATextField\");", "map": {"version": 3, "names": ["React", "LabeledTFContainer", "VerticalLabeledTFContainer", "TextFieldLabel", "TextField", "TextFieldError", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NATextField", "label", "verticalLabel", "fontSize", "labelFontWeight", "error", "background", "outlined", "padding", "borderRadius", "placeholder", "value", "isDarkMode", "type", "autoFocus", "onChange", "onFocus", "children", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "flex", "marginTop", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/TextFields/index.js"], "sourcesContent": ["import React from 'react';\nimport {\n  LabeledTFContainer,\n  VerticalLabeledTFContainer,\n  TextFieldLabel,\n  TextField,\n  TextFieldError\n} from './Elements';\n\nconst NATextField = ({\n  label=null,\n  verticalLabel=false,\n  fontSize='16px',\n  labelFontWeight='500',\n  error=null,\n  background='#fff',\n  outlined=false,\n  padding=null,\n  borderRadius=null,\n  placeholder=null,\n  value=null,\n  isDarkMode=false,\n  type='text',\n  autoFocus=false,\n  onChange,\n  onFocus\n}) => {\n  if (label !== null) {\n    if (!verticalLabel) {\n      return (\n        <>\n          <LabeledTFContainer>\n            <TextFieldLabel fontSize={fontSize} fontWeight={labelFontWeight} isDarkMode={isDarkMode}>\n              {label}\n            </TextFieldLabel>\n            <div style={{flex: '1 1 75%'}}>\n              <TextField background={background} placeholder={placeholder} padding={padding} fontSize={fontSize}\n                borderRadius={borderRadius} outlined={outlined} value={value} onFocus={onFocus} onChange={onChange}\n                type={type} autoFocus={autoFocus} isDarkMode={isDarkMode} />\n              {\n                error !== null ?\n                <TextFieldError>\n                  {error}\n                </TextFieldError> :\n                <></>\n              }\n            </div>\n          </LabeledTFContainer>\n        </>\n      );\n    } else {\n      return (\n        <>\n          <VerticalLabeledTFContainer>\n            <TextFieldLabel verticalLabel fontSize={fontSize} fontWeight={labelFontWeight} isDarkMode={isDarkMode}>\n              {label}\n            </TextFieldLabel>\n            <TextField style={{marginTop: '6px'}} background={background} placeholder={placeholder} padding={padding} fontSize={fontSize}\n              borderRadius={borderRadius} outlined={outlined} value={value} onFocus={onFocus} onChange={onChange} type={type} autoFocus={autoFocus}\n              isDarkMode={isDarkMode} />\n            {\n              error !== null ?\n              <TextFieldError>\n                {error}\n              </TextFieldError> :\n              <></>\n            }\n          </VerticalLabeledTFContainer>\n        </>\n      );\n    }\n  }\n  return (\n    <>\n      <TextField background={background} placeholder={placeholder} padding={padding} fontSize={fontSize}\n        borderRadius={borderRadius} outlined={outlined} value={value} onFocus={onFocus} onChange={onChange}\n        type={type} autoFocus={autoFocus} isDarkMode={isDarkMode} />\n      <TextFieldError>\n        {error}\n      </TextFieldError>\n    </>\n  );\n};\n\nexport default NATextField;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,kBAAkB,EAClBC,0BAA0B,EAC1BC,cAAc,EACdC,SAAS,EACTC,cAAc,QACT,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpB,MAAMC,WAAW,GAAGA,CAAC;EACnBC,KAAK,GAAC,IAAI;EACVC,aAAa,GAAC,KAAK;EACnBC,QAAQ,GAAC,MAAM;EACfC,eAAe,GAAC,KAAK;EACrBC,KAAK,GAAC,IAAI;EACVC,UAAU,GAAC,MAAM;EACjBC,QAAQ,GAAC,KAAK;EACdC,OAAO,GAAC,IAAI;EACZC,YAAY,GAAC,IAAI;EACjBC,WAAW,GAAC,IAAI;EAChBC,KAAK,GAAC,IAAI;EACVC,UAAU,GAAC,KAAK;EAChBC,IAAI,GAAC,MAAM;EACXC,SAAS,GAAC,KAAK;EACfC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,IAAIf,KAAK,KAAK,IAAI,EAAE;IAClB,IAAI,CAACC,aAAa,EAAE;MAClB,oBACEL,OAAA,CAAAE,SAAA;QAAAkB,QAAA,eACEpB,OAAA,CAACN,kBAAkB;UAAA0B,QAAA,gBACjBpB,OAAA,CAACJ,cAAc;YAACU,QAAQ,EAAEA,QAAS;YAACe,UAAU,EAAEd,eAAgB;YAACQ,UAAU,EAAEA,UAAW;YAAAK,QAAA,EACrFhB;UAAK;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACjBzB,OAAA;YAAK0B,KAAK,EAAE;cAACC,IAAI,EAAE;YAAS,CAAE;YAAAP,QAAA,gBAC5BpB,OAAA,CAACH,SAAS;cAACY,UAAU,EAAEA,UAAW;cAACI,WAAW,EAAEA,WAAY;cAACF,OAAO,EAAEA,OAAQ;cAACL,QAAQ,EAAEA,QAAS;cAChGM,YAAY,EAAEA,YAAa;cAACF,QAAQ,EAAEA,QAAS;cAACI,KAAK,EAAEA,KAAM;cAACK,OAAO,EAAEA,OAAQ;cAACD,QAAQ,EAAEA,QAAS;cACnGF,IAAI,EAAEA,IAAK;cAACC,SAAS,EAAEA,SAAU;cAACF,UAAU,EAAEA;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAE5DjB,KAAK,KAAK,IAAI,gBACdR,OAAA,CAACF,cAAc;cAAAsB,QAAA,EACZZ;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,gBACjBzB,OAAA,CAAAE,SAAA,mBAAI,CAAC;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC,gBACrB,CAAC;IAEP,CAAC,MAAM;MACL,oBACEzB,OAAA,CAAAE,SAAA;QAAAkB,QAAA,eACEpB,OAAA,CAACL,0BAA0B;UAAAyB,QAAA,gBACzBpB,OAAA,CAACJ,cAAc;YAACS,aAAa;YAACC,QAAQ,EAAEA,QAAS;YAACe,UAAU,EAAEd,eAAgB;YAACQ,UAAU,EAAEA,UAAW;YAAAK,QAAA,EACnGhB;UAAK;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACjBzB,OAAA,CAACH,SAAS;YAAC6B,KAAK,EAAE;cAACE,SAAS,EAAE;YAAK,CAAE;YAACnB,UAAU,EAAEA,UAAW;YAACI,WAAW,EAAEA,WAAY;YAACF,OAAO,EAAEA,OAAQ;YAACL,QAAQ,EAAEA,QAAS;YAC3HM,YAAY,EAAEA,YAAa;YAACF,QAAQ,EAAEA,QAAS;YAACI,KAAK,EAAEA,KAAM;YAACK,OAAO,EAAEA,OAAQ;YAACD,QAAQ,EAAEA,QAAS;YAACF,IAAI,EAAEA,IAAK;YAACC,SAAS,EAAEA,SAAU;YACrIF,UAAU,EAAEA;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAE1BjB,KAAK,KAAK,IAAI,gBACdR,OAAA,CAACF,cAAc;YAAAsB,QAAA,EACZZ;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,gBACjBzB,OAAA,CAAAE,SAAA,mBAAI,CAAC;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEmB;MAAC,gBAC7B,CAAC;IAEP;EACF;EACA,oBACEzB,OAAA,CAAAE,SAAA;IAAAkB,QAAA,gBACEpB,OAAA,CAACH,SAAS;MAACY,UAAU,EAAEA,UAAW;MAACI,WAAW,EAAEA,WAAY;MAACF,OAAO,EAAEA,OAAQ;MAACL,QAAQ,EAAEA,QAAS;MAChGM,YAAY,EAAEA,YAAa;MAACF,QAAQ,EAAEA,QAAS;MAACI,KAAK,EAAEA,KAAM;MAACK,OAAO,EAAEA,OAAQ;MAACD,QAAQ,EAAEA,QAAS;MACnGF,IAAI,EAAEA,IAAK;MAACC,SAAS,EAAEA,SAAU;MAACF,UAAU,EAAEA;IAAW;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9DzB,OAAA,CAACF,cAAc;MAAAsB,QAAA,EACZZ;IAAK;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA,eACjB,CAAC;AAEP,CAAC;AAACI,EAAA,GAzEI1B,WAAW;AA2EjB,eAAeA,WAAW;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}