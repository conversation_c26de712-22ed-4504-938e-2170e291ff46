import e from"postcss-selector-parser";const s=s=>{s=Object(s);const r=Boolean(!("preserve"in s)||s.preserve),t=String(s.replaceWith||".focus-visible"),o=e().astSync(t);return{postcssPlugin:"postcss-focus-visible",Rule(s,{result:t}){if(!s.selector.includes(":focus-visible"))return;let c;try{const r=e((e=>{e.walkPseudos((e=>{":focus-visible"===e.value&&(e.nodes&&e.nodes.length||e.replaceWith(o.clone({})))}))})).processSync(s.selector);c=String(r)}catch(e){return void s.warn(t,`Failed to parse selector : ${s.selector}`)}if(void 0===c)return;if(c===s.selector)return;const l=s.clone({selector:c});r?s.before(l):s.replaceWith(l)}}};s.postcss=!0;export{s as default};
