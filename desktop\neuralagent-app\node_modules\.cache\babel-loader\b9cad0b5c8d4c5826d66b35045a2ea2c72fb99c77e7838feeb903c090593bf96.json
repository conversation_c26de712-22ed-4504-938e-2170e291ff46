{"ast": null, "code": "import styled from 'styled-components';\nexport const Tag = styled.div`\n  display: inline-flex;\n  align-items: center;\n  padding: 4px 10px;\n  font-size: 13px;\n  font-weight: 500;\n  background-color: ${props => props.color ? props.color : 'var(--primary-color)'};\n  color: #fff;\n  border-radius: 50px;\n  gap: 6px;\n  user-select: none;\n`;", "map": {"version": 3, "names": ["styled", "Tag", "div", "props", "color"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/Tag.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Tag = styled.div`\n  display: inline-flex;\n  align-items: center;\n  padding: 4px 10px;\n  font-size: 13px;\n  font-weight: 500;\n  background-color: ${props => props.color ? props.color : 'var(--primary-color)'};\n  color: #fff;\n  border-radius: 50px;\n  gap: 6px;\n  user-select: none;\n`;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,GAAG,GAAGD,MAAM,CAACE,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAGD,KAAK,CAACC,KAAK,GAAG,sBAAsB;AACjF;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}