{"author": "<PERSON> <<EMAIL>> (http://feedic.com)", "name": "css-what", "description": "a CSS selector parser", "version": "3.4.2", "funding": "https://github.com/sponsors/fb55", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}}