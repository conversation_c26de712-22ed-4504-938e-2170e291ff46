{"version": 3, "names": ["_codeFrame", "require", "_index", "_index2", "_cache", "_modification", "_parser", "_t", "_context", "FUNCTION_TYPES", "arrowFunctionExpression", "assignmentExpression", "awaitExpression", "blockStatement", "buildUndefinedNode", "callExpression", "cloneNode", "conditionalExpression", "expressionStatement", "getBindingIdentifiers", "identifier", "inheritLeadingComments", "inheritTrailingComments", "inheritsComments", "isBlockStatement", "isEmptyStatement", "isExpression", "isExpressionStatement", "isIfStatement", "isProgram", "isStatement", "isVariableDeclaration", "removeComments", "returnStatement", "sequenceExpression", "validate", "yieldExpression", "replaceWithMultiple", "nodes", "_getCached<PERSON><PERSON>s", "resync", "call", "_verifyNodeList", "node", "length", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hub", "parent", "delete", "container", "key", "paths", "insertAfter", "requeue", "remove", "replaceWithSourceString", "replacement", "ast", "parse", "err", "loc", "message", "codeFrameColumns", "start", "line", "column", "code", "expressionAST", "program", "body", "expression", "traverse", "removeProperties", "replaceWith", "<PERSON><PERSON><PERSON>", "removed", "Error", "NodePath", "Array", "isArray", "nodePath", "isNodeType", "canHaveVariableDeclarationOrExpression", "canSwapBetweenExpressionAndStatement", "parentPath", "isExportDefaultDeclaration", "replaceExpressionWithStatements", "oldNode", "_replaceWith", "type", "setScope", "get", "_getCachedPaths2", "ReferenceError", "inList", "debug", "set", "declars", "nodesAsSingleExpression", "gatherSequenceExpressions", "id", "scope", "push", "functionParent", "getFunctionParent", "isParentAsync", "async", "isParentGenerator", "generator", "callee", "hoistVariables", "completionRecords", "getCompletionRecords", "path", "loop", "findParent", "isLoop", "uid", "getData", "generateDeclaredUidIdentifier", "pushContainer", "setData", "name", "arrowFunctionToExpression", "newCallee", "needToAwaitFunction", "hasType", "needToYieldFunction", "exprs", "ensureLastUndefined", "kind", "declar", "declarations", "bindings", "Object", "keys", "init", "consequent", "alternate", "test", "indexOf", "replaceInline", "_containerInsertAfter"], "sources": ["../../src/path/replacement.ts"], "sourcesContent": ["// This file contains methods responsible for replacing a node with another.\n\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"../index.ts\";\nimport NodePath from \"./index.ts\";\nimport { getCachedPaths } from \"../cache.ts\";\nimport { _verifyNodeList, _containerInsertAfter } from \"./modification.ts\";\nimport { parse } from \"@babel/parser\";\nimport {\n  FUNCTION_TYPES,\n  arrowFunctionExpression,\n  assignmentExpression,\n  awaitExpression,\n  blockStatement,\n  buildUndefinedNode,\n  callExpression,\n  cloneNode,\n  conditionalExpression,\n  expressionStatement,\n  getBindingIdentifiers,\n  identifier,\n  inheritLeadingComments,\n  inheritTrailingComments,\n  inheritsComments,\n  isBlockStatement,\n  isEmptyStatement,\n  isExpression,\n  isExpressionStatement,\n  isIfStatement,\n  isProgram,\n  isStatement,\n  isVariableDeclaration,\n  removeComments,\n  returnStatement,\n  sequenceExpression,\n  validate,\n  yieldExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { resync, setScope } from \"./context.ts\";\n\n/**\n * Replace a node with an array of multiple. This method performs the following steps:\n *\n *  - Inherit the comments of first provided node with that of the current node.\n *  - Insert the provided nodes after the current node.\n *  - Remove the current node.\n */\n\nexport function replaceWithMultiple(\n  this: NodePath,\n  nodes: t.Node | t.Node[],\n): NodePath[] {\n  resync.call(this);\n\n  nodes = _verifyNodeList.call(this, nodes);\n  inheritLeadingComments(nodes[0], this.node);\n  inheritTrailingComments(nodes[nodes.length - 1], this.node);\n  getCachedPaths(this.hub, this.parent)?.delete(this.node);\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key] = null;\n  const paths = this.insertAfter(nodes);\n\n  if (this.node) {\n    this.requeue();\n  } else {\n    this.remove();\n  }\n  return paths;\n}\n\n/**\n * Parse a string as an expression and replace the current node with the result.\n *\n * NOTE: This is typically not a good idea to use. Building source strings when\n * transforming ASTs is an antipattern and SHOULD NOT be encouraged. Even if it's\n * easier to use, your transforms will be extremely brittle.\n */\n\nexport function replaceWithSourceString(this: NodePath, replacement: string) {\n  resync.call(this);\n  let ast: t.File;\n\n  try {\n    replacement = `(${replacement})`;\n    ast = parse(replacement);\n  } catch (err) {\n    const loc = err.loc;\n    if (loc) {\n      err.message +=\n        \" - make sure this is an expression.\\n\" +\n        codeFrameColumns(replacement, {\n          start: {\n            line: loc.line,\n            column: loc.column + 1,\n          },\n        });\n      err.code = \"BABEL_REPLACE_SOURCE_ERROR\";\n    }\n    throw err;\n  }\n\n  const expressionAST = (ast.program.body[0] as t.ExpressionStatement)\n    .expression;\n  traverse.removeProperties(expressionAST);\n  return this.replaceWith(expressionAST);\n}\n\n/**\n * Replace the current node with another.\n */\nexport function replaceWith<R extends t.Node>(\n  this: NodePath,\n  replacementPath: R,\n): [NodePath<R>];\nexport function replaceWith<R extends NodePath>(\n  this: NodePath,\n  replacementPath: R,\n): [R];\nexport function replaceWith(\n  this: NodePath,\n  replacementPath: t.Node | NodePath,\n): [NodePath] {\n  resync.call(this);\n\n  if (this.removed) {\n    throw new Error(\"You can't replace this node, we've already removed it\");\n  }\n\n  let replacement: t.Node =\n    replacementPath instanceof NodePath\n      ? replacementPath.node\n      : replacementPath;\n\n  if (!replacement) {\n    throw new Error(\n      \"You passed `path.replaceWith()` a falsy node, use `path.remove()` instead\",\n    );\n  }\n\n  if (this.node === replacement) {\n    return [this];\n  }\n\n  if (this.isProgram() && !isProgram(replacement)) {\n    throw new Error(\n      \"You can only replace a Program root node with another Program node\",\n    );\n  }\n\n  if (Array.isArray(replacement)) {\n    throw new Error(\n      \"Don't use `path.replaceWith()` with an array of nodes, use `path.replaceWithMultiple()`\",\n    );\n  }\n\n  if (typeof replacement === \"string\") {\n    throw new Error(\n      \"Don't use `path.replaceWith()` with a source string, use `path.replaceWithSourceString()`\",\n    );\n  }\n\n  let nodePath = \"\";\n\n  if (this.isNodeType(\"Statement\") && isExpression(replacement)) {\n    if (\n      !this.canHaveVariableDeclarationOrExpression() &&\n      !this.canSwapBetweenExpressionAndStatement(replacement) &&\n      !this.parentPath.isExportDefaultDeclaration()\n    ) {\n      // replacing a statement with an expression so wrap it in an expression statement\n      replacement = expressionStatement(replacement);\n      nodePath = \"expression\";\n    }\n  }\n\n  if (this.isNodeType(\"Expression\") && isStatement(replacement)) {\n    if (\n      !this.canHaveVariableDeclarationOrExpression() &&\n      !this.canSwapBetweenExpressionAndStatement(replacement)\n    ) {\n      // replacing an expression with a statement so let's explode it\n      return this.replaceExpressionWithStatements([replacement]) as [NodePath];\n    }\n  }\n\n  const oldNode = this.node;\n  if (oldNode) {\n    inheritsComments(replacement, oldNode);\n    removeComments(oldNode);\n  }\n\n  // replace the node\n  _replaceWith.call(this, replacement);\n  this.type = replacement.type;\n\n  // potentially create new scope\n  setScope.call(this);\n\n  // requeue for visiting\n  this.requeue();\n\n  return [nodePath ? this.get(nodePath) : this];\n}\n\nexport function _replaceWith(this: NodePath, node: t.Node) {\n  if (!this.container) {\n    throw new ReferenceError(\"Container is falsy\");\n  }\n\n  if (this.inList) {\n    // @ts-expect-error todo(flow->ts): check if validate accepts a numeric key\n    validate(this.parent, this.key, [node]);\n  } else {\n    validate(this.parent, this.key as string, node);\n  }\n\n  this.debug(`Replace with ${node?.type}`);\n  getCachedPaths(this.hub, this.parent)?.set(node, this).delete(this.node);\n\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key] = node;\n}\n\n/**\n * This method takes an array of statements nodes and then explodes it\n * into expressions. This method retains completion records which is\n * extremely important to retain original semantics.\n */\n\nexport function replaceExpressionWithStatements(\n  this: NodePath,\n  nodes: Array<t.Statement>,\n) {\n  resync.call(this);\n\n  const declars: t.Identifier[] = [];\n  const nodesAsSingleExpression = gatherSequenceExpressions(nodes, declars);\n  if (nodesAsSingleExpression) {\n    for (const id of declars) this.scope.push({ id });\n    return this.replaceWith(nodesAsSingleExpression)[0].get(\"expressions\");\n  }\n\n  const functionParent = this.getFunctionParent();\n  const isParentAsync = functionParent?.node.async;\n  const isParentGenerator = functionParent?.node.generator;\n\n  const container = arrowFunctionExpression([], blockStatement(nodes));\n\n  this.replaceWith(callExpression(container, []));\n  // replaceWith changes the type of \"this\", but it isn't trackable by TS\n  type ThisType = NodePath<\n    t.CallExpression & {\n      callee: t.ArrowFunctionExpression & { body: t.BlockStatement };\n    }\n  >;\n\n  // hoist variable declaration in do block\n  // `(do { var x = 1; x;})` -> `var x; (() => { x = 1; return x; })()`\n  const callee = (this as ThisType).get(\"callee\");\n  callee.get(\"body\").scope.hoistVariables(id => this.scope.push({ id }));\n\n  // add implicit returns to all ending expression statements\n  const completionRecords: Array<NodePath> = callee.getCompletionRecords();\n  for (const path of completionRecords) {\n    if (!path.isExpressionStatement()) continue;\n\n    const loop = path.findParent(path => path.isLoop());\n    if (loop) {\n      let uid = loop.getData(\"expressionReplacementReturnUid\");\n\n      if (!uid) {\n        uid = callee.scope.generateDeclaredUidIdentifier(\"ret\");\n        callee\n          .get(\"body\")\n          .pushContainer(\"body\", returnStatement(cloneNode(uid)));\n        loop.setData(\"expressionReplacementReturnUid\", uid);\n      } else {\n        uid = identifier(uid.name);\n      }\n\n      path\n        .get(\"expression\")\n        .replaceWith(\n          assignmentExpression(\"=\", cloneNode(uid), path.node.expression),\n        );\n    } else {\n      path.replaceWith(returnStatement(path.node.expression));\n    }\n  }\n\n  // This is an IIFE, so we don't need to worry about the noNewArrows assumption\n  callee.arrowFunctionToExpression();\n  // Fixme: we can not `assert this is NodePath<t.FunctionExpression>` in `arrowFunctionToExpression`\n  // because it is not a class method known at compile time.\n  const newCallee = callee as unknown as NodePath<t.FunctionExpression>;\n\n  // (() => await xxx)() -> await (async () => await xxx)();\n  const needToAwaitFunction =\n    isParentAsync &&\n    traverse.hasType(\n      (this.get(\"callee.body\") as NodePath<t.BlockStatement>).node,\n      \"AwaitExpression\",\n      FUNCTION_TYPES,\n    );\n  const needToYieldFunction =\n    isParentGenerator &&\n    traverse.hasType(\n      (this.get(\"callee.body\") as NodePath<t.BlockStatement>).node,\n      \"YieldExpression\",\n      FUNCTION_TYPES,\n    );\n  if (needToAwaitFunction) {\n    newCallee.set(\"async\", true);\n    // yield* will await the generator return result\n    if (!needToYieldFunction) {\n      this.replaceWith(awaitExpression((this as ThisType).node));\n    }\n  }\n  if (needToYieldFunction) {\n    newCallee.set(\"generator\", true);\n    this.replaceWith(yieldExpression((this as ThisType).node, true));\n  }\n\n  return newCallee.get(\"body.body\");\n}\n\nfunction gatherSequenceExpressions(\n  nodes: ReadonlyArray<t.Node>,\n  declars: Array<t.Identifier>,\n) {\n  const exprs: t.Expression[] = [];\n  let ensureLastUndefined = true;\n\n  for (const node of nodes) {\n    // if we encounter emptyStatement before a non-emptyStatement\n    // we want to disregard that\n    if (!isEmptyStatement(node)) {\n      ensureLastUndefined = false;\n    }\n\n    if (isExpression(node)) {\n      exprs.push(node);\n    } else if (isExpressionStatement(node)) {\n      exprs.push(node.expression);\n    } else if (isVariableDeclaration(node)) {\n      if (node.kind !== \"var\") return; // bailed\n\n      for (const declar of node.declarations) {\n        const bindings = getBindingIdentifiers(declar);\n        for (const key of Object.keys(bindings)) {\n          declars.push(cloneNode(bindings[key]));\n        }\n\n        if (declar.init) {\n          exprs.push(assignmentExpression(\"=\", declar.id, declar.init));\n        }\n      }\n\n      ensureLastUndefined = true;\n    } else if (isIfStatement(node)) {\n      const consequent = node.consequent\n        ? gatherSequenceExpressions([node.consequent], declars)\n        : buildUndefinedNode();\n      const alternate = node.alternate\n        ? gatherSequenceExpressions([node.alternate], declars)\n        : buildUndefinedNode();\n      if (!consequent || !alternate) return; // bailed\n\n      exprs.push(conditionalExpression(node.test, consequent, alternate));\n    } else if (isBlockStatement(node)) {\n      const body = gatherSequenceExpressions(node.body, declars);\n      if (!body) return; // bailed\n\n      exprs.push(body);\n    } else if (isEmptyStatement(node)) {\n      // empty statement so ensure the last item is undefined if we're last\n      // checks if emptyStatement is first\n      if (nodes.indexOf(node) === 0) {\n        ensureLastUndefined = true;\n      }\n    } else {\n      // bailed, we can't turn this statement into an expression\n      return;\n    }\n  }\n\n  if (ensureLastUndefined) exprs.push(buildUndefinedNode());\n\n  if (exprs.length === 1) {\n    return exprs[0];\n  } else {\n    return sequenceExpression(exprs);\n  }\n}\n\nexport function replaceInline(this: NodePath, nodes: t.Node | Array<t.Node>) {\n  resync.call(this);\n\n  if (Array.isArray(nodes)) {\n    if (Array.isArray(this.container)) {\n      nodes = _verifyNodeList.call(this, nodes);\n      const paths = _containerInsertAfter.call(this, nodes);\n      this.remove();\n      return paths;\n    } else {\n      return this.replaceWithMultiple(nodes);\n    }\n  } else {\n    return this.replaceWith(nodes);\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,EAAA,GAAAN,OAAA;AA+BA,IAAAO,QAAA,GAAAP,OAAA;AAAgD;EA9B9CQ,cAAc;EACdC,uBAAuB;EACvBC,oBAAoB;EACpBC,eAAe;EACfC,cAAc;EACdC,kBAAkB;EAClBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,mBAAmB;EACnBC,qBAAqB;EACrBC,UAAU;EACVC,sBAAsB;EACtBC,uBAAuB;EACvBC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC,YAAY;EACZC,qBAAqB;EACrBC,aAAa;EACbC,SAAS;EACTC,WAAW;EACXC,qBAAqB;EACrBC,cAAc;EACdC,eAAe;EACfC,kBAAkB;EAClBC,QAAQ;EACRC;AAAe,IAAA7B,EAAA;AAaV,SAAS8B,mBAAmBA,CAEjCC,KAAwB,EACZ;EAAA,IAAAC,eAAA;EACZC,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjBH,KAAK,GAAGI,6BAAe,CAACD,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;EACzCjB,sBAAsB,CAACiB,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACK,IAAI,CAAC;EAC3CrB,uBAAuB,CAACgB,KAAK,CAACA,KAAK,CAACM,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAACD,IAAI,CAAC;EAC3D,CAAAJ,eAAA,OAAAM,qBAAc,EAAC,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,MAAM,CAAC,aAArCR,eAAA,CAAuCS,MAAM,CAAC,IAAI,CAACL,IAAI,CAAC;EACxD,IAAI,CAACA,IAAI,GAEP,IAAI,CAACM,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI;EACjC,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACd,KAAK,CAAC;EAErC,IAAI,IAAI,CAACK,IAAI,EAAE;IACb,IAAI,CAACU,OAAO,CAAC,CAAC;EAChB,CAAC,MAAM;IACL,IAAI,CAACC,MAAM,CAAC,CAAC;EACf;EACA,OAAOH,KAAK;AACd;AAUO,SAASI,uBAAuBA,CAAiBC,WAAmB,EAAE;EAC3EhB,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EACjB,IAAIgB,GAAW;EAEf,IAAI;IACFD,WAAW,GAAG,IAAIA,WAAW,GAAG;IAChCC,GAAG,GAAG,IAAAC,aAAK,EAACF,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,MAAMC,GAAG,GAAGD,GAAG,CAACC,GAAG;IACnB,IAAIA,GAAG,EAAE;MACPD,GAAG,CAACE,OAAO,IACT,uCAAuC,GACvC,IAAAC,2BAAgB,EAACN,WAAW,EAAE;QAC5BO,KAAK,EAAE;UACLC,IAAI,EAAEJ,GAAG,CAACI,IAAI;UACdC,MAAM,EAAEL,GAAG,CAACK,MAAM,GAAG;QACvB;MACF,CAAC,CAAC;MACJN,GAAG,CAACO,IAAI,GAAG,4BAA4B;IACzC;IACA,MAAMP,GAAG;EACX;EAEA,MAAMQ,aAAa,GAAIV,GAAG,CAACW,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CACvCC,UAAU;EACbC,cAAQ,CAACC,gBAAgB,CAACL,aAAa,CAAC;EACxC,OAAO,IAAI,CAACM,WAAW,CAACN,aAAa,CAAC;AACxC;AAaO,SAASM,WAAWA,CAEzBC,eAAkC,EACtB;EACZlC,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjB,IAAI,IAAI,CAACkC,OAAO,EAAE;IAChB,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAIpB,WAAmB,GACrBkB,eAAe,YAAYG,eAAQ,GAC/BH,eAAe,CAAC/B,IAAI,GACpB+B,eAAe;EAErB,IAAI,CAAClB,WAAW,EAAE;IAChB,MAAM,IAAIoB,KAAK,CACb,2EACF,CAAC;EACH;EAEA,IAAI,IAAI,CAACjC,IAAI,KAAKa,WAAW,EAAE;IAC7B,OAAO,CAAC,IAAI,CAAC;EACf;EAEA,IAAI,IAAI,CAAC3B,SAAS,CAAC,CAAC,IAAI,CAACA,SAAS,CAAC2B,WAAW,CAAC,EAAE;IAC/C,MAAM,IAAIoB,KAAK,CACb,oEACF,CAAC;EACH;EAEA,IAAIE,KAAK,CAACC,OAAO,CAACvB,WAAW,CAAC,EAAE;IAC9B,MAAM,IAAIoB,KAAK,CACb,yFACF,CAAC;EACH;EAEA,IAAI,OAAOpB,WAAW,KAAK,QAAQ,EAAE;IACnC,MAAM,IAAIoB,KAAK,CACb,2FACF,CAAC;EACH;EAEA,IAAII,QAAQ,GAAG,EAAE;EAEjB,IAAI,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,IAAIvD,YAAY,CAAC8B,WAAW,CAAC,EAAE;IAC7D,IACE,CAAC,IAAI,CAAC0B,sCAAsC,CAAC,CAAC,IAC9C,CAAC,IAAI,CAACC,oCAAoC,CAAC3B,WAAW,CAAC,IACvD,CAAC,IAAI,CAAC4B,UAAU,CAACC,0BAA0B,CAAC,CAAC,EAC7C;MAEA7B,WAAW,GAAGtC,mBAAmB,CAACsC,WAAW,CAAC;MAC9CwB,QAAQ,GAAG,YAAY;IACzB;EACF;EAEA,IAAI,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,IAAInD,WAAW,CAAC0B,WAAW,CAAC,EAAE;IAC7D,IACE,CAAC,IAAI,CAAC0B,sCAAsC,CAAC,CAAC,IAC9C,CAAC,IAAI,CAACC,oCAAoC,CAAC3B,WAAW,CAAC,EACvD;MAEA,OAAO,IAAI,CAAC8B,+BAA+B,CAAC,CAAC9B,WAAW,CAAC,CAAC;IAC5D;EACF;EAEA,MAAM+B,OAAO,GAAG,IAAI,CAAC5C,IAAI;EACzB,IAAI4C,OAAO,EAAE;IACXhE,gBAAgB,CAACiC,WAAW,EAAE+B,OAAO,CAAC;IACtCvD,cAAc,CAACuD,OAAO,CAAC;EACzB;EAGAC,YAAY,CAAC/C,IAAI,CAAC,IAAI,EAAEe,WAAW,CAAC;EACpC,IAAI,CAACiC,IAAI,GAAGjC,WAAW,CAACiC,IAAI;EAG5BC,iBAAQ,CAACjD,IAAI,CAAC,IAAI,CAAC;EAGnB,IAAI,CAACY,OAAO,CAAC,CAAC;EAEd,OAAO,CAAC2B,QAAQ,GAAG,IAAI,CAACW,GAAG,CAACX,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC/C;AAEO,SAASQ,YAAYA,CAAiB7C,IAAY,EAAE;EAAA,IAAAiD,gBAAA;EACzD,IAAI,CAAC,IAAI,CAAC3C,SAAS,EAAE;IACnB,MAAM,IAAI4C,cAAc,CAAC,oBAAoB,CAAC;EAChD;EAEA,IAAI,IAAI,CAACC,MAAM,EAAE;IAEf3D,QAAQ,CAAC,IAAI,CAACY,MAAM,EAAE,IAAI,CAACG,GAAG,EAAE,CAACP,IAAI,CAAC,CAAC;EACzC,CAAC,MAAM;IACLR,QAAQ,CAAC,IAAI,CAACY,MAAM,EAAE,IAAI,CAACG,GAAG,EAAYP,IAAI,CAAC;EACjD;EAEA,IAAI,CAACoD,KAAK,CAAC,gBAAgBpD,IAAI,oBAAJA,IAAI,CAAE8C,IAAI,EAAE,CAAC;EACxC,CAAAG,gBAAA,OAAA/C,qBAAc,EAAC,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,MAAM,CAAC,aAArC6C,gBAAA,CAAuCI,GAAG,CAACrD,IAAI,EAAE,IAAI,CAAC,CAACK,MAAM,CAAC,IAAI,CAACL,IAAI,CAAC;EAExE,IAAI,CAACA,IAAI,GAEP,IAAI,CAACM,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAGP,IAAI;AACnC;AAQO,SAAS2C,+BAA+BA,CAE7ChD,KAAyB,EACzB;EACAE,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjB,MAAMwD,OAAuB,GAAG,EAAE;EAClC,MAAMC,uBAAuB,GAAGC,yBAAyB,CAAC7D,KAAK,EAAE2D,OAAO,CAAC;EACzE,IAAIC,uBAAuB,EAAE;IAC3B,KAAK,MAAME,EAAE,IAAIH,OAAO,EAAE,IAAI,CAACI,KAAK,CAACC,IAAI,CAAC;MAAEF;IAAG,CAAC,CAAC;IACjD,OAAO,IAAI,CAAC3B,WAAW,CAACyB,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAACP,GAAG,CAAC,aAAa,CAAC;EACxE;EAEA,MAAMY,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC/C,MAAMC,aAAa,GAAGF,cAAc,oBAAdA,cAAc,CAAE5D,IAAI,CAAC+D,KAAK;EAChD,MAAMC,iBAAiB,GAAGJ,cAAc,oBAAdA,cAAc,CAAE5D,IAAI,CAACiE,SAAS;EAExD,MAAM3D,SAAS,GAAGvC,uBAAuB,CAAC,EAAE,EAAEG,cAAc,CAACyB,KAAK,CAAC,CAAC;EAEpE,IAAI,CAACmC,WAAW,CAAC1D,cAAc,CAACkC,SAAS,EAAE,EAAE,CAAC,CAAC;EAU/C,MAAM4D,MAAM,GAAI,IAAI,CAAclB,GAAG,CAAC,QAAQ,CAAC;EAC/CkB,MAAM,CAAClB,GAAG,CAAC,MAAM,CAAC,CAACU,KAAK,CAACS,cAAc,CAACV,EAAE,IAAI,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;IAAEF;EAAG,CAAC,CAAC,CAAC;EAGtE,MAAMW,iBAAkC,GAAGF,MAAM,CAACG,oBAAoB,CAAC,CAAC;EACxE,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,EAAE;IACpC,IAAI,CAACE,IAAI,CAACtF,qBAAqB,CAAC,CAAC,EAAE;IAEnC,MAAMuF,IAAI,GAAGD,IAAI,CAACE,UAAU,CAACF,IAAI,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC;IACnD,IAAIF,IAAI,EAAE;MACR,IAAIG,GAAG,GAAGH,IAAI,CAACI,OAAO,CAAC,gCAAgC,CAAC;MAExD,IAAI,CAACD,GAAG,EAAE;QACRA,GAAG,GAAGR,MAAM,CAACR,KAAK,CAACkB,6BAA6B,CAAC,KAAK,CAAC;QACvDV,MAAM,CACHlB,GAAG,CAAC,MAAM,CAAC,CACX6B,aAAa,CAAC,MAAM,EAAEvF,eAAe,CAACjB,SAAS,CAACqG,GAAG,CAAC,CAAC,CAAC;QACzDH,IAAI,CAACO,OAAO,CAAC,gCAAgC,EAAEJ,GAAG,CAAC;MACrD,CAAC,MAAM;QACLA,GAAG,GAAGjG,UAAU,CAACiG,GAAG,CAACK,IAAI,CAAC;MAC5B;MAEAT,IAAI,CACDtB,GAAG,CAAC,YAAY,CAAC,CACjBlB,WAAW,CACV9D,oBAAoB,CAAC,GAAG,EAAEK,SAAS,CAACqG,GAAG,CAAC,EAAEJ,IAAI,CAACtE,IAAI,CAAC2B,UAAU,CAChE,CAAC;IACL,CAAC,MAAM;MACL2C,IAAI,CAACxC,WAAW,CAACxC,eAAe,CAACgF,IAAI,CAACtE,IAAI,CAAC2B,UAAU,CAAC,CAAC;IACzD;EACF;EAGAuC,MAAM,CAACc,yBAAyB,CAAC,CAAC;EAGlC,MAAMC,SAAS,GAAGf,MAAmD;EAGrE,MAAMgB,mBAAmB,GACvBpB,aAAa,IACblC,cAAQ,CAACuD,OAAO,CACb,IAAI,CAACnC,GAAG,CAAC,aAAa,CAAC,CAAgChD,IAAI,EAC5D,iBAAiB,EACjBlC,cACF,CAAC;EACH,MAAMsH,mBAAmB,GACvBpB,iBAAiB,IACjBpC,cAAQ,CAACuD,OAAO,CACb,IAAI,CAACnC,GAAG,CAAC,aAAa,CAAC,CAAgChD,IAAI,EAC5D,iBAAiB,EACjBlC,cACF,CAAC;EACH,IAAIoH,mBAAmB,EAAE;IACvBD,SAAS,CAAC5B,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;IAE5B,IAAI,CAAC+B,mBAAmB,EAAE;MACxB,IAAI,CAACtD,WAAW,CAAC7D,eAAe,CAAE,IAAI,CAAc+B,IAAI,CAAC,CAAC;IAC5D;EACF;EACA,IAAIoF,mBAAmB,EAAE;IACvBH,SAAS,CAAC5B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;IAChC,IAAI,CAACvB,WAAW,CAACrC,eAAe,CAAE,IAAI,CAAcO,IAAI,EAAE,IAAI,CAAC,CAAC;EAClE;EAEA,OAAOiF,SAAS,CAACjC,GAAG,CAAC,WAAW,CAAC;AACnC;AAEA,SAASQ,yBAAyBA,CAChC7D,KAA4B,EAC5B2D,OAA4B,EAC5B;EACA,MAAM+B,KAAqB,GAAG,EAAE;EAChC,IAAIC,mBAAmB,GAAG,IAAI;EAE9B,KAAK,MAAMtF,IAAI,IAAIL,KAAK,EAAE;IAGxB,IAAI,CAACb,gBAAgB,CAACkB,IAAI,CAAC,EAAE;MAC3BsF,mBAAmB,GAAG,KAAK;IAC7B;IAEA,IAAIvG,YAAY,CAACiB,IAAI,CAAC,EAAE;MACtBqF,KAAK,CAAC1B,IAAI,CAAC3D,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIhB,qBAAqB,CAACgB,IAAI,CAAC,EAAE;MACtCqF,KAAK,CAAC1B,IAAI,CAAC3D,IAAI,CAAC2B,UAAU,CAAC;IAC7B,CAAC,MAAM,IAAIvC,qBAAqB,CAACY,IAAI,CAAC,EAAE;MACtC,IAAIA,IAAI,CAACuF,IAAI,KAAK,KAAK,EAAE;MAEzB,KAAK,MAAMC,MAAM,IAAIxF,IAAI,CAACyF,YAAY,EAAE;QACtC,MAAMC,QAAQ,GAAGlH,qBAAqB,CAACgH,MAAM,CAAC;QAC9C,KAAK,MAAMjF,GAAG,IAAIoF,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAE;UACvCpC,OAAO,CAACK,IAAI,CAACtF,SAAS,CAACqH,QAAQ,CAACnF,GAAG,CAAC,CAAC,CAAC;QACxC;QAEA,IAAIiF,MAAM,CAACK,IAAI,EAAE;UACfR,KAAK,CAAC1B,IAAI,CAAC3F,oBAAoB,CAAC,GAAG,EAAEwH,MAAM,CAAC/B,EAAE,EAAE+B,MAAM,CAACK,IAAI,CAAC,CAAC;QAC/D;MACF;MAEAP,mBAAmB,GAAG,IAAI;IAC5B,CAAC,MAAM,IAAIrG,aAAa,CAACe,IAAI,CAAC,EAAE;MAC9B,MAAM8F,UAAU,GAAG9F,IAAI,CAAC8F,UAAU,GAC9BtC,yBAAyB,CAAC,CAACxD,IAAI,CAAC8F,UAAU,CAAC,EAAExC,OAAO,CAAC,GACrDnF,kBAAkB,CAAC,CAAC;MACxB,MAAM4H,SAAS,GAAG/F,IAAI,CAAC+F,SAAS,GAC5BvC,yBAAyB,CAAC,CAACxD,IAAI,CAAC+F,SAAS,CAAC,EAAEzC,OAAO,CAAC,GACpDnF,kBAAkB,CAAC,CAAC;MACxB,IAAI,CAAC2H,UAAU,IAAI,CAACC,SAAS,EAAE;MAE/BV,KAAK,CAAC1B,IAAI,CAACrF,qBAAqB,CAAC0B,IAAI,CAACgG,IAAI,EAAEF,UAAU,EAAEC,SAAS,CAAC,CAAC;IACrE,CAAC,MAAM,IAAIlH,gBAAgB,CAACmB,IAAI,CAAC,EAAE;MACjC,MAAM0B,IAAI,GAAG8B,yBAAyB,CAACxD,IAAI,CAAC0B,IAAI,EAAE4B,OAAO,CAAC;MAC1D,IAAI,CAAC5B,IAAI,EAAE;MAEX2D,KAAK,CAAC1B,IAAI,CAACjC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAI5C,gBAAgB,CAACkB,IAAI,CAAC,EAAE;MAGjC,IAAIL,KAAK,CAACsG,OAAO,CAACjG,IAAI,CAAC,KAAK,CAAC,EAAE;QAC7BsF,mBAAmB,GAAG,IAAI;MAC5B;IACF,CAAC,MAAM;MAEL;IACF;EACF;EAEA,IAAIA,mBAAmB,EAAED,KAAK,CAAC1B,IAAI,CAACxF,kBAAkB,CAAC,CAAC,CAAC;EAEzD,IAAIkH,KAAK,CAACpF,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOoF,KAAK,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM;IACL,OAAO9F,kBAAkB,CAAC8F,KAAK,CAAC;EAClC;AACF;AAEO,SAASa,aAAaA,CAAiBvG,KAA6B,EAAE;EAC3EE,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjB,IAAIqC,KAAK,CAACC,OAAO,CAACzC,KAAK,CAAC,EAAE;IACxB,IAAIwC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,SAAS,CAAC,EAAE;MACjCX,KAAK,GAAGI,6BAAe,CAACD,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;MACzC,MAAMa,KAAK,GAAG2F,mCAAqB,CAACrG,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;MACrD,IAAI,CAACgB,MAAM,CAAC,CAAC;MACb,OAAOH,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI,CAACd,mBAAmB,CAACC,KAAK,CAAC;IACxC;EACF,CAAC,MAAM;IACL,OAAO,IAAI,CAACmC,WAAW,CAACnC,KAAK,CAAC;EAChC;AACF", "ignoreList": []}