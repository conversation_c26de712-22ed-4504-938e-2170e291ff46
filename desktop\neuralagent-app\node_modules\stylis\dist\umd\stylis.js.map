{"version": 3, "file": "stylis.js", "sources": ["../../src/Enum.js", "../../src/Utility.js", "../../src/Tokenizer.js", "../../src/Parser.js", "../../src/Prefixer.js", "../../src/Serializer.js", "../../src/Middleware.js"], "sourcesContent": ["export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "import {MS, MO<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from './Enum.js'\nimport {hash, charat, strlen, indexof, replace, substr, match} from './Utility.js'\n\n/**\n * @param {string} value\n * @param {number} length\n * @param {object[]} children\n * @return {string}\n */\nexport function prefix (value, length, children) {\n\tswitch (hash(value, length)) {\n\t\t// color-adjust\n\t\tcase 5103:\n\t\t\treturn WEBKIT + 'print-' + value + value\n\t\t// animation, animation-(delay|direction|duration|fill-mode|iteration-count|name|play-state|timing-function)\n\t\tcase 5737: case 4201: case 3177: case 3433: case 1641: case 4457: case 2921:\n\t\t// text-decoration, filter, clip-path, backface-visibility, column, box-decoration-break\n\t\tcase 5572: case 6356: case 5844: case 3191: case 6645: case 3005:\n\t\t// mask, mask-image, mask-(mode|clip|size), mask-(repeat|origin), mask-position, mask-composite,\n\t\tcase 6391: case 5879: case 5623: case 6135: case 4599: case 4855:\n\t\t// background-clip, columns, column-(count|fill|gap|rule|rule-color|rule-style|rule-width|span|width)\n\t\tcase 4215: case 6389: case 5109: case 5365: case 5621: case 3829:\n\t\t\treturn WEBKIT + value + value\n\t\t// tab-size\n\t\tcase 4789:\n\t\t\treturn MOZ + value + value\n\t\t// appearance, user-select, transform, hyphens, text-size-adjust\n\t\tcase 5349: case 4246: case 4810: case 6968: case 2756:\n\t\t\treturn WEBKIT + value + MOZ + value + MS + value + value\n\t\t// writing-mode\n\t\tcase 5936:\n\t\t\tswitch (charat(value, length + 11)) {\n\t\t\t\t// vertical-l(r)\n\t\t\t\tcase 114:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb') + value\n\t\t\t\t// vertical-r(l)\n\t\t\t\tcase 108:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb-rl') + value\n\t\t\t\t// horizontal(-)tb\n\t\t\t\tcase 45:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'lr') + value\n\t\t\t\t// default: fallthrough to below\n\t\t\t}\n\t\t// flex, flex-direction, scroll-snap-type, writing-mode\n\t\tcase 6828: case 4268: case 2903:\n\t\t\treturn WEBKIT + value + MS + value + value\n\t\t// order\n\t\tcase 6165:\n\t\t\treturn WEBKIT + value + MS + 'flex-' + value + value\n\t\t// align-items\n\t\tcase 5187:\n\t\t\treturn WEBKIT + value + replace(value, /(\\w+).+(:[^]+)/, WEBKIT + 'box-$1$2' + MS + 'flex-$1$2') + value\n\t\t// align-self\n\t\tcase 5443:\n\t\t\treturn WEBKIT + value + MS + 'flex-item-' + replace(value, /flex-|-self/g, '') + (!match(value, /flex-|baseline/) ? MS + 'grid-row-' + replace(value, /flex-|-self/g, '') : '') + value\n\t\t// align-content\n\t\tcase 4675:\n\t\t\treturn WEBKIT + value + MS + 'flex-line-pack' + replace(value, /align-content|flex-|-self/g, '') + value\n\t\t// flex-shrink\n\t\tcase 5548:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'shrink', 'negative') + value\n\t\t// flex-basis\n\t\tcase 5292:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'basis', 'preferred-size') + value\n\t\t// flex-grow\n\t\tcase 6060:\n\t\t\treturn WEBKIT + 'box-' + replace(value, '-grow', '') + WEBKIT + value + MS + replace(value, 'grow', 'positive') + value\n\t\t// transition\n\t\tcase 4554:\n\t\t\treturn WEBKIT + replace(value, /([^-])(transform)/g, '$1' + WEBKIT + '$2') + value\n\t\t// cursor\n\t\tcase 6187:\n\t\t\treturn replace(replace(replace(value, /(zoom-|grab)/, WEBKIT + '$1'), /(image-set)/, WEBKIT + '$1'), value, '') + value\n\t\t// background, background-image\n\t\tcase 5495: case 3959:\n\t\t\treturn replace(value, /(image-set\\([^]*)/, WEBKIT + '$1' + '$`$1')\n\t\t// justify-content\n\t\tcase 4968:\n\t\t\treturn replace(replace(value, /(.+:)(flex-)?(.*)/, WEBKIT + 'box-pack:$3' + MS + 'flex-pack:$3'), /s.+-b[^;]+/, 'justify') + WEBKIT + value + value\n\t\t// justify-self\n\t\tcase 4200:\n\t\t\tif (!match(value, /flex-|baseline/)) return MS + 'grid-column-align' + substr(value, length) + value\n\t\t\tbreak\n\t\t// grid-template-(columns|rows)\n\t\tcase 2592: case 3360:\n\t\t\treturn MS + replace(value, 'template-', '') + value\n\t\t// grid-(row|column)-start\n\t\tcase 4384: case 3616:\n\t\t\tif (children && children.some(function (element, index) { return length = index, match(element.props, /grid-\\w+-end/) })) {\n\t\t\t\treturn ~indexof(value + (children = children[length].value), 'span', 0) ? value : (MS + replace(value, '-start', '') + value + MS + 'grid-row-span:' + (~indexof(children, 'span', 0) ? match(children, /\\d+/) : +match(children, /\\d+/) - +match(value, /\\d+/)) + ';')\n\t\t\t}\n\t\t\treturn MS + replace(value, '-start', '') + value\n\t\t// grid-(row|column)-end\n\t\tcase 4896: case 4128:\n\t\t\treturn (children && children.some(function (element) { return match(element.props, /grid-\\w+-start/) })) ? value : MS + replace(replace(value, '-end', '-span'), 'span ', '') + value\n\t\t// (margin|padding)-inline-(start|end)\n\t\tcase 4095: case 3583: case 4068: case 2532:\n\t\t\treturn replace(value, /(.+)-inline(.+)/, WEBKIT + '$1$2') + value\n\t\t// (min|max)?(width|height|inline-size|block-size)\n\t\tcase 8116: case 7059: case 5753: case 5535:\n\t\tcase 5445: case 5701: case 4933: case 4677:\n\t\tcase 5533: case 5789: case 5021: case 4765:\n\t\t\t// stretch, max-content, min-content, fill-available\n\t\t\tif (strlen(value) - 1 - length > 6)\n\t\t\t\tswitch (charat(value, length + 1)) {\n\t\t\t\t\t// (m)ax-content, (m)in-content\n\t\t\t\t\tcase 109:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (charat(value, length + 4) !== 45)\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t// (f)ill-available, (f)it-content\n\t\t\t\t\tcase 102:\n\t\t\t\t\t\treturn replace(value, /(.+:)(.+)-([^]+)/, '$1' + WEBKIT + '$2-$3' + '$1' + MOZ + (charat(value, length + 3) == 108 ? '$3' : '$2-$3')) + value\n\t\t\t\t\t// (s)tretch\n\t\t\t\t\tcase 115:\n\t\t\t\t\t\treturn ~indexof(value, 'stretch', 0) ? prefix(replace(value, 'stretch', 'fill-available'), length, children) + value : value\n\t\t\t\t}\n\t\t\tbreak\n\t\t// grid-(column|row)\n\t\tcase 5152: case 5920:\n\t\t\treturn replace(value, /(.+?):(\\d+)(\\s*\\/\\s*(span)?\\s*(\\d+))?(.*)/, function (_, a, b, c, d, e, f) { return (MS + a + ':' + b + f) + (c ? (MS + a + '-span:' + (d ? e : +e - +b)) + f : '') + value })\n\t\t// position: sticky\n\t\tcase 4949:\n\t\t\t// stick(y)?\n\t\t\tif (charat(value, length + 6) === 121)\n\t\t\t\treturn replace(value, ':', ':' + WEBKIT) + value\n\t\t\tbreak\n\t\t// display: (flex|inline-flex|grid|inline-grid)\n\t\tcase 6444:\n\t\t\tswitch (charat(value, charat(value, 14) === 45 ? 18 : 11)) {\n\t\t\t\t// (inline-)?fle(x)\n\t\t\t\tcase 120:\n\t\t\t\t\treturn replace(value, /(.+:)([^;\\s!]+)(;|(\\s+)?!.+)?/, '$1' + WEBKIT + (charat(value, 14) === 45 ? 'inline-' : '') + 'box$3' + '$1' + WEBKIT + '$2$3' + '$1' + MS + '$2box$3') + value\n\t\t\t\t// (inline-)?gri(d)\n\t\t\t\tcase 100:\n\t\t\t\t\treturn replace(value, ':', ':' + MS) + value\n\t\t\t}\n\t\t\tbreak\n\t\t// scroll-margin, scroll-margin-(top|right|bottom|left)\n\t\tcase 5719: case 2647: case 2135: case 3927: case 2391:\n\t\t\treturn replace(value, 'scroll-', 'scroll-snap-') + value\n\t}\n\n\treturn value\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n", "import {MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION} from './Enum.js'\nimport {match, charat, substr, strlen, sizeof, replace, combine, filter, assign} from './Utility.js'\nimport {copy, lift, tokenize} from './Tokenizer.js'\nimport {serialize} from './Serializer.js'\nimport {prefix} from './Prefixer.js'\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware (collection) {\n\tvar length = sizeof(collection)\n\n\treturn function (element, index, children, callback) {\n\t\tvar output = ''\n\n\t\tfor (var i = 0; i < length; i++)\n\t\t\toutput += collection[i](element, index, children, callback) || ''\n\n\t\treturn output\n\t}\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet (callback) {\n\treturn function (element) {\n\t\tif (!element.root)\n\t\t\tif (element = element.return)\n\t\t\t\tcallback(element)\n\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer (element, index, children, callback) {\n\tif (element.length > -1)\n\t\tif (!element.return)\n\t\t\tswitch (element.type) {\n\t\t\t\tcase DECLARATION: element.return = prefix(element.value, element.length, children)\n\t\t\t\t\treturn\n\t\t\t\tcase KEYFRAMES:\n\t\t\t\t\treturn serialize([copy(element, {value: replace(element.value, '@', '@' + WEBKIT)})], callback)\n\t\t\t\tcase RULESET:\n\t\t\t\t\tif (element.length)\n\t\t\t\t\t\treturn combine(children = element.props, function (value) {\n\t\t\t\t\t\t\tswitch (match(value, callback = /(::plac\\w+|:read-\\w+)/)) {\n\t\t\t\t\t\t\t\t// :read-(only|write)\n\t\t\t\t\t\t\t\tcase ':read-only': case ':read-write':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t// :placeholder\n\t\t\t\t\t\t\t\tcase '::placeholder':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treturn ''\n\t\t\t\t\t\t})\n\t\t\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace (element) {\n\tswitch (element.type) {\n\t\tcase RULESET:\n\t\t\telement.props = element.props.map(function (value) {\n\t\t\t\treturn combine(tokenize(value), function (value, index, children) {\n\t\t\t\t\tswitch (charat(value, 0)) {\n\t\t\t\t\t\t// \\f\n\t\t\t\t\t\tcase 12:\n\t\t\t\t\t\t\treturn substr(value, 1, strlen(value))\n\t\t\t\t\t\t// \\0 ( + > ~\n\t\t\t\t\t\tcase 0: case 40: case 43: case 62: case 126:\n\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t// :\n\t\t\t\t\t\tcase 58:\n\t\t\t\t\t\t\tif (children[++index] === 'global')\n\t\t\t\t\t\t\t\tchildren[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1)\n\t\t\t\t\t\t// \\s\n\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\treturn index === 1 ? '' : value\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tswitch (index) {\n\t\t\t\t\t\t\t\tcase 0: element = value\n\t\t\t\t\t\t\t\t\treturn sizeof(children) > 1 ? '' : value\n\t\t\t\t\t\t\t\tcase index = sizeof(children) - 1: case 2:\n\t\t\t\t\t\t\t\t\treturn index === 2 ? value + element + element : value + element\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t}\n}\n"], "names": ["MS", "MOZ", "WEBKIT", "COMMENT", "RULESET", "DECLARATION", "PAGE", "MEDIA", "IMPORT", "CHARSET", "VIEWPORT", "SUPPORTS", "DOCUMENT", "NAMESPACE", "KEYFRAMES", "FONT_FACE", "COUNTER_STYLE", "FONT_FEATURE_VALUES", "LAYER", "SCOPE", "abs", "Math", "from", "String", "fromCharCode", "assign", "Object", "hash", "value", "length", "charat", "trim", "match", "pattern", "exec", "replace", "replacement", "indexof", "search", "position", "indexOf", "index", "charCodeAt", "substr", "begin", "end", "slice", "strlen", "sizeof", "append", "array", "push", "combine", "callback", "map", "join", "filter", "node", "root", "parent", "type", "props", "children", "siblings", "line", "column", "return", "copy", "lift", "char", "character", "prev", "characters", "next", "peek", "caret", "token", "alloc", "dealloc", "delimit", "delimiter", "tokenize", "tokenizer", "whitespace", "identifier", "escaping", "count", "commenter", "compile", "parse", "rule", "rules", "rulesets", "pseudo", "points", "declarations", "offset", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "reference", "comment", "declaration", "ruleset", "post", "size", "i", "j", "k", "x", "y", "z", "prefix", "some", "element", "_", "a", "b", "c", "d", "e", "f", "serialize", "output", "stringify", "middleware", "collection", "rulesheet", "prefixer", "namespace"], "mappings": "8MAAWA,EAAK,WACLC,EAAM,YACNC,EAAS,eAETC,EAAU,WACVC,EAAU,WACVC,EAAc,WAEdC,EAAO,YACPC,EAAQ,aACRC,EAAS,cACTC,EAAU,eACVC,EAAW,gBACXC,EAAW,gBACXC,EAAW,gBACXC,EAAY,iBACZC,EAAY,iBACZC,EAAY,iBACZC,EAAgB,qBAChBC,EAAsB,2BACtBC,EAAQ,aACRC,EAAQ,aCjBRC,EAAMC,KAAKD,QAMXE,EAAOC,OAAOC,iBAMdC,EAASC,OAAOD,OAOpB,SAASE,EAAMC,EAAOC,GAC5B,OAAOC,EAAOF,EAAO,GAAK,MAAYC,GAAU,EAAKC,EAAOF,EAAO,KAAO,EAAKE,EAAOF,EAAO,KAAO,EAAKE,EAAOF,EAAO,KAAO,EAAKE,EAAOF,EAAO,GAAK,EAOhJ,SAASG,EAAMH,GACrB,OAAOA,EAAMG,OAQP,SAASC,EAAOJ,EAAOK,GAC7B,OAAQL,EAAQK,EAAQC,KAAKN,IAAUA,EAAM,GAAKA,EAS5C,SAASO,EAASP,EAAOK,EAASG,GACxC,OAAOR,EAAMO,QAAQF,EAASG,GASxB,SAASC,EAAST,EAAOU,EAAQC,GACvC,OAAOX,EAAMY,QAAQF,EAAQC,GAQvB,SAAST,EAAQF,EAAOa,GAC9B,OAAOb,EAAMc,WAAWD,GAAS,EAS3B,SAASE,EAAQf,EAAOgB,EAAOC,GACrC,OAAOjB,EAAMkB,MAAMF,EAAOC,GAOpB,SAASE,EAAQnB,GACvB,OAAOA,EAAMC,OAOP,SAASmB,EAAQpB,GACvB,OAAOA,EAAMC,OAQP,SAASoB,EAAQrB,EAAOsB,GAC9B,OAAOA,EAAMC,KAAKvB,GAAQA,EAQpB,SAASwB,EAASF,EAAOG,GAC/B,OAAOH,EAAMI,IAAID,GAAUE,KAAK,IAQ1B,SAASC,EAAQN,EAAOjB,GAC9B,OAAOiB,EAAMM,QAAO,SAAU5B,GAAS,OAAQI,EAAMJ,EAAOK,aCzH3C,WACE,WACA,aACE,cACC,eACC,GAYjB,SAASwB,EAAM7B,EAAO8B,EAAMC,EAAQC,EAAMC,EAAOC,EAAUjC,EAAQkC,GACzE,MAAO,CAACnC,MAAOA,EAAO8B,KAAMA,EAAMC,OAAQA,EAAQC,KAAMA,EAAMC,MAAOA,EAAOC,SAAUA,EAAUE,KAAMA,OAAMC,OAAQA,SAAQpC,OAAQA,EAAQqC,OAAQ,GAAIH,SAAUA,GAQ5J,SAASI,EAAMT,EAAMG,GAC3B,OAAOpC,EAAOgC,EAAK,GAAI,KAAM,KAAM,GAAI,KAAM,KAAM,EAAGC,EAAKK,UAAWL,EAAM,CAAC7B,QAAS6B,EAAK7B,QAASgC,GAM9F,SAASO,EAAMV,GACrB,MAAOA,EAAKA,KACXA,EAAOS,EAAKT,EAAKA,KAAM,CAACI,SAAU,CAACJ,KAEpCT,EAAOS,EAAMA,EAAKK,UAMZ,SAASM,IACf,OAAOC,YAMD,SAASC,IACfD,YAAY/B,WAAW,EAAIT,EAAO0C,eAAcjC,YAAY,EAE5D,GAAI0B,WAAUK,cAAc,GAC3BL,SAAS,EAAGD,SAEb,OAAOM,YAMD,SAASG,IACfH,YAAY/B,WAAWV,SAASC,EAAO0C,aAAYjC,cAAc,EAEjE,GAAI0B,WAAUK,cAAc,GAC3BL,SAAS,EAAGD,SAEb,OAAOM,YAMD,SAASI,IACf,OAAO5C,EAAO0C,aAAYjC,YAMpB,SAASoC,IACf,OAAOpC,WAQD,SAASO,EAAOF,EAAOC,GAC7B,OAAOF,EAAO6B,aAAY5B,EAAOC,GAO3B,SAAS+B,EAAOhB,GACtB,OAAQA,GAEP,KAAK,EAAG,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GACtC,OAAO,EAER,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,IAE3D,KAAK,GAAI,KAAK,IAAK,KAAK,IACvB,OAAO,EAER,KAAK,GACJ,OAAO,EAER,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAC/B,OAAO,EAER,KAAK,GAAI,KAAK,GACb,OAAO,EAGT,OAAO,EAOD,SAASiB,EAAOjD,GACtB,OAAOoC,OAAOC,SAAS,EAAGpC,SAASkB,EAAOyB,aAAa5C,GAAQW,WAAW,EAAG,GAOvE,SAASuC,EAASlD,GACxB,OAAO4C,aAAa,GAAI5C,EAOlB,SAASmD,EAASnB,GACxB,OAAO7B,EAAKe,EAAMP,WAAW,EAAGyC,EAAUpB,IAAS,GAAKA,EAAO,EAAIA,IAAS,GAAKA,EAAO,EAAIA,KAOtF,SAASqB,EAAUrD,GACzB,OAAOkD,EAAQI,EAAUL,EAAMjD,KAOzB,SAASuD,EAAYvB,GAC3B,MAAOU,YAAYI,IAClB,GAAIJ,YAAY,GACfG,SAEA,MAEF,OAAOG,EAAMhB,GAAQ,GAAKgB,EAAMN,aAAa,EAAI,GAAK,IAOhD,SAASY,EAAWpB,GAC1B,MAAOW,IACN,OAAQG,EAAMN,cACb,KAAK,EAAGrB,EAAOmC,GAAW7C,WAAW,GAAIuB,GACxC,MACD,KAAK,EAAGb,EAAO8B,EAAQT,aAAYR,GAClC,MACD,QAASb,EAAO3B,EAAKgD,aAAYR,GAGnC,OAAOA,EAQD,SAASuB,EAAU5C,EAAO6C,GAChC,QAASA,GAASb,IAEjB,GAAIH,YAAY,IAAMA,YAAY,KAAQA,YAAY,IAAMA,YAAY,IAAQA,YAAY,IAAMA,YAAY,GAC7G,MAEF,OAAOxB,EAAML,EAAOkC,KAAWW,EAAQ,GAAKZ,KAAU,IAAMD,KAAU,KAOhE,SAASO,EAAWpB,GAC1B,MAAOa,IACN,OAAQH,aAEP,KAAKV,EACJ,OAAOrB,WAER,KAAK,GAAI,KAAK,GACb,GAAIqB,IAAS,IAAMA,IAAS,GAC3BoB,EAAUV,aACX,MAED,KAAK,GACJ,GAAIV,IAAS,GACZoB,EAAUpB,GACX,MAED,KAAK,GACJa,IACA,MAGH,OAAOlC,WAQD,SAASgD,GAAW3B,EAAMnB,GAChC,MAAOgC,IAEN,GAAIb,EAAOU,cAAc,GAAK,GAC7B,WAEI,GAAIV,EAAOU,cAAc,GAAK,IAAMI,MAAW,GACnD,MAEF,MAAO,KAAO5B,EAAML,EAAOF,WAAW,GAAK,IAAMjB,EAAKsC,IAAS,GAAKA,EAAOa,KAOrE,SAASW,GAAY3C,GAC3B,OAAQmC,EAAMF,KACbD,IAED,OAAO3B,EAAML,EAAOF,YCvPd,SAASiD,GAAS5D,GACxB,OAAOkD,EAAQW,GAAM,GAAI,KAAM,KAAM,KAAM,CAAC,IAAK7D,EAAQiD,EAAMjD,GAAQ,EAAG,CAAC,GAAIA,IAezE,SAAS6D,GAAO7D,EAAO8B,EAAMC,EAAQ+B,EAAMC,EAAOC,EAAUC,EAAQC,EAAQC,GAClF,IAAItD,EAAQ,EACZ,IAAIuD,EAAS,EACb,IAAInE,EAASgE,EACb,IAAII,EAAS,EACb,IAAIC,EAAW,EACf,IAAIC,EAAW,EACf,IAAIC,EAAW,EACf,IAAIC,EAAW,EACf,IAAIC,EAAY,EAChB,IAAIhC,EAAY,EAChB,IAAIV,EAAO,GACX,IAAIC,EAAQ8B,EACZ,IAAI7B,EAAW8B,EACf,IAAIW,EAAYb,EAChB,IAAIlB,EAAaZ,EAEjB,MAAOyC,EACN,OAAQF,EAAW7B,EAAWA,EAAYG,KAEzC,KAAK,GACJ,GAAI0B,GAAY,KAAOrE,EAAO0C,EAAY3C,EAAS,IAAM,GAAI,CAC5D,GAAIQ,EAAQmC,GAAcrC,EAAQ4C,EAAQT,GAAY,IAAK,OAAQ,MAAOlD,EAAIqB,EAAQqD,EAAOrD,EAAQ,GAAK,MAAQ,EACjH6D,GAAa,EACd,MAGF,KAAK,GAAI,KAAK,GAAI,KAAK,GACtB9B,GAAcO,EAAQT,GACtB,MAED,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GAC9BE,GAAcW,EAAWgB,GACzB,MAED,KAAK,GACJ3B,GAAca,EAASV,IAAU,EAAG,GACpC,SAED,KAAK,GACJ,OAAQD,KACP,KAAK,GAAI,KAAK,GACbzB,EAAOuD,GAAQjB,GAAUd,IAAQE,KAAUjB,EAAMC,EAAQoC,GAAeA,GACxE,MACD,QACCvB,GAAc,IAEhB,MAED,KAAK,IAAM4B,EACVN,EAAOrD,KAAWM,EAAOyB,GAAc8B,EAExC,KAAK,IAAMF,EAAU,KAAK,GAAI,KAAK,EAClC,OAAQ9B,GAEP,KAAK,EAAG,KAAK,IAAK+B,EAAW,EAE7B,KAAK,GAAKL,EAAQ,GAAIM,IAAc,EAAG9B,EAAarC,EAAQqC,EAAY,MAAO,IAC9E,GAAI0B,EAAW,GAAMnD,EAAOyB,GAAc3C,EACzCoB,EAAOiD,EAAW,GAAKO,GAAYjC,EAAa,IAAKkB,EAAM/B,EAAQ9B,EAAS,EAAGkE,GAAgBU,GAAYtE,EAAQqC,EAAY,IAAK,IAAM,IAAKkB,EAAM/B,EAAQ9B,EAAS,EAAGkE,GAAeA,GACzL,MAED,KAAK,GAAIvB,GAAc,IAEvB,QACCvB,EAAOsD,EAAYG,GAAQlC,EAAYd,EAAMC,EAAQlB,EAAOuD,EAAQL,EAAOG,EAAQlC,EAAMC,EAAQ,GAAIC,EAAW,GAAIjC,EAAQ+D,GAAWA,GAEvI,GAAItB,IAAc,IACjB,GAAI0B,IAAW,EACdP,GAAMjB,EAAYd,EAAM6C,EAAWA,EAAW1C,EAAO+B,EAAU/D,EAAQiE,EAAQhC,QAE/E,OAAQmC,IAAW,IAAMnE,EAAO0C,EAAY,KAAO,IAAM,IAAMyB,GAE9D,KAAK,IAAK,KAAK,IAAK,KAAK,IAAK,KAAK,IAClCR,GAAM7D,EAAO2E,EAAWA,EAAWb,GAAQzC,EAAOyD,GAAQ9E,EAAO2E,EAAWA,EAAW,EAAG,EAAGZ,EAAOG,EAAQlC,EAAM+B,EAAO9B,EAAQ,GAAIhC,EAAQiC,GAAWA,GAAW6B,EAAO7B,EAAUjC,EAAQiE,EAAQJ,EAAO7B,EAAQC,GACnN,MACD,QACC2B,GAAMjB,EAAY+B,EAAWA,EAAWA,EAAW,CAAC,IAAKzC,EAAU,EAAGgC,EAAQhC,IAIpFrB,EAAQuD,EAASE,EAAW,EAAGE,EAAWE,EAAY,EAAG1C,EAAOY,EAAa,GAAI3C,EAASgE,EAC1F,MAED,KAAK,GACJhE,EAAS,EAAIkB,EAAOyB,GAAa0B,EAAWC,EAC7C,QACC,GAAIC,EAAW,EACd,GAAI9B,GAAa,MACd8B,OACE,GAAI9B,GAAa,KAAO8B,KAAc,GAAK7B,KAAU,IACzD,SAEF,OAAQC,GAAclD,EAAKgD,GAAYA,EAAY8B,GAElD,KAAK,GACJE,EAAYN,EAAS,EAAI,GAAKxB,GAAc,MAAO,GACnD,MAED,KAAK,GACJsB,EAAOrD,MAAYM,EAAOyB,GAAc,GAAK8B,EAAWA,EAAY,EACpE,MAED,KAAK,GAEJ,GAAI5B,MAAW,GACdF,GAAcO,EAAQN,KAEvBwB,EAASvB,IAAQsB,EAASnE,EAASkB,EAAOa,EAAOY,GAAcY,GAAWT,MAAWL,IACrF,MAED,KAAK,GACJ,GAAI6B,IAAa,IAAMpD,EAAOyB,IAAe,EAC5C4B,EAAW,GAIjB,OAAOR,EAkBD,SAASc,GAAS9E,EAAO8B,EAAMC,EAAQlB,EAAOuD,EAAQL,EAAOG,EAAQlC,EAAMC,EAAOC,EAAUjC,EAAQkC,GAC1G,IAAI4C,EAAOX,EAAS,EACpB,IAAIN,EAAOM,IAAW,EAAIL,EAAQ,CAAC,IACnC,IAAIiB,EAAO5D,EAAO0C,GAElB,IAAK,IAAImB,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGF,EAAIpE,IAASoE,EAC1C,IAAK,IAAIG,EAAI,EAAGC,EAAItE,EAAOf,EAAO+E,EAAO,EAAGA,EAAOvF,EAAI0F,EAAIhB,EAAOe,KAAMK,EAAItF,EAAOoF,EAAIJ,IAAQI,EAC9F,GAAIE,EAAInF,EAAK+E,EAAI,EAAIpB,EAAKsB,GAAK,IAAMC,EAAI9E,EAAQ8E,EAAG,OAAQvB,EAAKsB,KAChEnD,EAAMkD,KAAOG,EAEhB,OAAOzD,EAAK7B,EAAO8B,EAAMC,EAAQqC,IAAW,EAAI5F,EAAUwD,EAAMC,EAAOC,EAAUjC,EAAQkC,GAUnF,SAASyC,GAAS5E,EAAO8B,EAAMC,EAAQI,GAC7C,OAAON,EAAK7B,EAAO8B,EAAMC,EAAQxD,EAASmB,EAAK+C,KAAS1B,EAAOf,EAAO,GAAI,GAAI,EAAGmC,GAW3E,SAAS0C,GAAa7E,EAAO8B,EAAMC,EAAQ9B,EAAQkC,GACzD,OAAON,EAAK7B,EAAO8B,EAAMC,EAAQtD,EAAasC,EAAOf,EAAO,EAAGC,GAASc,EAAOf,EAAOC,EAAS,GAAI,GAAIA,EAAQkC,GCvLzG,SAASoD,GAAQvF,EAAOC,EAAQiC,GACtC,OAAQnC,EAAKC,EAAOC,IAEnB,KAAK,KACJ,OAAO3B,EAAS,SAAW0B,EAAQA,EAEpC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAEvE,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAE5D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAE5D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAC3D,OAAO1B,EAAS0B,EAAQA,EAEzB,KAAK,KACJ,OAAO3B,EAAM2B,EAAQA,EAEtB,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAChD,OAAO1B,EAAS0B,EAAQ3B,EAAM2B,EAAQ5B,EAAK4B,EAAQA,EAEpD,KAAK,KACJ,OAAQE,EAAOF,EAAOC,EAAS,KAE9B,KAAK,IACJ,OAAO3B,EAAS0B,EAAQ5B,EAAKmC,EAAQP,EAAO,qBAAsB,MAAQA,EAE3E,KAAK,IACJ,OAAO1B,EAAS0B,EAAQ5B,EAAKmC,EAAQP,EAAO,qBAAsB,SAAWA,EAE9E,KAAK,GACJ,OAAO1B,EAAS0B,EAAQ5B,EAAKmC,EAAQP,EAAO,qBAAsB,MAAQA,EAI7E,KAAK,KAAM,KAAK,KAAM,KAAK,KAC1B,OAAO1B,EAAS0B,EAAQ5B,EAAK4B,EAAQA,EAEtC,KAAK,KACJ,OAAO1B,EAAS0B,EAAQ5B,EAAK,QAAU4B,EAAQA,EAEhD,KAAK,KACJ,OAAO1B,EAAS0B,EAAQO,EAAQP,EAAO,iBAAkB1B,EAAS,WAAaF,EAAK,aAAe4B,EAEpG,KAAK,KACJ,OAAO1B,EAAS0B,EAAQ5B,EAAK,aAAemC,EAAQP,EAAO,eAAgB,MAAQI,EAAMJ,EAAO,kBAAoB5B,EAAK,YAAcmC,EAAQP,EAAO,eAAgB,IAAM,IAAMA,EAEnL,KAAK,KACJ,OAAO1B,EAAS0B,EAAQ5B,EAAK,iBAAmBmC,EAAQP,EAAO,6BAA8B,IAAMA,EAEpG,KAAK,KACJ,OAAO1B,EAAS0B,EAAQ5B,EAAKmC,EAAQP,EAAO,SAAU,YAAcA,EAErE,KAAK,KACJ,OAAO1B,EAAS0B,EAAQ5B,EAAKmC,EAAQP,EAAO,QAAS,kBAAoBA,EAE1E,KAAK,KACJ,OAAO1B,EAAS,OAASiC,EAAQP,EAAO,QAAS,IAAM1B,EAAS0B,EAAQ5B,EAAKmC,EAAQP,EAAO,OAAQ,YAAcA,EAEnH,KAAK,KACJ,OAAO1B,EAASiC,EAAQP,EAAO,qBAAsB,KAAO1B,EAAS,MAAQ0B,EAE9E,KAAK,KACJ,OAAOO,EAAQA,EAAQA,EAAQP,EAAO,eAAgB1B,EAAS,MAAO,cAAeA,EAAS,MAAO0B,EAAO,IAAMA,EAEnH,KAAK,KAAM,KAAK,KACf,OAAOO,EAAQP,EAAO,oBAAqB1B,EAAS,KAAO,QAE5D,KAAK,KACJ,OAAOiC,EAAQA,EAAQP,EAAO,oBAAqB1B,EAAS,cAAgBF,EAAK,gBAAiB,aAAc,WAAaE,EAAS0B,EAAQA,EAE/I,KAAK,KACJ,IAAKI,EAAMJ,EAAO,kBAAmB,OAAO5B,EAAK,oBAAsB2C,EAAOf,EAAOC,GAAUD,EAC/F,MAED,KAAK,KAAM,KAAK,KACf,OAAO5B,EAAKmC,EAAQP,EAAO,YAAa,IAAMA,EAE/C,KAAK,KAAM,KAAK,KACf,GAAIkC,GAAYA,EAASsD,MAAK,SAAUC,EAAS5E,GAAS,OAAOZ,EAASY,EAAOT,EAAMqF,EAAQxD,MAAO,mBAAoB,CACzH,OAAQxB,EAAQT,GAASkC,EAAWA,EAASjC,GAAQD,OAAQ,OAAQ,GAAKA,EAAS5B,EAAKmC,EAAQP,EAAO,SAAU,IAAMA,EAAQ5B,EAAK,mBAAqBqC,EAAQyB,EAAU,OAAQ,GAAK9B,EAAM8B,EAAU,QAAU9B,EAAM8B,EAAU,QAAU9B,EAAMJ,EAAO,QAAU,IAEpQ,OAAO5B,EAAKmC,EAAQP,EAAO,SAAU,IAAMA,EAE5C,KAAK,KAAM,KAAK,KACf,OAAQkC,GAAYA,EAASsD,MAAK,SAAUC,GAAW,OAAOrF,EAAMqF,EAAQxD,MAAO,qBAAwBjC,EAAQ5B,EAAKmC,EAAQA,EAAQP,EAAO,OAAQ,SAAU,QAAS,IAAMA,EAEjL,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACrC,OAAOO,EAAQP,EAAO,kBAAmB1B,EAAS,QAAU0B,EAE7D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACtC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACtC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAErC,GAAImB,EAAOnB,GAAS,EAAIC,EAAS,EAChC,OAAQC,EAAOF,EAAOC,EAAS,IAE9B,KAAK,IAEJ,GAAIC,EAAOF,EAAOC,EAAS,KAAO,GACjC,MAEF,KAAK,IACJ,OAAOM,EAAQP,EAAO,mBAAoB,KAAO1B,EAAS,QAAU,KAAOD,GAAO6B,EAAOF,EAAOC,EAAS,IAAM,IAAM,KAAO,UAAYD,EAEzI,KAAK,IACJ,OAAQS,EAAQT,EAAO,UAAW,GAAKuF,GAAOhF,EAAQP,EAAO,UAAW,kBAAmBC,EAAQiC,GAAYlC,EAAQA,EAE1H,MAED,KAAK,KAAM,KAAK,KACf,OAAOO,EAAQP,EAAO,6CAA6C,SAAU0F,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GAAK,OAAQ5H,EAAKuH,EAAI,IAAMC,EAAII,GAAMH,EAAKzH,EAAKuH,EAAI,UAAYG,EAAIC,GAAKA,GAAKH,GAAMI,EAAI,IAAMhG,KAE9L,KAAK,KAEJ,GAAIE,EAAOF,EAAOC,EAAS,KAAO,IACjC,OAAOM,EAAQP,EAAO,IAAK,IAAM1B,GAAU0B,EAC5C,MAED,KAAK,KACJ,OAAQE,EAAOF,EAAOE,EAAOF,EAAO,MAAQ,GAAK,GAAK,KAErD,KAAK,IACJ,OAAOO,EAAQP,EAAO,gCAAiC,KAAO1B,GAAU4B,EAAOF,EAAO,MAAQ,GAAK,UAAY,IAAM,QAAU,KAAO1B,EAAS,OAAS,KAAOF,EAAK,WAAa4B,EAElL,KAAK,IACJ,OAAOO,EAAQP,EAAO,IAAK,IAAM5B,GAAM4B,EAEzC,MAED,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAChD,OAAOO,EAAQP,EAAO,UAAW,gBAAkBA,EAGrD,OAAOA,ECvID,SAASiG,GAAW/D,EAAUT,GACpC,IAAIyE,EAAS,GAEb,IAAK,IAAIjB,EAAI,EAAGA,EAAI/C,EAASjC,OAAQgF,IACpCiB,GAAUzE,EAASS,EAAS+C,GAAIA,EAAG/C,EAAUT,IAAa,GAE3D,OAAOyE,EAUD,SAASC,GAAWV,EAAS5E,EAAOqB,EAAUT,GACpD,OAAQgE,EAAQzD,MACf,KAAK1C,EAAO,GAAImG,EAAQvD,SAASjC,OAAQ,MACzC,KAAKrB,EAAQ,KAAKH,EAAa,OAAOgH,EAAQnD,OAASmD,EAAQnD,QAAUmD,EAAQzF,MACjF,KAAKzB,EAAS,MAAO,GACrB,KAAKW,EAAW,OAAOuG,EAAQnD,OAASmD,EAAQzF,MAAQ,IAAMiG,GAAUR,EAAQvD,SAAUT,GAAY,IACtG,KAAKjD,EAAS,IAAK2C,EAAOsE,EAAQzF,MAAQyF,EAAQxD,MAAMN,KAAK,MAAO,MAAO,GAG5E,OAAOR,EAAOe,EAAW+D,GAAUR,EAAQvD,SAAUT,IAAagE,EAAQnD,OAASmD,EAAQzF,MAAQ,IAAMkC,EAAW,IAAM,GCvBpH,SAASkE,GAAYC,GAC3B,IAAIpG,EAASmB,EAAOiF,GAEpB,OAAO,SAAUZ,EAAS5E,EAAOqB,EAAUT,GAC1C,IAAIyE,EAAS,GAEb,IAAK,IAAIjB,EAAI,EAAGA,EAAIhF,EAAQgF,IAC3BiB,GAAUG,EAAWpB,GAAGQ,EAAS5E,EAAOqB,EAAUT,IAAa,GAEhE,OAAOyE,GAQF,SAASI,GAAW7E,GAC1B,OAAO,SAAUgE,GAChB,IAAKA,EAAQ3D,KACZ,GAAI2D,EAAUA,EAAQnD,OACrBb,EAASgE,IAUN,SAASc,GAAUd,EAAS5E,EAAOqB,EAAUT,GACnD,GAAIgE,EAAQxF,QAAU,EACrB,IAAKwF,EAAQnD,OACZ,OAAQmD,EAAQzD,MACf,KAAKvD,EAAagH,EAAQnD,OAASiD,GAAOE,EAAQzF,MAAOyF,EAAQxF,OAAQiC,GACxE,OACD,KAAKhD,EACJ,OAAO+G,GAAU,CAAC1D,EAAKkD,EAAS,CAACzF,MAAOO,EAAQkF,EAAQzF,MAAO,IAAK,IAAM1B,MAAYmD,GACvF,KAAKjD,EACJ,GAAIiH,EAAQxF,OACX,OAAOuB,EAAQU,EAAWuD,EAAQxD,OAAO,SAAUjC,GAClD,OAAQI,EAAMJ,EAAOyB,EAAW,0BAE/B,IAAK,aAAc,IAAK,cACvBe,EAAKD,EAAKkD,EAAS,CAACxD,MAAO,CAAC1B,EAAQP,EAAO,cAAe,IAAM3B,EAAM,UACtEmE,EAAKD,EAAKkD,EAAS,CAACxD,MAAO,CAACjC,MAC5BH,EAAO4F,EAAS,CAACxD,MAAOL,EAAOM,EAAUT,KACzC,MAED,IAAK,gBACJe,EAAKD,EAAKkD,EAAS,CAACxD,MAAO,CAAC1B,EAAQP,EAAO,aAAc,IAAM1B,EAAS,gBACxEkE,EAAKD,EAAKkD,EAAS,CAACxD,MAAO,CAAC1B,EAAQP,EAAO,aAAc,IAAM3B,EAAM,UACrEmE,EAAKD,EAAKkD,EAAS,CAACxD,MAAO,CAAC1B,EAAQP,EAAO,aAAc5B,EAAK,gBAC9DoE,EAAKD,EAAKkD,EAAS,CAACxD,MAAO,CAACjC,MAC5BH,EAAO4F,EAAS,CAACxD,MAAOL,EAAOM,EAAUT,KACzC,MAGF,MAAO,OAUP,SAAS+E,GAAWf,GAC1B,OAAQA,EAAQzD,MACf,KAAKxD,EACJiH,EAAQxD,MAAQwD,EAAQxD,MAAMP,KAAI,SAAU1B,GAC3C,OAAOwB,EAAQ6B,EAASrD,IAAQ,SAAUA,EAAOa,EAAOqB,GACvD,OAAQhC,EAAOF,EAAO,IAErB,KAAK,GACJ,OAAOe,EAAOf,EAAO,EAAGmB,EAAOnB,IAEhC,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,IACvC,OAAOA,EAER,KAAK,GACJ,GAAIkC,IAAWrB,KAAW,SACzBqB,EAASrB,GAAS,GAAIqB,IAAWrB,GAAS,KAAOE,EAAOmB,EAASrB,GAAQA,EAAQ,GAAI,GAEvF,KAAK,GACJ,OAAOA,IAAU,EAAI,GAAKb,EAC3B,QACC,OAAQa,GACP,KAAK,EAAG4E,EAAUzF,EACjB,OAAOoB,EAAOc,GAAY,EAAI,GAAKlC,EACpC,KAAKa,EAAQO,EAAOc,GAAY,EAAG,KAAK,EACvC,OAAOrB,IAAU,EAAIb,EAAQyF,EAAUA,EAAUzF,EAAQyF,EAC1D,QACC,OAAOzF"}