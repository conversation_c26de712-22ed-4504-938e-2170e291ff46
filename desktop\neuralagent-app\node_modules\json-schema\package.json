{"name": "json-schema", "version": "0.4.0", "author": "<PERSON>", "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "files": ["lib"], "license": "(AFL-2.1 OR BSD-3-Clause)", "repository": {"type": "git", "url": "http://github.com/kriszyp/json-schema"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "vows --spec test/*.js"}}