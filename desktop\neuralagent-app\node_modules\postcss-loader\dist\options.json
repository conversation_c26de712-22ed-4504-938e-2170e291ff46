{"title": "PostCSS Loader options", "type": "object", "properties": {"postcssOptions": {"description": "Options to pass through to `Postcss`.", "link": "https://github.com/webpack-contrib/postcss-loader#postcssOptions", "anyOf": [{"type": "object", "additionalProperties": true, "properties": {"config": {"description": "Allows to specify PostCSS config path.", "link": "https://github.com/webpack-contrib/postcss-loader#config", "anyOf": [{"description": "Allows to specify the path to the configuration file", "type": "string"}, {"description": "Enables/Disables autoloading config", "type": "boolean"}]}}}, {"instanceof": "Function"}]}, "execute": {"description": "Enables/Disables PostCSS parser support in 'CSS-in-JS'.", "link": "https://github.com/webpack-contrib/postcss-loader#execute", "type": "boolean"}, "sourceMap": {"description": "Enables/Disables generation of source maps.", "link": "https://github.com/webpack-contrib/postcss-loader#sourcemap", "type": "boolean"}, "implementation": {"description": "The implementation of postcss to use, instead of the locally installed version", "link": "https://github.com/webpack-contrib/postcss-loader#implementation", "anyOf": [{"type": "string"}, {"instanceof": "Function"}]}}, "additionalProperties": false}