declare const _exports: {
    clear(): void;
    delete(key: string): boolean;
    forEach(callbackfn: (value: string, key: string, map: Map<string, string>) => void, thisArg?: any): void;
    get(key: string): string | undefined;
    has(key: string): boolean;
    set(key: string, value: string): Map<string, string>;
    readonly size: number;
    entries(): IterableIterator<[string, string]>;
    keys(): IterableIterator<string>;
    values(): IterableIterator<string>;
    [Symbol.iterator](): IterableIterator<[string, string]>;
    readonly [Symbol.toStringTag]: string;
};
export = _exports;
