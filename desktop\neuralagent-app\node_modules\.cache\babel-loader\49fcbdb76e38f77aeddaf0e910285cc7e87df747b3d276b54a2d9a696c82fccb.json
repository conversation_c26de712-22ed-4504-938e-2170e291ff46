{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\views\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FlexSpacer } from '../components/Elements/SmallElements';\nimport { IconButton } from '../components/Elements/Button';\nimport { FaArrowAltCircleUp } from 'react-icons/fa';\nimport { useDispatch, useSelector } from 'react-redux';\nimport axios from '../utils/axios';\nimport { setLoadingDialog, setError } from '../store';\nimport constants from '../utils/constants';\nimport { Text } from '../components/Elements/Typography';\nimport NATextArea from '../components/Elements/TextAreas';\nimport { useNavigate } from 'react-router-dom';\nimport { MdOutlineSchedule } from 'react-icons/md';\nimport { GiBrain } from 'react-icons/gi';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeDiv = styled.div`\n  flex: 1;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n`;\n_c = HomeDiv;\nconst Card = styled.div`\n  border: thin solid rgba(255,255,255,0.3);\n  border-radius: 20px;\n  padding: 15px;\n  width: 100%;\n  max-width: 600px;\n`;\n_c2 = Card;\nconst ToggleContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.9rem;\n  color: var(--secondary-color);\n`;\n_c3 = ToggleContainer;\nconst ModeToggle = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  background-color: ${({\n  active\n}) => active ? 'rgba(255,255,255,0.1)' : 'transparent'};\n  color: #fff;\n  border: thin solid rgba(255,255,255,0.3);\n  border-radius: 999px;\n  padding: 6px 12px;\n  font-size: 13px;\n  transition: background-color 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255,255,255,0.1);\n  }\n`;\n_c4 = ModeToggle;\nexport default function Home() {\n  _s();\n  const [messageText, setMessageText] = useState('');\n  const [backgroundMode, setBackgroundMode] = useState(false);\n  const [thinkingMode, setThinkingMode] = useState(false);\n  const accessToken = useSelector(state => state.accessToken);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const cancelRunningTask = tid => {\n    dispatch(setLoadingDialog(true));\n    axios.post(`/threads/${tid}/cancel_task`, {}, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      dispatch(setLoadingDialog(false));\n      window.electronAPI.stopAIAgent();\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n  const createThread = async () => {\n    if (messageText.length === 0) {\n      return;\n    }\n    const data = {\n      task: messageText,\n      background_mode: backgroundMode,\n      extended_thinking_mode: thinkingMode\n    };\n    setMessageText('');\n    dispatch(setLoadingDialog(true));\n    axios.post('/threads', data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(async response => {\n      dispatch(setLoadingDialog(false));\n      if (response.data.type === 'desktop_task') {\n        if (!backgroundMode && response.data.is_background_mode_requested) {\n          const ready = await window.electronAPI.isBackgroundModeReady();\n          if (!ready) {\n            cancelRunningTask();\n            return;\n          }\n        }\n        setBackgroundMode(backgroundMode || response.data.is_background_mode_requested);\n        setThinkingMode(thinkingMode || response.data.is_extended_thinking_mode_requested);\n        window.electronAPI.setLastThinkingModeValue((thinkingMode || response.data.is_extended_thinking_mode_requested).toString());\n        window.electronAPI.launchAIAgent(process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS, response.data.thread_id, backgroundMode || response.data.is_background_mode_requested);\n      }\n      navigate('/threads/' + response.data.thread_id);\n      window.location.reload();\n    }).catch(error => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        var _error$response$data;\n        if (((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) === 'Not_Browser_Task_BG_Mode') {\n          dispatch(setError(true, 'Background Mode only supports browser tasks.'));\n        } else {\n          dispatch(setError(true, constants.GENERAL_ERROR));\n        }\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n  const handleTextEnterKey = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      createThread();\n    }\n  };\n  const onBGModeToggleChange = async value => {\n    if (value) {\n      const ready = await window.electronAPI.isBackgroundModeReady();\n      if (!ready) {\n        window.electronAPI.startBackgroundSetup();\n        return;\n      }\n    }\n    setBackgroundMode(value);\n  };\n  useEffect(() => {\n    var _window$electronAPI;\n    if ((_window$electronAPI = window.electronAPI) !== null && _window$electronAPI !== void 0 && _window$electronAPI.onAIAgentLaunch) {\n      window.electronAPI.onAIAgentLaunch(threadId => {\n        navigate('/threads/' + threadId);\n        window.location.reload();\n      });\n    }\n  }, []);\n  useEffect(() => {\n    var _window$electronAPI2;\n    if ((_window$electronAPI2 = window.electronAPI) !== null && _window$electronAPI2 !== void 0 && _window$electronAPI2.onAIAgentExit) {\n      window.electronAPI.onAIAgentExit(() => {\n        window.location.reload();\n      });\n    }\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastBackgroundModeValue = await window.electronAPI.getLastBackgroundModeValue();\n      setBackgroundMode(lastBackgroundModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastThinkingModeValue = await window.electronAPI.getLastThinkingModeValue();\n      setThinkingMode(lastThinkingModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(HomeDiv, {\n    children: [/*#__PURE__*/_jsxDEV(Text, {\n      fontWeight: \"600\",\n      fontSize: \"23px\",\n      color: \"#fff\",\n      children: \"Start a New Task\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginTop: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(NATextArea, {\n        background: \"transparent\",\n        isDarkMode: true,\n        padding: \"10px 4px\",\n        placeholder: \"What do you want NeuralAgent to do?\",\n        rows: \"3\",\n        value: messageText,\n        onChange: e => setMessageText(e.target.value),\n        onKeyDown: handleTextEnterKey\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '10px',\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ToggleContainer, {\n          children: /*#__PURE__*/_jsxDEV(ModeToggle, {\n            active: backgroundMode,\n            onClick: () => onBGModeToggleChange(!backgroundMode),\n            children: [/*#__PURE__*/_jsxDEV(MdOutlineSchedule, {\n              style: {\n                fontSize: '19px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), \"Background\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToggleContainer, {\n          children: /*#__PURE__*/_jsxDEV(ModeToggle, {\n            active: thinkingMode,\n            onClick: () => setThinkingMode(!thinkingMode),\n            children: [/*#__PURE__*/_jsxDEV(GiBrain, {\n              style: {\n                fontSize: '19px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), \"Thinking\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FlexSpacer, {\n          isRTL: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          iconSize: \"35px\",\n          color: \"#fff\",\n          disabled: messageText.length === 0,\n          onClick: () => createThread(),\n          onKeyDown: handleTextEnterKey,\n          children: /*#__PURE__*/_jsxDEV(FaArrowAltCircleUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"xGcDBeO/4dKUec35Sa6kaix8j6k=\", false, function () {\n  return [useSelector, useDispatch, useNavigate];\n});\n_c5 = Home;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HomeDiv\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"ToggleContainer\");\n$RefreshReg$(_c4, \"ModeToggle\");\n$RefreshReg$(_c5, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FlexSpacer", "IconButton", "FaArrowAltCircleUp", "useDispatch", "useSelector", "axios", "setLoadingDialog", "setError", "constants", "Text", "NATextArea", "useNavigate", "MdOutlineSchedule", "GiBrain", "styled", "jsxDEV", "_jsxDEV", "HomeDiv", "div", "_c", "Card", "_c2", "ToggleContainer", "_c3", "ModeToggle", "button", "active", "_c4", "Home", "_s", "messageText", "setMessageText", "backgroundMode", "setBackgroundMode", "thinkingMode", "setThinkingMode", "accessToken", "state", "dispatch", "navigate", "cancelRunningTask", "tid", "post", "headers", "then", "response", "window", "electronAPI", "stopAIAgent", "catch", "error", "status", "BAD_REQUEST", "GENERAL_ERROR", "setTimeout", "createThread", "length", "data", "task", "background_mode", "extended_thinking_mode", "type", "is_background_mode_requested", "ready", "isBackgroundModeReady", "is_extended_thinking_mode_requested", "setLastThinkingModeValue", "toString", "launchAIAgent", "process", "env", "REACT_APP_PROTOCOL", "REACT_APP_DNS", "thread_id", "location", "reload", "_error$response$data", "message", "handleTextEnterKey", "e", "key", "shift<PERSON>ey", "preventDefault", "onBGModeToggleChange", "value", "startBackgroundSetup", "_window$electronAPI", "onAIAgentLaunch", "threadId", "_window$electronAPI2", "onAIAgentExit", "asyncTask", "lastBackgroundModeValue", "getLastBackgroundModeValue", "lastThinkingModeValue", "getLastThinkingModeValue", "children", "fontWeight", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginTop", "background", "isDarkMode", "padding", "placeholder", "rows", "onChange", "target", "onKeyDown", "display", "alignItems", "onClick", "width", "isRTL", "iconSize", "disabled", "_c5", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/views/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FlexSpacer } from '../components/Elements/SmallElements';\nimport { IconButton } from '../components/Elements/Button';\nimport { FaArrowAltCircleUp } from 'react-icons/fa';\nimport { useDispatch, useSelector } from 'react-redux';\nimport axios from '../utils/axios';\nimport { setLoadingDialog, setError } from '../store';\nimport constants from '../utils/constants';\nimport { Text } from '../components/Elements/Typography';\nimport NATextArea from '../components/Elements/TextAreas';\nimport { useNavigate } from 'react-router-dom';\nimport { MdOutlineSchedule } from 'react-icons/md';\nimport { GiBrain } from 'react-icons/gi';\n\nimport styled from 'styled-components';\n\nconst HomeDiv = styled.div`\n  flex: 1;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n`;\n\nconst Card = styled.div`\n  border: thin solid rgba(255,255,255,0.3);\n  border-radius: 20px;\n  padding: 15px;\n  width: 100%;\n  max-width: 600px;\n`;\n\nconst ToggleContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.9rem;\n  color: var(--secondary-color);\n`;\n\nconst ModeToggle = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  background-color: ${({ active }) => (active ? 'rgba(255,255,255,0.1)' : 'transparent')};\n  color: #fff;\n  border: thin solid rgba(255,255,255,0.3);\n  border-radius: 999px;\n  padding: 6px 12px;\n  font-size: 13px;\n  transition: background-color 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255,255,255,0.1);\n  }\n`;\n\n\nexport default function Home() {\n  const [messageText, setMessageText] = useState('');\n  const [backgroundMode, setBackgroundMode] = useState(false);\n  const [thinkingMode, setThinkingMode] = useState(false);\n\n  const accessToken = useSelector(state => state.accessToken);\n\n  const dispatch = useDispatch();\n\n  const navigate = useNavigate();\n\n  const cancelRunningTask = (tid) => {\n    dispatch(setLoadingDialog(true));\n    axios.post(`/threads/${tid}/cancel_task`, {}, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      dispatch(setLoadingDialog(false));\n      window.electronAPI.stopAIAgent();\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n\n  const createThread = async () => {\n    if (messageText.length === 0) {\n      return;\n    }\n    const data = {task: messageText, background_mode: backgroundMode, extended_thinking_mode: thinkingMode};\n    setMessageText('');\n    dispatch(setLoadingDialog(true));\n    axios.post('/threads', data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then(async (response) => {\n      dispatch(setLoadingDialog(false));\n      if (response.data.type === 'desktop_task') {\n        if (!backgroundMode && response.data.is_background_mode_requested) {\n          const ready = await window.electronAPI.isBackgroundModeReady();\n          if (!ready) {\n            cancelRunningTask();\n            return;\n          }\n        }\n        setBackgroundMode(backgroundMode || response.data.is_background_mode_requested);\n        setThinkingMode(thinkingMode || response.data.is_extended_thinking_mode_requested);\n        window.electronAPI.setLastThinkingModeValue((thinkingMode || response.data.is_extended_thinking_mode_requested).toString());\n        window.electronAPI.launchAIAgent(\n          process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS,\n          response.data.thread_id,\n          backgroundMode || response.data.is_background_mode_requested\n        );\n      }\n      navigate('/threads/' + response.data.thread_id);\n      window.location.reload();\n    }).catch((error) => {\n      dispatch(setLoadingDialog(false));\n      if (error.response.status === constants.status.BAD_REQUEST) {\n        if (error.response.data?.message === 'Not_Browser_Task_BG_Mode') {\n          dispatch(setError(true, 'Background Mode only supports browser tasks.'));\n        } else {\n          dispatch(setError(true, constants.GENERAL_ERROR));\n        }\n      } else {\n        dispatch(setError(true, constants.GENERAL_ERROR));\n      }\n      setTimeout(() => {\n        dispatch(setError(false, ''));\n      }, 3000);\n    });\n  };\n\n  const handleTextEnterKey = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      createThread();\n    }\n  };\n\n  const onBGModeToggleChange = async (value) => {\n    if (value) {\n      const ready = await window.electronAPI.isBackgroundModeReady();\n      if (!ready) {\n        window.electronAPI.startBackgroundSetup();\n        return;\n      }\n    }\n    setBackgroundMode(value);\n  };\n\n  useEffect(() => {\n    if (window.electronAPI?.onAIAgentLaunch) {\n      window.electronAPI.onAIAgentLaunch((threadId) => {\n        navigate('/threads/' + threadId)\n        window.location.reload();\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    if (window.electronAPI?.onAIAgentExit) {\n      window.electronAPI.onAIAgentExit(() => {\n        window.location.reload();\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastBackgroundModeValue = await window.electronAPI.getLastBackgroundModeValue();\n      setBackgroundMode(lastBackgroundModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastThinkingModeValue = await window.electronAPI.getLastThinkingModeValue();\n      setThinkingMode(lastThinkingModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n\n  return (\n    <HomeDiv>\n      <Text fontWeight='600' fontSize='23px' color='#fff'>\n        Start a New Task\n      </Text>\n      <Card style={{marginTop: '15px'}}>\n        <NATextArea\n          background='transparent'\n          isDarkMode\n          padding='10px 4px'\n          placeholder=\"What do you want NeuralAgent to do?\"\n          rows='3'\n          value={messageText}\n          onChange={(e) => setMessageText(e.target.value)}\n          onKeyDown={handleTextEnterKey}\n        />\n        <div style={{marginTop: '10px', display: 'flex', alignItems: 'center'}}>\n          <ToggleContainer>\n            <ModeToggle\n              active={backgroundMode}\n              onClick={() => onBGModeToggleChange(!backgroundMode)}\n            >\n              <MdOutlineSchedule style={{fontSize: '19px'}} />\n              Background\n            </ModeToggle>\n          </ToggleContainer>\n          <div style={{width: '10px'}} />\n          <ToggleContainer>\n            <ModeToggle\n              active={thinkingMode}\n              onClick={() => setThinkingMode(!thinkingMode)}\n            >\n              <GiBrain style={{fontSize: '19px'}} />\n              Thinking\n            </ModeToggle>\n          </ToggleContainer>\n          <FlexSpacer isRTL={false} />\n          <IconButton\n            iconSize='35px'\n            color='#fff'\n            disabled={messageText.length === 0}\n            onClick={() => createThread()}\n            onKeyDown={handleTextEnterKey}>\n            <FaArrowAltCircleUp />\n          </IconButton>\n        </div>\n      </Card>\n    </HomeDiv>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,KAAK,MAAM,gBAAgB;AAClC,SAASC,gBAAgB,EAAEC,QAAQ,QAAQ,UAAU;AACrD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,mCAAmC;AACxD,OAAOC,UAAU,MAAM,kCAAkC;AACzD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,gBAAgB;AAExC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGH,MAAM,CAACI,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,OAAO;AASb,MAAMG,IAAI,GAAGN,MAAM,CAACI,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,IAAI;AAQV,MAAME,eAAe,GAAGR,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GANID,eAAe;AAQrB,MAAME,UAAU,GAAGV,MAAM,CAACW,MAAM;AAChC;AACA;AACA;AACA,sBAAsB,CAAC;EAAEC;AAAO,CAAC,KAAMA,MAAM,GAAG,uBAAuB,GAAG,aAAc;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhBIH,UAAU;AAmBhB,eAAe,SAASI,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMsC,WAAW,GAAGhC,WAAW,CAACiC,KAAK,IAAIA,KAAK,CAACD,WAAW,CAAC;EAE3D,MAAME,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAE9B,MAAMoC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9B,MAAM6B,iBAAiB,GAAIC,GAAG,IAAK;IACjCH,QAAQ,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCD,KAAK,CAACqC,IAAI,CAAC,YAAYD,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE;MAC5CE,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGP;MAC/B;IACF,CAAC,CAAC,CAACQ,IAAI,CAAEC,QAAQ,IAAK;MACpBP,QAAQ,CAAChC,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjCwC,MAAM,CAACC,WAAW,CAACC,WAAW,CAAC,CAAC;IAClC,CAAC,CAAC,CAACC,KAAK,CAAEC,KAAK,IAAK;MAClBZ,QAAQ,CAAChC,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAI4C,KAAK,CAACL,QAAQ,CAACM,MAAM,KAAK3C,SAAS,CAAC2C,MAAM,CAACC,WAAW,EAAE;QAC1Dd,QAAQ,CAAC/B,QAAQ,CAAC,IAAI,EAAEC,SAAS,CAAC6C,aAAa,CAAC,CAAC;MACnD,CAAC,MAAM;QACLf,QAAQ,CAAC/B,QAAQ,CAAC,IAAI,EAAEC,SAAS,CAAC6C,aAAa,CAAC,CAAC;MACnD;MACAC,UAAU,CAAC,MAAM;QACfhB,QAAQ,CAAC/B,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIzB,WAAW,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC5B;IACF;IACA,MAAMC,IAAI,GAAG;MAACC,IAAI,EAAE5B,WAAW;MAAE6B,eAAe,EAAE3B,cAAc;MAAE4B,sBAAsB,EAAE1B;IAAY,CAAC;IACvGH,cAAc,CAAC,EAAE,CAAC;IAClBO,QAAQ,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChCD,KAAK,CAACqC,IAAI,CAAC,UAAU,EAAEe,IAAI,EAAE;MAC3Bd,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGP;MAC/B;IACF,CAAC,CAAC,CAACQ,IAAI,CAAC,MAAOC,QAAQ,IAAK;MAC1BP,QAAQ,CAAChC,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAIuC,QAAQ,CAACY,IAAI,CAACI,IAAI,KAAK,cAAc,EAAE;QACzC,IAAI,CAAC7B,cAAc,IAAIa,QAAQ,CAACY,IAAI,CAACK,4BAA4B,EAAE;UACjE,MAAMC,KAAK,GAAG,MAAMjB,MAAM,CAACC,WAAW,CAACiB,qBAAqB,CAAC,CAAC;UAC9D,IAAI,CAACD,KAAK,EAAE;YACVvB,iBAAiB,CAAC,CAAC;YACnB;UACF;QACF;QACAP,iBAAiB,CAACD,cAAc,IAAIa,QAAQ,CAACY,IAAI,CAACK,4BAA4B,CAAC;QAC/E3B,eAAe,CAACD,YAAY,IAAIW,QAAQ,CAACY,IAAI,CAACQ,mCAAmC,CAAC;QAClFnB,MAAM,CAACC,WAAW,CAACmB,wBAAwB,CAAC,CAAChC,YAAY,IAAIW,QAAQ,CAACY,IAAI,CAACQ,mCAAmC,EAAEE,QAAQ,CAAC,CAAC,CAAC;QAC3HrB,MAAM,CAACC,WAAW,CAACqB,aAAa,CAC9BC,OAAO,CAACC,GAAG,CAACC,kBAAkB,GAAG,KAAK,GAAGF,OAAO,CAACC,GAAG,CAACE,aAAa,EAClE3B,QAAQ,CAACY,IAAI,CAACgB,SAAS,EACvBzC,cAAc,IAAIa,QAAQ,CAACY,IAAI,CAACK,4BAClC,CAAC;MACH;MACAvB,QAAQ,CAAC,WAAW,GAAGM,QAAQ,CAACY,IAAI,CAACgB,SAAS,CAAC;MAC/C3B,MAAM,CAAC4B,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC1B,KAAK,CAAEC,KAAK,IAAK;MAClBZ,QAAQ,CAAChC,gBAAgB,CAAC,KAAK,CAAC,CAAC;MACjC,IAAI4C,KAAK,CAACL,QAAQ,CAACM,MAAM,KAAK3C,SAAS,CAAC2C,MAAM,CAACC,WAAW,EAAE;QAAA,IAAAwB,oBAAA;QAC1D,IAAI,EAAAA,oBAAA,GAAA1B,KAAK,CAACL,QAAQ,CAACY,IAAI,cAAAmB,oBAAA,uBAAnBA,oBAAA,CAAqBC,OAAO,MAAK,0BAA0B,EAAE;UAC/DvC,QAAQ,CAAC/B,QAAQ,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;QAC1E,CAAC,MAAM;UACL+B,QAAQ,CAAC/B,QAAQ,CAAC,IAAI,EAAEC,SAAS,CAAC6C,aAAa,CAAC,CAAC;QACnD;MACF,CAAC,MAAM;QACLf,QAAQ,CAAC/B,QAAQ,CAAC,IAAI,EAAEC,SAAS,CAAC6C,aAAa,CAAC,CAAC;MACnD;MACAC,UAAU,CAAC,MAAM;QACfhB,QAAQ,CAAC/B,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuE,kBAAkB,GAAIC,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB3B,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAM4B,oBAAoB,GAAG,MAAOC,KAAK,IAAK;IAC5C,IAAIA,KAAK,EAAE;MACT,MAAMrB,KAAK,GAAG,MAAMjB,MAAM,CAACC,WAAW,CAACiB,qBAAqB,CAAC,CAAC;MAC9D,IAAI,CAACD,KAAK,EAAE;QACVjB,MAAM,CAACC,WAAW,CAACsC,oBAAoB,CAAC,CAAC;QACzC;MACF;IACF;IACApD,iBAAiB,CAACmD,KAAK,CAAC;EAC1B,CAAC;EAEDrF,SAAS,CAAC,MAAM;IAAA,IAAAuF,mBAAA;IACd,KAAAA,mBAAA,GAAIxC,MAAM,CAACC,WAAW,cAAAuC,mBAAA,eAAlBA,mBAAA,CAAoBC,eAAe,EAAE;MACvCzC,MAAM,CAACC,WAAW,CAACwC,eAAe,CAAEC,QAAQ,IAAK;QAC/CjD,QAAQ,CAAC,WAAW,GAAGiD,QAAQ,CAAC;QAChC1C,MAAM,CAAC4B,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN5E,SAAS,CAAC,MAAM;IAAA,IAAA0F,oBAAA;IACd,KAAAA,oBAAA,GAAI3C,MAAM,CAACC,WAAW,cAAA0C,oBAAA,eAAlBA,oBAAA,CAAoBC,aAAa,EAAE;MACrC5C,MAAM,CAACC,WAAW,CAAC2C,aAAa,CAAC,MAAM;QACrC5C,MAAM,CAAC4B,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN5E,SAAS,CAAC,MAAM;IACd,MAAM4F,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,uBAAuB,GAAG,MAAM9C,MAAM,CAACC,WAAW,CAAC8C,0BAA0B,CAAC,CAAC;MACrF5D,iBAAiB,CAAC2D,uBAAuB,KAAK,MAAM,CAAC;IACvD,CAAC;IACDD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN5F,SAAS,CAAC,MAAM;IACd,MAAM4F,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMG,qBAAqB,GAAG,MAAMhD,MAAM,CAACC,WAAW,CAACgD,wBAAwB,CAAC,CAAC;MACjF5D,eAAe,CAAC2D,qBAAqB,KAAK,MAAM,CAAC;IACnD,CAAC;IACDH,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE3E,OAAA,CAACC,OAAO;IAAA+E,QAAA,gBACNhF,OAAA,CAACP,IAAI;MAACwF,UAAU,EAAC,KAAK;MAACC,QAAQ,EAAC,MAAM;MAACC,KAAK,EAAC,MAAM;MAAAH,QAAA,EAAC;IAEpD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACPvF,OAAA,CAACI,IAAI;MAACoF,KAAK,EAAE;QAACC,SAAS,EAAE;MAAM,CAAE;MAAAT,QAAA,gBAC/BhF,OAAA,CAACN,UAAU;QACTgG,UAAU,EAAC,aAAa;QACxBC,UAAU;QACVC,OAAO,EAAC,UAAU;QAClBC,WAAW,EAAC,qCAAqC;QACjDC,IAAI,EAAC,GAAG;QACR1B,KAAK,EAAEtD,WAAY;QACnBiF,QAAQ,EAAGhC,CAAC,IAAKhD,cAAc,CAACgD,CAAC,CAACiC,MAAM,CAAC5B,KAAK,CAAE;QAChD6B,SAAS,EAAEnC;MAAmB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACFvF,OAAA;QAAKwF,KAAK,EAAE;UAACC,SAAS,EAAE,MAAM;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAAnB,QAAA,gBACrEhF,OAAA,CAACM,eAAe;UAAA0E,QAAA,eACdhF,OAAA,CAACQ,UAAU;YACTE,MAAM,EAAEM,cAAe;YACvBoF,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,CAACnD,cAAc,CAAE;YAAAgE,QAAA,gBAErDhF,OAAA,CAACJ,iBAAiB;cAAC4F,KAAK,EAAE;gBAACN,QAAQ,EAAE;cAAM;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAClBvF,OAAA;UAAKwF,KAAK,EAAE;YAACa,KAAK,EAAE;UAAM;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BvF,OAAA,CAACM,eAAe;UAAA0E,QAAA,eACdhF,OAAA,CAACQ,UAAU;YACTE,MAAM,EAAEQ,YAAa;YACrBkF,OAAO,EAAEA,CAAA,KAAMjF,eAAe,CAAC,CAACD,YAAY,CAAE;YAAA8D,QAAA,gBAE9ChF,OAAA,CAACH,OAAO;cAAC2F,KAAK,EAAE;gBAACN,QAAQ,EAAE;cAAM;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAClBvF,OAAA,CAAChB,UAAU;UAACsH,KAAK,EAAE;QAAM;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BvF,OAAA,CAACf,UAAU;UACTsH,QAAQ,EAAC,MAAM;UACfpB,KAAK,EAAC,MAAM;UACZqB,QAAQ,EAAE1F,WAAW,CAAC0B,MAAM,KAAK,CAAE;UACnC4D,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,CAAE;UAC9B0D,SAAS,EAAEnC,kBAAmB;UAAAkB,QAAA,eAC9BhF,OAAA,CAACd,kBAAkB;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC1E,EAAA,CAtLuBD,IAAI;EAAA,QAKNxB,WAAW,EAEdD,WAAW,EAEXQ,WAAW;AAAA;AAAA8G,GAAA,GATN7F,IAAI;AAAA,IAAAT,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAA8F,GAAA;AAAAC,YAAA,CAAAvG,EAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}