{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nexport function setAppLoading(isLoading) {\n  return {\n    type: 'SET_APP_LOADING',\n    isAppLoading: isLoading\n  };\n}\nexport function setFullLoading(isLoading) {\n  return {\n    type: 'SET_FULL_LOADING',\n    isFullLoading: isLoading\n  };\n}\nexport function setLoadingDialog(isLoading) {\n  return {\n    type: 'SET_LOADING_DIALOG',\n    isLoadingDialog: isLoading\n  };\n}\nexport function setAccessToken(accessToken) {\n  return {\n    type: 'SET_ACCESS_TOKEN',\n    accessToken: accessToken\n  };\n}\nexport function setUser(user) {\n  return {\n    type: 'SET_USER',\n    user: user\n  };\n}\nexport function setError(isError, errorMessage = '') {\n  return {\n    type: 'SET_ERROR',\n    isError: isError,\n    errorMessage: errorMessage\n  };\n}\nexport function setSuccess(isSuccess, successMsg = '') {\n  return {\n    type: 'SET_SUCCESS',\n    isSuccess: isSuccess,\n    successMsg: successMsg\n  };\n}\nconst defaultState = {\n  isAppLoading: true,\n  isFullLoading: false,\n  isLoadingDialog: false,\n  accessToken: null,\n  user: null,\n  isError: false,\n  errorMessage: '',\n  isSuccess: false,\n  successMsg: ''\n};\nfunction reducer(state = defaultState, action) {\n  let newState = Object.assign({}, state);\n  switch (action.type) {\n    case 'SET_APP_LOADING':\n      newState.isAppLoading = action.isAppLoading;\n      return newState;\n    case 'SET_FULL_LOADING':\n      newState.isFullLoading = action.isFullLoading;\n      return newState;\n    case 'SET_LOADING_DIALOG':\n      newState.isLoadingDialog = action.isLoadingDialog;\n      return newState;\n    case 'SET_ACCESS_TOKEN':\n      newState.accessToken = action.accessToken;\n      return newState;\n    case 'SET_USER':\n      newState.user = action.user;\n      return newState;\n    case 'SET_ERROR':\n      if (action.isError) {\n        newState.errorMessage = action.errorMessage;\n      }\n      newState.isError = action.isError;\n      return newState;\n    case 'SET_SUCCESS':\n      if (action.isSuccess) {\n        newState.successMsg = action.successMsg;\n      }\n      newState.isSuccess = action.isSuccess;\n      return newState;\n    default:\n      break;\n  }\n  return state;\n}\nexport const store = configureStore({\n  reducer: reducer\n});", "map": {"version": 3, "names": ["configureStore", "setAppLoading", "isLoading", "type", "isAppLoading", "setFullLoading", "isFullLoading", "setLoadingDialog", "isLoadingDialog", "setAccessToken", "accessToken", "setUser", "user", "setError", "isError", "errorMessage", "setSuccess", "isSuccess", "successMsg", "defaultState", "reducer", "state", "action", "newState", "Object", "assign", "store"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/store/index.js"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\n\nexport function setAppLoading(isLoading) {\n  return {\n    type: 'SET_APP_LOADING',\n    isAppLoading: isLoading,\n  };\n}\n\nexport function setFullLoading(isLoading) {\n  return {\n    type: 'SET_FULL_LOADING',\n    isFullLoading: isLoading,\n  };\n}\n\nexport function setLoadingDialog(isLoading) {\n  return {\n    type: 'SET_LOADING_DIALOG',\n    isLoadingDialog: isLoading,\n  };\n}\n\nexport function setAccessToken(accessToken) {\n  return {\n    type: 'SET_ACCESS_TOKEN',\n    accessToken: accessToken,\n  };\n}\n\nexport function setUser(user) {\n  return {\n    type: 'SET_USER',\n    user: user,\n  };\n}\n\nexport function setError(isError, errorMessage='') {\n  return {\n    type: 'SET_ERROR',\n    isError: isError,\n    errorMessage: errorMessage,\n  };\n}\n\nexport function setSuccess(isSuccess, successMsg='') {\n  return {\n    type: 'SET_SUCCESS',\n    isSuccess: isSuccess,\n    successMsg: successMsg,\n  };\n}\n\nconst defaultState = {\n  isAppLoading: true,\n  isFullLoading: false,\n  isLoadingDialog: false,\n  accessToken: null,\n  user: null,\n  isError: false,\n  errorMessage: '',\n  isSuccess: false,\n  successMsg: '',\n}\n\nfunction reducer(state=defaultState, action) {\n  let newState = Object.assign({}, state);\n  switch (action.type) {\n    case 'SET_APP_LOADING':\n      newState.isAppLoading = action.isAppLoading;\n      return newState;\n    case 'SET_FULL_LOADING':\n      newState.isFullLoading = action.isFullLoading;\n      return newState;\n    case 'SET_LOADING_DIALOG':\n      newState.isLoadingDialog = action.isLoadingDialog;\n      return newState;\n    case 'SET_ACCESS_TOKEN':\n      newState.accessToken = action.accessToken;\n      return newState;\n    case 'SET_USER':\n      newState.user = action.user;\n      return newState;\n    case 'SET_ERROR':\n      if (action.isError) {\n        newState.errorMessage = action.errorMessage;\n      }\n      newState.isError = action.isError;\n      return newState;\n    case 'SET_SUCCESS':\n      if (action.isSuccess) {\n        newState.successMsg = action.successMsg;\n      }\n      newState.isSuccess = action.isSuccess;\n      return newState;\n    default:\n      break;\n  }\n  return state;\n}\n\nexport const store = configureStore({ reducer: reducer });\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AAEjD,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAE;EACvC,OAAO;IACLC,IAAI,EAAE,iBAAiB;IACvBC,YAAY,EAAEF;EAChB,CAAC;AACH;AAEA,OAAO,SAASG,cAAcA,CAACH,SAAS,EAAE;EACxC,OAAO;IACLC,IAAI,EAAE,kBAAkB;IACxBG,aAAa,EAAEJ;EACjB,CAAC;AACH;AAEA,OAAO,SAASK,gBAAgBA,CAACL,SAAS,EAAE;EAC1C,OAAO;IACLC,IAAI,EAAE,oBAAoB;IAC1BK,eAAe,EAAEN;EACnB,CAAC;AACH;AAEA,OAAO,SAASO,cAAcA,CAACC,WAAW,EAAE;EAC1C,OAAO;IACLP,IAAI,EAAE,kBAAkB;IACxBO,WAAW,EAAEA;EACf,CAAC;AACH;AAEA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO;IACLT,IAAI,EAAE,UAAU;IAChBS,IAAI,EAAEA;EACR,CAAC;AACH;AAEA,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAEC,YAAY,GAAC,EAAE,EAAE;EACjD,OAAO;IACLZ,IAAI,EAAE,WAAW;IACjBW,OAAO,EAAEA,OAAO;IAChBC,YAAY,EAAEA;EAChB,CAAC;AACH;AAEA,OAAO,SAASC,UAAUA,CAACC,SAAS,EAAEC,UAAU,GAAC,EAAE,EAAE;EACnD,OAAO;IACLf,IAAI,EAAE,aAAa;IACnBc,SAAS,EAAEA,SAAS;IACpBC,UAAU,EAAEA;EACd,CAAC;AACH;AAEA,MAAMC,YAAY,GAAG;EACnBf,YAAY,EAAE,IAAI;EAClBE,aAAa,EAAE,KAAK;EACpBE,eAAe,EAAE,KAAK;EACtBE,WAAW,EAAE,IAAI;EACjBE,IAAI,EAAE,IAAI;EACVE,OAAO,EAAE,KAAK;EACdC,YAAY,EAAE,EAAE;EAChBE,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE;AACd,CAAC;AAED,SAASE,OAAOA,CAACC,KAAK,GAACF,YAAY,EAAEG,MAAM,EAAE;EAC3C,IAAIC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,KAAK,CAAC;EACvC,QAAQC,MAAM,CAACnB,IAAI;IACjB,KAAK,iBAAiB;MACpBoB,QAAQ,CAACnB,YAAY,GAAGkB,MAAM,CAAClB,YAAY;MAC3C,OAAOmB,QAAQ;IACjB,KAAK,kBAAkB;MACrBA,QAAQ,CAACjB,aAAa,GAAGgB,MAAM,CAAChB,aAAa;MAC7C,OAAOiB,QAAQ;IACjB,KAAK,oBAAoB;MACvBA,QAAQ,CAACf,eAAe,GAAGc,MAAM,CAACd,eAAe;MACjD,OAAOe,QAAQ;IACjB,KAAK,kBAAkB;MACrBA,QAAQ,CAACb,WAAW,GAAGY,MAAM,CAACZ,WAAW;MACzC,OAAOa,QAAQ;IACjB,KAAK,UAAU;MACbA,QAAQ,CAACX,IAAI,GAAGU,MAAM,CAACV,IAAI;MAC3B,OAAOW,QAAQ;IACjB,KAAK,WAAW;MACd,IAAID,MAAM,CAACR,OAAO,EAAE;QAClBS,QAAQ,CAACR,YAAY,GAAGO,MAAM,CAACP,YAAY;MAC7C;MACAQ,QAAQ,CAACT,OAAO,GAAGQ,MAAM,CAACR,OAAO;MACjC,OAAOS,QAAQ;IACjB,KAAK,aAAa;MAChB,IAAID,MAAM,CAACL,SAAS,EAAE;QACpBM,QAAQ,CAACL,UAAU,GAAGI,MAAM,CAACJ,UAAU;MACzC;MACAK,QAAQ,CAACN,SAAS,GAAGK,MAAM,CAACL,SAAS;MACrC,OAAOM,QAAQ;IACjB;MACE;EACJ;EACA,OAAOF,KAAK;AACd;AAEA,OAAO,MAAMK,KAAK,GAAG1B,cAAc,CAAC;EAAEoB,OAAO,EAAEA;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}