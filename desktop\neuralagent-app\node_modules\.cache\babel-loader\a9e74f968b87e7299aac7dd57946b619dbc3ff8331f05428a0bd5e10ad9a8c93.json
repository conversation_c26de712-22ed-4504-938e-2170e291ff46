{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\views\\\\BackgroundAuth.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  color: var(--secondary-color);\n  height: 100vh;\n  width: 100vw;\n  overflow: hidden;\n`;\n_c = Container;\nconst InstructionBox = styled.div`\n  padding: 1rem 2rem;\n  font-size: 1rem;\n  line-height: 1.6;\n  text-align: center;\n  border-bottom: 1px solid var(--third-color);\n`;\n_c2 = InstructionBox;\nconst Highlight = styled.span`\n  font-weight: 500;\n  color: var(--light-gray-background);\n`;\n_c3 = Highlight;\nconst FrameWrapper = styled.div`\n  flex: 1;\n  iframe {\n    width: 100%;\n    height: 100%;\n    border: none;\n  }\n`;\n_c4 = FrameWrapper;\nexport default function BackgroundAuth() {\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(InstructionBox, {\n      children: [\"Log in to any sites or apps you'd like \", /*#__PURE__*/_jsxDEV(Highlight, {\n        children: \"NeuralAgent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 48\n      }, this), \" to control in the background. Close the window when you finish.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 146\n      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n        style: {\n          opacity: 0.7\n        },\n        children: \"These sessions are stored securely on your computer. You can always do this from App > Background Mode Authentication.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FrameWrapper, {\n      children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n        src: \"http://127.0.0.1:39742/vnc.html?autoconnect=true&bell=off\",\n        title: \"NeuralAgent VNC Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_c5 = BackgroundAuth;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"InstructionBox\");\n$RefreshReg$(_c3, \"Highlight\");\n$RefreshReg$(_c4, \"FrameWrapper\");\n$RefreshReg$(_c5, \"BackgroundAuth\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "Container", "div", "_c", "InstructionBox", "_c2", "Highlight", "span", "_c3", "FrameWrapper", "_c4", "Background<PERSON>uth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "opacity", "src", "title", "_c5", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/views/BackgroundAuth.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  color: var(--secondary-color);\n  height: 100vh;\n  width: 100vw;\n  overflow: hidden;\n`;\n\nconst InstructionBox = styled.div`\n  padding: 1rem 2rem;\n  font-size: 1rem;\n  line-height: 1.6;\n  text-align: center;\n  border-bottom: 1px solid var(--third-color);\n`;\n\nconst Highlight = styled.span`\n  font-weight: 500;\n  color: var(--light-gray-background);\n`;\n\nconst FrameWrapper = styled.div`\n  flex: 1;\n  iframe {\n    width: 100%;\n    height: 100%;\n    border: none;\n  }\n`;\n\nexport default function BackgroundAuth() {\n  return (\n    <Container>\n      <InstructionBox>\n        Log in to any sites or apps you'd like <Highlight>NeuralAgent</Highlight> to control in the background. Close the window when you finish.<br />\n        <small style={{ opacity: 0.7 }}>\n          These sessions are stored securely on your computer. You can always do this from App &gt; Background Mode Authentication.\n        </small>\n      </InstructionBox>\n      <FrameWrapper>\n        <iframe\n          src=\"http://127.0.0.1:39742/vnc.html?autoconnect=true&bell=off\"\n          title=\"NeuralAgent VNC Session\"\n        />\n      </FrameWrapper>\n    </Container>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,SAAS,GAAGH,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,SAAS;AASf,MAAMG,cAAc,GAAGN,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,cAAc;AAQpB,MAAME,SAAS,GAAGR,MAAM,CAACS,IAAI;AAC7B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,SAAS;AAKf,MAAMG,YAAY,GAAGX,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAPID,YAAY;AASlB,eAAe,SAASE,cAAcA,CAAA,EAAG;EACvC,oBACEX,OAAA,CAACC,SAAS;IAAAW,QAAA,gBACRZ,OAAA,CAACI,cAAc;MAAAQ,QAAA,GAAC,yCACyB,eAAAZ,OAAA,CAACM,SAAS;QAAAM,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,oEAAgE,eAAAhB,OAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/IhB,OAAA;QAAOiB,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAI,CAAE;QAAAN,QAAA,EAAC;MAEhC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBhB,OAAA,CAACS,YAAY;MAAAG,QAAA,eACXZ,OAAA;QACEmB,GAAG,EAAC,2DAA2D;QAC/DC,KAAK,EAAC;MAAyB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEhB;AAACK,GAAA,GAjBuBV,cAAc;AAAA,IAAAR,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAnB,EAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}