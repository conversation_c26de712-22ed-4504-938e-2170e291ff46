{"ast": null, "code": "var _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Fragment as _Fragment, jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RedirectTo({\n  linkType,\n  to,\n  redirectType\n}) {\n  _s();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (linkType === 'router') {\n      if (redirectType === 'replace') {\n        navigate(to, {\n          replace: true\n        });\n      } else {\n        navigate(to);\n      }\n    } else {\n      if (redirectType === 'replace') {\n        window.location.replace(to);\n      } else {\n        window.location.href = to;\n      }\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n}\n_s(RedirectTo, \"0pNeyzXk/ByIxyERsdaIrG6js9s=\", false, function () {\n  return [useNavigate];\n});\n_c = RedirectTo;\n;\nexport default RedirectTo;\nvar _c;\n$RefreshReg$(_c, \"RedirectTo\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "RedirectTo", "linkType", "to", "redirectType", "_s", "navigate", "replace", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/RedirectTo.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nfunction RedirectTo({ linkType, to, redirectType }) {\n\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (linkType === 'router') {\n      if (redirectType === 'replace') {\n        navigate(to, { replace: true });\n      } else {\n        navigate(to);\n      }\n    } else {\n      if (redirectType === 'replace') {\n        window.location.replace(to);\n      } else {\n        window.location.href = to;\n      }\n    }\n  }, []);\n\n  return (\n    <></>\n  );\n};\n\nexport default RedirectTo;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,UAAUA,CAAC;EAAEC,QAAQ;EAAEC,EAAE;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAElD,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,IAAIO,QAAQ,KAAK,QAAQ,EAAE;MACzB,IAAIE,YAAY,KAAK,SAAS,EAAE;QAC9BE,QAAQ,CAACH,EAAE,EAAE;UAAEI,OAAO,EAAE;QAAK,CAAC,CAAC;MACjC,CAAC,MAAM;QACLD,QAAQ,CAACH,EAAE,CAAC;MACd;IACF,CAAC,MAAM;MACL,IAAIC,YAAY,KAAK,SAAS,EAAE;QAC9BI,MAAM,CAACC,QAAQ,CAACF,OAAO,CAACJ,EAAE,CAAC;MAC7B,CAAC,MAAM;QACLK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGP,EAAE;MAC3B;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEH,OAAA,CAAAF,SAAA,mBAAI,CAAC;AAET;AAACO,EAAA,CAvBQJ,UAAU;EAAA,QAEAL,WAAW;AAAA;AAAAe,EAAA,GAFrBV,UAAU;AAuBlB;AAED,eAAeA,UAAU;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}