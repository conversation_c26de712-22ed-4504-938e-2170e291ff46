{"ast": null, "code": "import styled from 'styled-components';\nexport const Text = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  color: ${props => props.color ? props.color : '#000'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '400'};\n  text-align: ${props => props.textAlign ? props.textAlign : 'unset'};\n`;", "map": {"version": 3, "names": ["styled", "Text", "div", "props", "fontSize", "color", "fontWeight", "textAlign"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/Typography/index.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Text = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  color: ${props => props.color ? props.color : '#000'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '400'};\n  text-align: ${props => props.textAlign ? props.textAlign : 'unset'};\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,IAAI,GAAGD,MAAM,CAACE,GAAG;AAC9B,eAAeC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,GAAG,MAAM;AAChE,WAAWD,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,MAAM;AACtD,iBAAiBF,KAAK,IAAIA,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACG,UAAU,GAAG,KAAK;AACrE,gBAAgBH,KAAK,IAAIA,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACI,SAAS,GAAG,OAAO;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}