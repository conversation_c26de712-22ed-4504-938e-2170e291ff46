"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TSEnumScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class TSEnumScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scopeManager, ScopeType_1.ScopeType.tsEnum, upperScope, block, false);
    }
}
exports.TSEnumScope = TSEnumScope;
//# sourceMappingURL=TSEnumScope.js.map