{"version": 3, "file": "RuleTester.js", "sourceRoot": "", "sources": ["../../../src/eslint-utils/rule-tester/RuleTester.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA4B;AAC5B,sDAA+D;AAC/D,2CAA6B;AAC7B,+CAAiC;AAKjC,2EAA6D;AAC7D,4CAAyC;AAEzC,mEAA4E;AAE5E,MAAM,gBAAgB,GAAG,2BAA2B,CAAC;AACrD,MAAM,aAAa,GAAG,sFAAsF,gBAAgB,EAAE,CAAC;AAqC/H,SAAS,kBAAkB,CACzB,KAAc;IAId,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,IAAI,IAAI;QACb,MAAM,IAAI,KAAK;QACf,OAAQ,KAAiC,CAAC,IAAI,KAAK,UAAU,CAC9D,CAAC;AACJ,CAAC;AAED,MAAM,UAAW,SAAQ,cAAc,CAAC,UAAU;IAIhD;;;OAGG;IACH,MAAM,KAAK,QAAQ;;QACjB,OAAO,CACL,MAAA,uBAAA,IAAI,gCAAU,mCACd,CAAC,OAAO,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAS,EAAE,GAAE,CAAC,CAAC,CAC7D,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,QAAQ,CAAC,KAA2B;QAC7C,uBAAA,IAAI,MAAa,KAAK,4BAAA,CAAC;IACzB,CAAC;IAED,IAAY,UAAU;QACpB,8EAA8E;QAC9E,OAAO,IAAI,CAAC,WAAgC,CAAC;IAC/C,CAAC;IAED,YAAY,WAA6B;;QACvC,0EAA0E;QAC1E,MAAM,EAAE,qBAAqB,EAAE,CAAC,KAC9B,WAAW,EADwB,wBAAwB,UAC3D,WAAW,EADP,yBAAyD,CAClD,CAAC;QACd,KAAK,iCACA,wBAAwB,KAC3B,aAAa,kCACR,WAAW,CAAC,aAAa,KAC5B,kCAAkC,EAChC,MAAA,MAAA,WAAW,CAAC,aAAa,0CAAE,kCAAkC,mCAC7D,KAAK;YAET,oEAAoE;YACpE,4EAA4E;YAC5E,+CAA+C;YAC/C,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAC3C,CAAC;QAtCI,0CAA+B;QAwCtC,uBAAA,IAAI,2BAAgB,WAAW,MAAA,CAAC;QAEhC,yEAAyE;QACzE,4FAA4F;QAC5F,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE;YAC5B,IAAI;gBACF,iEAAiE;gBACjE,qEAAqE;gBACrE,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAA8B,CAAC;gBACtE,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;YAAC,WAAM;gBACN,qBAAqB;aACtB;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACO,WAAW,CAAC,WAA2B;;QAC7C,MAAM,eAAe,GAAG,IAAA,qBAAS,EAC/B,uBAAA,IAAI,+BAAa,CAAC,aAAa,EAC/B,WAAW,CACK,CAAC;QACnB,MAAM,QAAQ,GAAG,UAAU,CAAA,MAAA,eAAe,CAAC,YAAY,0CAAE,GAAG,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1E,IAAI,eAAe,CAAC,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC,IAAI,CACd,eAAe,CAAC,eAAe,IAAI,IAAI;gBACrC,CAAC,CAAC,eAAe,CAAC,eAAe;gBACjC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EACjB,QAAQ,CACT,CAAC;SACH;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,oEAAoE;IACpE,qFAAqF;IACrF,8BAA8B;IAC9B,GAAG,CACD,IAAY,EACZ,IAAuC,EACvC,aAA8C;QAE9C,IACE,uBAAA,IAAI,+BAAa,CAAC,qBAAqB;YACvC,CAAC,IAAA,yDAAiC,EAChC,uBAAA,IAAI,+BAAa,CAAC,qBAAqB,CACxC,EACD;YACA,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAChD,iFAAiF;gBACjF,sCAAsC;gBACtC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;oBACvC,IAAI,CAAC,UAAU,CAAC,EAAE,CAChB,yEAAyE,EACzE,GAAG,EAAE,GAAE,CAAC,CACT,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,uCAAuC;gBACvC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE;oBAClC,IAAI,CAAC,UAAU,CAAC,EAAE,CAChB,yEAAyE,EACzE,GAAG,EAAE;wBACH,mDAAmD;wBACnD,gBAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC3B,CAAC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;YAED,iEAAiE;YACjE,OAAO;SACR;QAED,MAAM,KAAK,GAAG;YACZ,yCAAyC;YACzC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC5B,OAAO;wBACL,IAAI,EAAE,IAAI;qBACX,CAAC;iBACH;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;YACF,OAAO,EAAE,aAAa,CAAC,OAAO;SAC/B,CAAC;QAEF,0EAA0E;QAC1E,MAAM,gBAAgB,GAAG;YACvB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAChB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;oBAC9B,MAAM,IAAI,CAAC;iBACZ;gBACD,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;oBAChC,MAAM,IAAI,CAAC;iBACZ;YACH,CAAC;SACF,CAAC;QAEF;;;;;;;UAOE;QACF,MAAM,aAAa,GAAG,CAIpB,EAGE,EAAoC,EAAE;gBAHxC,EACA,qBAAqB,EAAE,CAAC,OAEtB,EADC,IAAI,cAFP,yBAGD,CADQ;YAEP,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;aAChC;YACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,uCACK,IAAI,KACP,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,IAC9C;aACH;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QACF,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC7C,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAEjD,MAAM,OAAO,GAAG,CAAC,GAAY,EAAE;YAC7B,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE;gBACnC,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,EAAE,CAAC;QACL,sEAAsE;QACtE,sEAAsE;QACtE,IAAI,CAAC,OAAO,EAAE;YACZ;;cAEE;YACF,MAAM,cAAc,GAAG,CAAC,GAAY,EAAE;gBACpC,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE;oBACnC,IACE,IAAI,CAAC,qBAAqB;wBAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,MAAM,GAAG,CAAC,EAClD;wBACA,OAAO,IAAI,CAAC;qBACb;iBACF;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,EAAE,CAAC;YACL,IAAI,cAAc,EAAE;gBAClB,sEAAsE;gBACtE,IAAI,MAAM,CAAC,SAAS,CAAC,sBAAa,EAAE,UAAU,CAAC,EAAE;oBAC/C;;;;;;;;;sBASE;oBACF,MAAM,eAAe,GAAG,CAKtB,IAAO,EACJ,EAAE;wBACL,uCACK,IAAI,KACP,IAAI,EAAE,IAAA,yDAAiC,EACrC,IAAI,CAAC,qBAAqB,CAC3B,IACD;oBACJ,CAAC,CAAC;oBAEF,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBAC/C,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;iBACpD;qBAAM;oBACL,yEAAyE;oBACzE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACtC,IAAA,yDAAiC,EAAC,IAAI,CAAC,qBAAqB,CAAC,CAC9D,CAAC;oBACF,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC1C,IAAA,yDAAiC,EAAC,IAAI,CAAC,qBAAqB,CAAC,CAC9D,CAAC;iBACH;aACF;SACF;QAED,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;CACF;AAUkB,gCAAU;;AApPpB,wCAAS,CAAuB;AA4OzC;;;GAGG;AACH,SAAS,QAAQ,CAAC,GAAyB,EAAE,GAAG,IAAc;IAC5D,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;AACtC,CAAC;AAEQ,4BAAQ"}