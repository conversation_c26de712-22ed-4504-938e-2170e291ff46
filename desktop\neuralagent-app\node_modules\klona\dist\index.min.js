!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.klona={})}(this,(function(e){e.klona=function e(t){if("object"!=typeof t)return t;var o,r,n=Object.prototype.toString.call(t);if("[object Object]"===n){if(t.constructor!==Object&&"function"==typeof t.constructor)for(o in r=new t.constructor,t)t.hasOwnProperty(o)&&r[o]!==t[o]&&(r[o]=e(t[o]));else for(o in r={},t)"__proto__"===o?Object.defineProperty(r,o,{value:e(t[o]),configurable:!0,enumerable:!0,writable:!0}):r[o]=e(t[o]);return r}if("[object Array]"===n){for(o=t.length,r=Array(o);o--;)r[o]=e(t[o]);return r}return"[object Set]"===n?(r=new Set,t.forEach((function(t){r.add(e(t))})),r):"[object Map]"===n?(r=new Map,t.forEach((function(t,o){r.set(e(o),e(t))})),r):"[object Date]"===n?new Date(+t):"[object RegExp]"===n?((r=new RegExp(t.source,t.flags)).lastIndex=t.lastIndex,r):"[object DataView]"===n?new t.constructor(e(t.buffer)):"[object ArrayBuffer]"===n?t.slice(0):"Array]"===n.slice(-6)?new t.constructor(t):t}}));