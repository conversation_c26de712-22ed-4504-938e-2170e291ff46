{"version": 3, "file": "non-nullable-type-assertion-style.js", "sourceRoot": "", "sources": ["../../src/rules/non-nullable-type-assertion-style.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,mCAAmC;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,sDAAsD;YACnE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,sBAAsB,EACpB,+EAA+E;SAClF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAElB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,MAAM,kBAAkB,GAAG,CAAC,IAAmB,EAAyB,EAAE;YACxE,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CACpC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAC/C,CAAC;YAEF,IACE,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EACpE;gBACA,OAAO,SAAS,CAAC;aAClB;YAED,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,IAAa,EAAW,EAAE;YAChD,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,EAAE;gBAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,OAAO,UAAU,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC;aACzD;iBAAM,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;gBACpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;oBAC7B,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;wBACxB,OAAO,IAAI,CAAC;qBACb;iBACF;gBACD,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,OAAO,CACL,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAClE,CAAC;aACH;QACH,CAAC,CAAC;QAEF,MAAM,sBAAsB,GAAG,CAC7B,aAAwB,EACxB,aAAwB,EACf,EAAE;YACX,MAAM,uBAAuB,GAAG,aAAa,CAAC,MAAM,CAClD,IAAI,CAAC,EAAE,CACL,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CACpE,CAAC;YAEF,IAAI,uBAAuB,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE;gBAC3D,OAAO,KAAK,CAAC;aACd;YAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;gBACxC,IACE,cAAc,CAAC,YAAY,CAAC;oBAC5B,CAAC,uBAAuB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAC/C;oBACA,OAAO,KAAK,CAAC;iBACd;aACF;YAED,KAAK,MAAM,YAAY,IAAI,uBAAuB,EAAE;gBAClD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACzC,OAAO,KAAK,CAAC;iBACd;aACF;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,CACvB,IAAwD,EAC/C,EAAE;YACX,OAAO,CACL,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC3D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC9C,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,iCAAiC,CAC/B,IAAwD;gBAExD,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO;iBACR;gBAED,MAAM,aAAa,GAAG,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,CAAC,aAAa,EAAE;oBAClB,OAAO;iBACR;gBAED,MAAM,aAAa,GAAG,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9D,IAAI,CAAC,aAAa,EAAE;oBAClB,OAAO;iBACR;gBAED,IAAI,sBAAsB,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE;oBACxD,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAC1C,CAAC;wBACJ,CAAC;wBACD,SAAS,EAAE,wBAAwB;wBACnC,IAAI;qBACL,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}