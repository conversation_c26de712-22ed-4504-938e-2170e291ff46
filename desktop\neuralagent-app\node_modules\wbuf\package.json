{"name": "wbuf", "version": "1.7.3", "description": "Write buffer", "main": "index.js", "scripts": {"test": "mocha test/**/*-test.js"}, "repository": {"type": "git", "url": "**************:indutny/wbuf"}, "keywords": ["Write", "<PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/wbuf/issues"}, "homepage": "https://github.com/indutny/wbuf", "devDependencies": {"mocha": "^5.0.4"}, "dependencies": {"minimalistic-assert": "^1.0.0"}}