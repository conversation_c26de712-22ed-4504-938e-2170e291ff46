{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nimport defaults from '../defaults/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */\nexport default function transformData(fns, response) {\n  const config = this || defaults;\n  const context = response || config;\n  const headers = AxiosHeaders.from(context.headers);\n  let data = context.data;\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n  });\n  headers.normalize();\n  return data;\n}", "map": {"version": 3, "names": ["utils", "defaults", "AxiosHeaders", "transformData", "fns", "response", "config", "context", "headers", "from", "data", "for<PERSON>ach", "transform", "fn", "call", "normalize", "status", "undefined"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/axios/lib/core/transformData.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport defaults from '../defaults/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */\nexport default function transformData(fns, response) {\n  const config = this || defaults;\n  const context = response || config;\n  const headers = AxiosHeaders.from(context.headers);\n  let data = context.data;\n\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n  });\n\n  headers.normalize();\n\n  return data;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,YAAY,MAAM,yBAAyB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACnD,MAAMC,MAAM,GAAG,IAAI,IAAIL,QAAQ;EAC/B,MAAMM,OAAO,GAAGF,QAAQ,IAAIC,MAAM;EAClC,MAAME,OAAO,GAAGN,YAAY,CAACO,IAAI,CAACF,OAAO,CAACC,OAAO,CAAC;EAClD,IAAIE,IAAI,GAAGH,OAAO,CAACG,IAAI;EAEvBV,KAAK,CAACW,OAAO,CAACP,GAAG,EAAE,SAASQ,SAASA,CAACC,EAAE,EAAE;IACxCH,IAAI,GAAGG,EAAE,CAACC,IAAI,CAACR,MAAM,EAAEI,IAAI,EAAEF,OAAO,CAACO,SAAS,CAAC,CAAC,EAAEV,QAAQ,GAAGA,QAAQ,CAACW,MAAM,GAAGC,SAAS,CAAC;EAC3F,CAAC,CAAC;EAEFT,OAAO,CAACO,SAAS,CAAC,CAAC;EAEnB,OAAOL,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}