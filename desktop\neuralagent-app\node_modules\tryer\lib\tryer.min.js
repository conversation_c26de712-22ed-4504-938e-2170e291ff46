!function(n){"use strict";function t(r){var n;function t(){i("when",t)&&e()}function i(n,t){return!!r[n]()||(r.count+=1,0<=(u=r).limit&&u.count>=u.limit?r.fail():(i=t,e=function(n){var t=n.interval;n.interval<0&&(n.interval*=2);return t}(r),setTimeout(i,Math.abs(e))),!1);var i,e,u}function e(){var n;if(0===r.action.length)return(n=r.action())&&f(n.then)?n.then(u,u):u();r.action(u)}function u(){i("until",e)&&r.pass()}r={count:0,when:o((n=(n=r)||{}).when),until:o(n.until),action:c(n.action),fail:c(n.fail),pass:c(n.pass),interval:a(n.interval,-1e3),limit:a(n.limit,-1)},t()}function o(n){return u(n,f,i)}function f(n){return"function"==typeof n}function i(){return!0}function c(n){return u(n,f,e)}function e(){}function u(n,t,i){return t(n)?n:i}function a(n,t){return u(n,r,t)}function r(n){return"number"==typeof n&&n==n}"function"==typeof define&&define.amd?define(function(){return t}):"undefined"!=typeof module&&null!==module?module.exports=t:n.tryer=t}(this);