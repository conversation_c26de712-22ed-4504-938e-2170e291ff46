{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.production.js');\n} else {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/use-sync-external-store/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.production.js');\n} else {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,2DAA2D,CAAC;AACvF,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,4DAA4D,CAAC;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}