{"version": 3, "file": "explicit-module-boundary-types.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-module-boundary-types.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oEAAkE;AAElE,oDAA0D;AAE1D,8CAAgC;AAKhC,6EAMyC;AAmBzC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,gCAAgC;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,qGAAqG;YACvG,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,kCAAkC;YACrD,cAAc,EAAE,sCAAsC;YACtD,qBAAqB,EAAE,oCAAoC;YAC3D,WAAW,EAAE,0DAA0D;YACvE,kBAAkB,EAChB,wDAAwD;SAC3D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,kCAAkC,EAAE;wBAClC,WAAW,EACT,iEAAiE;wBACnE,IAAI,EAAE,SAAS;qBAChB;oBACD,yCAAyC,EAAE;wBACzC,WAAW,EAAE;4BACX,kHAAkH;4BAClH,qDAAqD;yBACtD,CAAC,IAAI,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,gGAAgG;wBAClG,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;wBACD,IAAI,EAAE,OAAO;qBACd;oBACD,yBAAyB,EAAE;wBACzB,WAAW,EAAE;4BACX,2GAA2G;4BAC3G,qDAAqD;yBACtD,CAAC,IAAI,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,SAAS;qBAChB;oBACD,6BAA6B,EAAE;wBAC7B,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,2CAA2C;oBAC3C,qBAAqB,EAAE;wBACrB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,kCAAkC,EAAE,KAAK;YACzC,yCAAyC,EAAE,IAAI;YAC/C,YAAY,EAAE,EAAE;YAChB,yBAAyB,EAAE,IAAI;YAC/B,6BAA6B,EAAE,IAAI;SACpC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,oDAAoD;QACpD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAgB,CAAC;QAEjD,qDAAqD;QACrD,MAAM,cAAc,GAAmB,EAAE,CAAC;QAE1C,qEAAqE;QACrE,6CAA6C;QAC7C,MAAM,cAAc,GAAG,IAAI,GAAG,EAAiB,CAAC;QAEhD;;;;;;;;;UASE;QAEF,OAAO;YACL,wBAAwB,CAAC,IAAI;gBAC3B,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC;YACD,sCAAsC,CACpC,IAAqC;gBAErC,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC7B;qBAAM;oBACL,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;wBACvC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;qBAClC;iBACF;YACH,CAAC;YACD,kBAAkB,CAAC,IAAI;gBACrB,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC;YACD,kEAAkE,CAChE,IAAkB;gBAElB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;YACD,cAAc;gBACZ,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;oBACjC,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE;wBACvC,SAAS,CAAC,IAAI,CAAC,CAAC;qBACjB;iBACF;YACH,CAAC;SACF,CAAC;QAEF,SAAS,eAAe,CACtB,IAA2D;YAE3D,SAAS,cAAc,CAAC,KAAyB;gBAC/C,SAAS,MAAM,CACb,cAA0B,EAC1B,gBAA4B;oBAE5B,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;wBAC5C,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,cAAc;4BACzB,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;yBAC3B,CAAC,CAAC;qBACJ;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE;wBACrD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,gBAAgB;4BAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE;yBAChC,CAAC,CAAC;qBACJ;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE;wBACtD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,gBAAgB;4BAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;yBACjC,CAAC,CAAC;qBACJ;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE;wBACpD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;4BACrD,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,KAAK;gCACX,SAAS,EAAE,cAAc;gCACzB,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE;6BACpC,CAAC,CAAC;yBACJ;6BAAM;4BACL,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,KAAK;gCACX,SAAS,EAAE,gBAAgB;gCAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;6BACvB,CAAC,CAAC;yBACJ;qBACF;gBACH,CAAC;gBAED,QAAQ,KAAK,CAAC,IAAI,EAAE;oBAClB,KAAK,sBAAc,CAAC,YAAY,CAAC;oBACjC,KAAK,sBAAc,CAAC,UAAU,CAAC;oBAC/B,KAAK,sBAAc,CAAC,aAAa,CAAC;oBAClC,KAAK,sBAAc,CAAC,WAAW;wBAC7B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;4BACzB,MAAM,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;yBACnD;6BAAM,IACL,OAAO,CAAC,kCAAkC,KAAK,IAAI;4BACnD,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI;gCACtC,sBAAc,CAAC,YAAY,EAC7B;4BACA,MAAM,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;yBAC7C;wBACD,OAAO;oBAET,KAAK,sBAAc,CAAC,mBAAmB;wBACrC,OAAO,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBAEzC,KAAK,sBAAc,CAAC,iBAAiB,EAAE,8CAA8C;wBACnF,OAAO;iBACV;YACH,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC7B,cAAc,CAAC,GAAG,CAAC,CAAC;aACrB;QACH,CAAC;QAED;;WAEG;QACH,SAAS,aAAa,CAAC,IAA+B;;YACpD,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE;gBAClE,OAAO,KAAK,CAAC;aACd;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAChD;gBACA,OAAO,CACL,CAAA,MAAA,IAAI,CAAC,EAAE,0CAAE,IAAI,MAAK,sBAAc,CAAC,UAAU;oBAC3C,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAC5C,CAAC;aACH;iBAAM,IACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;gBACvD,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;gBACtD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C;gBACA,IACE,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ,EAClC;oBACA,OAAO,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBACtD;gBACD,IACE,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBAChD,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EACjC;oBACA,OAAO,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACpE;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;oBACjE,OAAO,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBACrD;aACF;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,6BAA6B,CAAC,IAAkB;;YACvD,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,OAAO,OAAO,EAAE;gBACd,IAAI,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;oBACnD,kFAAkF;oBAClF,OAAO,GAAG,MAAA,OAAO,CAAC,MAAM,0CAAE,MAAM,CAAC;oBACjC,SAAS;iBACV;gBAED,IACE,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;oBACzB,CAAC,IAAA,iEAAuC,EAAC,OAAO,CAAC,EACjD;oBACA,OAAO,KAAK,CAAC;iBACd;gBAED,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACjC,OAAO,IAAI,CAAC;iBACb;gBAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;aAC1B;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,IAAyB;YAChD,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,wBAAwB,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtC,OAAO;aACR;YAED,+BAA+B;YAC/B,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,IAAI,EAAE;gBACtC,yCAAyC;gBACzC,IACE;oBACE,8BAAc,CAAC,sBAAsB;oBACrC,8BAAc,CAAC,aAAa;oBAC5B,8BAAc,CAAC,WAAW;oBAC1B,8BAAc,CAAC,SAAS;iBACzB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAC3B;oBACA,SAAS;iBACV;gBAED,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aAC5B;YAED,mDAAmD;YACnD,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC3C;gBACE,6FAA6F;gBAC7F,CAAC,SAAS,CAAC,IAAI;oBACf,SAAS,CAAC,SAAS,EACnB;oBACA,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;iBAChC;aACF;QACH,CAAC;QAED,SAAS,SAAS,CAAC,IAA0B;YAC3C,IAAI,IAAI,IAAI,IAAI,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC5C,OAAO;aACR;YACD,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEzB,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,sBAAc,CAAC,uBAAuB,CAAC;gBAC5C,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAEvC,KAAK,sBAAc,CAAC,eAAe;oBACjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACnC,SAAS,CAAC,OAAO,CAAC,CAAC;qBACpB;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,IACE,IAAI,CAAC,aAAa,KAAK,SAAS;wBAChC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAClD;wBACA,OAAO;qBACR;oBACD,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,sBAAc,CAAC,eAAe;oBACjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;wBACpC,SAAS,CAAC,OAAO,CAAC,CAAC;qBACpB;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,mBAAmB;oBACrC,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;gBAE7B,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,sBAAc,CAAC,0BAA0B;oBAC5C,IACE,IAAI,CAAC,aAAa,KAAK,SAAS;wBAChC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAClD;wBACA,OAAO;qBACR;oBACD,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,UAAU;oBAC5B,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;wBACtC,SAAS,CAAC,QAAQ,CAAC,CAAC;qBACrB;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,QAAQ;oBAC1B,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,6BAA6B;oBAC/C,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAC;gBAEhD,KAAK,sBAAc,CAAC,mBAAmB;oBACrC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE;wBAC3C,SAAS,CAAC,WAAW,CAAC,CAAC;qBACxB;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC/B;QACH,CAAC;QAED,SAAS,gCAAgC,CACvC,IAA4C;;YAE5C,MAAM,aAAa,GACjB,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB;gBACrD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC;YACrC,MAAM,aAAa,GACjB,CAAC,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,0BAA0B;gBAC9D,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACxD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC;YAC7B,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACxD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;aACJ;YAED,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,SAAS,uBAAuB,CAAC,IAAwB;YACvD,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC9B,OAAO;aACR;YACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE3B,IACE,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC1B,IAAA,mDAAyB,EAAC,IAAI,EAAE,OAAO,CAAC;gBACxC,IAAA,+CAAqB,EAAC,IAAI,CAAC,EAC3B;gBACA,OAAO;aACR;YAED,IAAA,2DAAiC,EAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE;gBACjE,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,SAAS,aAAa,CAAC,IAAkC;YACvD,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC9B,OAAO;aACR;YACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE3B,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,IAAA,+CAAqB,EAAC,IAAI,CAAC,EAAE;gBACtD,OAAO;aACR;YAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE;gBACvD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}