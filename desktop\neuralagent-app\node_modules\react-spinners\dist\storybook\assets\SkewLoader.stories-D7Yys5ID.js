import{j as m}from"./jsx-runtime-QvZ8i92b.js";import{c as f,a as t}from"./animation-CmKoZPRv.js";import"./index-uubelm5h.js";const g=f("SkewLoader","25% {transform: perspective(100px) rotateX(180deg) rotateY(0)} 50% {transform: perspective(100px) rotateX(180deg) rotateY(180deg)} 75% {transform: perspective(100px) rotateX(0) rotateY(180deg)} 100% {transform: perspective(100px) rotateX(0) rotateY(0)}","skew");function a({loading:o=!0,color:p="#000000",speedMultiplier:d=1,cssOverride:l={},size:r=20,...c}){const u={width:"0",height:"0",borderLeft:`${t(r)} solid transparent`,borderRight:`${t(r)} solid transparent`,borderBottom:`${t(r)} solid ${p}`,display:"inline-block",animation:`${g} ${3/d}s 0s infinite cubic-bezier(0.09, 0.57, 0.49, 0.9)`,animationFillMode:"both",...l};return o?m.jsx("span",{style:u,...c}):null}try{a.displayName="SkewLoader",a.__docgenInfo={description:"",displayName:"SkewLoader",props:{size:{defaultValue:{value:"20"},description:"",name:"size",required:!1,type:{name:"LengthType"}},color:{defaultValue:{value:"#000000"},description:"",name:"color",required:!1,type:{name:"string"}},loading:{defaultValue:{value:"true"},description:"",name:"loading",required:!1,type:{name:"boolean"}},cssOverride:{defaultValue:{value:"{}"},description:"",name:"cssOverride",required:!1,type:{name:"CSSProperties"}},speedMultiplier:{defaultValue:{value:"1"},description:"",name:"speedMultiplier",required:!1,type:{name:"number"}}}}}catch{}const x={component:a,argTypes:{size:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}}}},e={};var s,i,n;e.parameters={...e.parameters,docs:{...(s=e.parameters)==null?void 0:s.docs,source:{originalSource:"{}",...(n=(i=e.parameters)==null?void 0:i.docs)==null?void 0:n.source}}};const _=["Primary"];export{e as Primary,_ as __namedExportsOrder,x as default};
