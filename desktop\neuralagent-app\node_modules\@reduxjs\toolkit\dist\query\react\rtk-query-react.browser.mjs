import{build<PERSON><PERSON><PERSON><PERSON> as Ke,coreModule as Ve}from"@reduxjs/toolkit/query";import"@reduxjs/toolkit";import{batch as Ee,useDispatch as ke,useSelector as Me,useS<PERSON> as Oe}from"react-redux";import{createSelector as Fe}from"reselect";function q(e){return e.replace(e[0],e[0].toUpperCase())}function se(e){return e.type==="query"}function ue(e){return e.type==="mutation"}function _(e){return e.type==="infinitequery"}function L(e,...c){return Object.assign(e,...c)}import{formatProdErrorMessage as xe}from"@reduxjs/toolkit";import{defaultSerializeQueryArgs as me,QueryStatus as Se,skipToken as N}from"@reduxjs/toolkit/query";import{useCallback as C,useDebugValue as te,useEffect as w,useLayoutEffect as Ae,useMemo as x,useRef as O,useState as ye}from"react";import{shallowEqual as ne}from"react-redux";var K=Symbol();import{useEffect as le,useRef as ge,useMemo as Te}from"react";function ee(e,c,R,l){let P=Te(()=>({queryArgs:e,serialized:typeof e=="object"?c({queryArgs:e,endpointDefinition:R,endpointName:l}):e}),[e,c,R,l]),g=ge(P);return le(()=>{g.current.serialized!==P.serialized&&(g.current=P)},[P]),g.current.serialized===P.serialized?g.current.queryArgs:e}import{useEffect as Re,useRef as De}from"react";import{shallowEqual as ae}from"react-redux";function V(e){let c=De(e);return Re(()=>{ae(c.current,e)||(c.current=e)},[e]),ae(c.current,e)?c.current:e}var Pe=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Be=Pe(),Ie=()=>typeof navigator<"u"&&navigator.product==="ReactNative",Ue=Ie(),he=()=>Be||Ue?Ae:w,be=he(),oe=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:Se.pending}:e;function re(e,...c){let R={};return c.forEach(l=>{R[l]=e[l]}),R}var ie=["data","status","isLoading","isSuccess","isError","error"];function pe({api:e,moduleOptions:{batch:c,hooks:{useDispatch:R,useSelector:l,useStore:P},unstable__sideEffectsInRender:g,createSelector:F},serializeQueryArgs:U,context:B}){let h=g?t=>t():w;return{buildQueryHooks:H,buildInfiniteQueryHooks:Z,buildMutationHook:Y,usePrefetch:G};function W(t,i,p){if(i?.endpointName&&t.isUninitialized){let{endpointName:a}=i,y=B.endpointDefinitions[a];p!==N&&U({queryArgs:i.originalArgs,endpointDefinition:y,endpointName:a})===U({queryArgs:p,endpointDefinition:y,endpointName:a})&&(i=void 0)}let s=t.isSuccess?t.data:i?.data;s===void 0&&(s=t.data);let u=s!==void 0,n=t.isLoading,r=(!i||i.isLoading||i.isUninitialized)&&!u&&n,o=t.isSuccess||u&&(n&&!i?.isError||t.isUninitialized);return{...t,data:s,currentData:t.data,isFetching:n,isLoading:r,isSuccess:o}}function $(t,i,p){if(i?.endpointName&&t.isUninitialized){let{endpointName:a}=i,y=B.endpointDefinitions[a];U({queryArgs:i.originalArgs,endpointDefinition:y,endpointName:a})===U({queryArgs:p,endpointDefinition:y,endpointName:a})&&(i=void 0)}let s=t.isSuccess?t.data:i?.data;s===void 0&&(s=t.data);let u=s!==void 0,n=t.isLoading,r=(!i||i.isLoading||i.isUninitialized)&&!u&&n,o=t.isSuccess||n&&u;return{...t,data:s,currentData:t.data,isFetching:n,isLoading:r,isSuccess:o}}function G(t,i){let p=R(),s=V(i);return C((u,n)=>p(e.util.prefetch(t,u,{...s,...n})),[t,p,s])}function D(t,i,{refetchOnReconnect:p,refetchOnFocus:s,refetchOnMountOrArgChange:u,skip:n=!1,pollingInterval:r=0,skipPollingIfUnfocused:o=!1,...a}={}){let{initiate:y}=e.endpoints[t],Q=R(),m=O(void 0);if(!m.current){let M=Q(e.internalActions.internal_getRTKQSubscriptions());m.current=M}let f=ee(n?N:i,me,B.endpointDefinitions[t],t),d=V({refetchOnReconnect:p,refetchOnFocus:s,pollingInterval:r,skipPollingIfUnfocused:o}),S=O(!1),I=a.initialPageParam,b=V(I),T=O(void 0),{queryCacheKey:k,requestId:z}=T.current||{},J=!1;k&&z&&(J=m.current.isRequestSubscribed(k,z));let X=!J&&S.current;return h(()=>{S.current=J}),h(()=>{X&&(T.current=void 0)},[X]),h(()=>{let M=T.current;if(typeof process<"u",f===N){M?.unsubscribe(),T.current=void 0;return}let de=T.current?.subscriptionOptions;if(!M||M.arg!==f){M?.unsubscribe();let ce=Q(y(f,{subscriptionOptions:d,forceRefetch:u,..._(B.endpointDefinitions[t])?{initialPageParam:b}:{}}));T.current=ce}else d!==de&&M.updateSubscriptionOptions(d)},[Q,y,u,f,d,X,b,t]),[T,Q,y,d]}function v(t,i){return(s,{skip:u=!1,selectFromResult:n}={})=>{let{select:r}=e.endpoints[t],o=ee(u?N:s,U,B.endpointDefinitions[t],t),a=O(void 0),y=x(()=>F([r(o),(S,I)=>I,S=>o],i,{memoizeOptions:{resultEqualityCheck:ne}}),[r,o]),Q=x(()=>n?F([y],n,{devModeChecks:{identityFunctionCheck:"never"}}):y,[y,n]),m=l(S=>Q(S,a.current),ne),f=P(),d=y(f.getState(),a.current);return be(()=>{a.current=d},[d]),m}}function A(t){w(()=>()=>{t.current?.unsubscribe?.(),t.current=void 0},[t])}function E(t){if(!t.current)throw new Error(xe(38));return t.current.refetch()}function H(t){let i=(u,n={})=>{let[r]=D(t,u,n);return A(r),x(()=>({refetch:()=>E(r)}),[r])},p=({refetchOnReconnect:u,refetchOnFocus:n,pollingInterval:r=0,skipPollingIfUnfocused:o=!1}={})=>{let{initiate:a}=e.endpoints[t],y=R(),[Q,m]=ye(K),f=O(void 0),d=V({refetchOnReconnect:u,refetchOnFocus:n,pollingInterval:r,skipPollingIfUnfocused:o});h(()=>{let T=f.current?.subscriptionOptions;d!==T&&f.current?.updateSubscriptionOptions(d)},[d]);let S=O(d);h(()=>{S.current=d},[d]);let I=C(function(T,k=!1){let z;return c(()=>{f.current?.unsubscribe(),f.current=z=y(a(T,{subscriptionOptions:S.current,forceRefetch:!k})),m(T)}),z},[y,a]),b=C(()=>{f.current?.queryCacheKey&&y(e.internalActions.removeQueryResult({queryCacheKey:f.current?.queryCacheKey}))},[y]);return w(()=>()=>{f?.current?.unsubscribe()},[]),w(()=>{Q!==K&&!f.current&&I(Q,!0)},[Q,I]),x(()=>[I,Q,{reset:b}],[I,Q,b])},s=v(t,W);return{useQueryState:s,useQuerySubscription:i,useLazyQuerySubscription:p,useLazyQuery(u){let[n,r,{reset:o}]=p(u),a=s(r,{...u,skip:r===K}),y=x(()=>({lastArg:r}),[r]);return x(()=>[n,{...a,reset:o},y],[n,a,o,y])},useQuery(u,n){let r=i(u,n),o=s(u,{selectFromResult:u===N||n?.skip?void 0:oe,...n}),a=re(o,...ie);return te(a),x(()=>({...o,...r}),[o,r])}}}function Z(t){let i=(s,u={})=>{let[n,r,o,a]=D(t,s,u),y=O(a);h(()=>{y.current=a},[a]);let Q=C(function(m,f){let d;return c(()=>{n.current?.unsubscribe(),n.current=d=r(o(m,{subscriptionOptions:y.current,direction:f}))}),d},[n,r,o]);return A(n),x(()=>({trigger:Q,refetch:()=>E(n),fetchNextPage:()=>Q(s,"forward"),fetchPreviousPage:()=>Q(s,"backward")}),[n,Q,s])},p=v(t,$);return{useInfiniteQueryState:p,useInfiniteQuerySubscription:i,useInfiniteQuery(s,u){let{refetch:n,fetchNextPage:r,fetchPreviousPage:o}=i(s,u),a=p(s,{selectFromResult:s===N||u?.skip?void 0:oe,...u}),y=re(a,...ie,"hasNextPage","hasPreviousPage");return te(y),x(()=>({...a,fetchNextPage:r,fetchPreviousPage:o,refetch:n}),[a,r,o,n])}}}function Y(t){return({selectFromResult:i,fixedCacheKey:p}={})=>{let{select:s,initiate:u}=e.endpoints[t],n=R(),[r,o]=ye();w(()=>()=>{r?.arg.fixedCacheKey||r?.reset()},[r]);let a=C(function(T){let k=n(u(T,{fixedCacheKey:p}));return o(k),k},[n,u,p]),{requestId:y}=r||{},Q=x(()=>s({fixedCacheKey:p,requestId:r?.requestId}),[p,r,s]),m=x(()=>i?F([Q],i):Q,[i,Q]),f=l(m,ne),d=p==null?r?.arg.originalArgs:void 0,S=C(()=>{c(()=>{r&&o(void 0),p&&n(e.internalActions.removeMutationResult({requestId:y,fixedCacheKey:p}))})},[n,p,r,y]),I=re(f,...ie,"endpointName");te(I);let b=x(()=>({...f,originalArgs:d,reset:S}),[f,d,S]);return x(()=>[a,b],[a,b])}}}var fe=Symbol(),Qe=({batch:e=Ee,hooks:c={useDispatch:ke,useSelector:Me,useStore:Oe},createSelector:R=Fe,unstable__sideEffectsInRender:l=!1,...P}={})=>({name:fe,init(g,{serializeQueryArgs:F},U){let B=g,{buildQueryHooks:h,buildInfiniteQueryHooks:W,buildMutationHook:$,usePrefetch:G}=pe({api:g,moduleOptions:{batch:e,hooks:c,unstable__sideEffectsInRender:l,createSelector:R},serializeQueryArgs:F,context:U});return L(B,{usePrefetch:G}),L(U,{batch:e}),{injectEndpoint(D,v){if(se(v)){let{useQuery:A,useLazyQuery:E,useLazyQuerySubscription:H,useQueryState:Z,useQuerySubscription:Y}=h(D);L(B.endpoints[D],{useQuery:A,useLazyQuery:E,useLazyQuerySubscription:H,useQueryState:Z,useQuerySubscription:Y}),g[`use${q(D)}Query`]=A,g[`useLazy${q(D)}Query`]=E}if(ue(v)){let A=$(D);L(B.endpoints[D],{useMutation:A}),g[`use${q(D)}Mutation`]=A}else if(_(v)){let{useInfiniteQuery:A,useInfiniteQuerySubscription:E,useInfiniteQueryState:H}=W(D);L(B.endpoints[D],{useInfiniteQuery:A,useInfiniteQuerySubscription:E,useInfiniteQueryState:H}),g[`use${q(D)}InfiniteQuery`]=A}}}}});export*from"@reduxjs/toolkit/query";import{configureStore as ve,formatProdErrorMessage as Le}from"@reduxjs/toolkit";import{useContext as Ne}from"react";import{useEffect as Ce}from"react";import*as j from"react";import{Provider as we,ReactReduxContext as He}from"react-redux";import{setupListeners as ze}from"@reduxjs/toolkit/query";function qe(e){let c=e.context||He;if(Ne(c))throw new Error(Le(35));let[l]=j.useState(()=>ve({reducer:{[e.api.reducerPath]:e.api.reducer},middleware:P=>P().concat(e.api.middleware)}));return Ce(()=>e.setupListeners===!1?void 0:ze(l.dispatch,e.setupListeners),[e.setupListeners,l.dispatch]),j.createElement(we,{store:l,context:c},e.children)}var ht=Ke(Ve(),Qe());export{qe as ApiProvider,K as UNINITIALIZED_VALUE,ht as createApi,Qe as reactHooksModule,fe as reactHooksModuleName};
//# sourceMappingURL=rtk-query-react.browser.mjs.map