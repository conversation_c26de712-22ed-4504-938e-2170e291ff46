{"ast": null, "code": "import styled from 'styled-components';\nexport const MessageBarContainer = styled.div`\n  position: fixed;\n  top: 80px;\n  z-index: 2000;\n  padding: 20px 50px;\n  background-color: ${props => props.backgroundColor ? props.backgroundColor : 'green'};\n  left: 50%;\n  transform: translateX(-50%);\n  width: 100%;\n  color: #fff;\n  font-weight: 500;\n  border-radius: 6px;\n  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);\n  transition: 0.2s ease-in-out;\n  max-width: 700px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n`;", "map": {"version": 3, "names": ["styled", "MessageBarContainer", "div", "props", "backgroundColor"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/MessageBar/MessageBarElements.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const MessageBarContainer = styled.div`\n  position: fixed;\n  top: 80px;\n  z-index: 2000;\n  padding: 20px 50px;\n  background-color: ${props => props.backgroundColor ? props.backgroundColor : 'green'};\n  left: 50%;\n  transform: translateX(-50%);\n  width: 100%;\n  color: #fff;\n  font-weight: 500;\n  border-radius: 6px;\n  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);\n  transition: 0.2s ease-in-out;\n  max-width: 700px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,mBAAmB,GAAGD,MAAM,CAACE,GAAG;AAC7C;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,eAAe,GAAGD,KAAK,CAACC,eAAe,GAAG,OAAO;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}