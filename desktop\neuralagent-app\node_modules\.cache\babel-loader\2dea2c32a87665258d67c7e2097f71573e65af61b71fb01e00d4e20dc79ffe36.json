{"ast": null, "code": "export const EMAIL_REGEX = /^(([^<>()[\\]\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@(([^<>()[\\]\\.,;:\\s@\\\"]+\\.)+[^<>()[\\]\\.,;:\\s@\\\"]{2,})$/i;", "map": {"version": 3, "names": ["EMAIL_REGEX"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/utils/regex.js"], "sourcesContent": ["export const EMAIL_REGEX = \n/^(([^<>()[\\]\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@(([^<>()[\\]\\.,;:\\s@\\\"]+\\.)+[^<>()[\\]\\.,;:\\s@\\\"]{2,})$/i;\n\n\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GACxB,sHAAsH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}