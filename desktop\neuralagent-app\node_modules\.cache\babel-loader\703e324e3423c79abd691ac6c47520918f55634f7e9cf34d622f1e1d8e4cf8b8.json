{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\Elements\\\\TextAreas\\\\index.js\";\nimport React from 'react';\nimport { LabeledTAContainer, VerticalLabeledTAContainer, TextAreaLabel, TextArea, TextAreaError } from './Elements';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NATextArea = ({\n  label = null,\n  verticalLabel = false,\n  fontSize = '16px',\n  labelFontWeight = '500',\n  error = null,\n  background = '#fff',\n  outlined = false,\n  padding = null,\n  borderRadius = null,\n  placeholder = null,\n  value = null,\n  isDarkMode = false,\n  onChange,\n  rows = '4',\n  onKeyDown\n}) => {\n  if (label !== null) {\n    if (!verticalLabel) {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(LabeledTAContainer, {\n          children: [/*#__PURE__*/_jsxDEV(TextAreaLabel, {\n            fontSize: fontSize,\n            fontWeight: labelFontWeight,\n            isDarkMode: isDarkMode,\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1 1 75%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextArea, {\n              background: background,\n              fontSize: fontSize,\n              placeholder: placeholder,\n              padding: padding,\n              rows: rows,\n              cols: \"50\",\n              isDarkMode: isDarkMode,\n              onKeyDown: onKeyDown,\n              borderRadius: borderRadius,\n              outlined: outlined,\n              value: value,\n              onChange: onChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), error !== null ? /*#__PURE__*/_jsxDEV(TextAreaError, {\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false);\n    } else {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(VerticalLabeledTAContainer, {\n          children: [/*#__PURE__*/_jsxDEV(TextAreaLabel, {\n            verticalLabel: true,\n            fontSize: fontSize,\n            fontWeight: labelFontWeight,\n            isDarkMode: isDarkMode,\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n            background: background,\n            fontSize: fontSize,\n            placeholder: placeholder,\n            padding: padding,\n            rows: rows,\n            cols: \"50\",\n            isDarkMode: isDarkMode,\n            onKeyDown: onKeyDown,\n            borderRadius: borderRadius,\n            outlined: outlined,\n            value: value,\n            onChange: onChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), error !== null ? /*#__PURE__*/_jsxDEV(TextAreaError, {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(TextArea, {\n      background: background,\n      fontSize: fontSize,\n      placeholder: placeholder,\n      padding: padding,\n      rows: rows,\n      cols: \"50\",\n      isDarkMode: isDarkMode,\n      onKeyDown: onKeyDown,\n      borderRadius: borderRadius,\n      outlined: outlined,\n      value: value,\n      onChange: onChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextAreaError, {\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = NATextArea;\nexport default NATextArea;\nvar _c;\n$RefreshReg$(_c, \"NATextArea\");", "map": {"version": 3, "names": ["React", "LabeledTAContainer", "VerticalLabeledTAContainer", "TextAreaLabel", "TextArea", "TextAreaError", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NATextArea", "label", "verticalLabel", "fontSize", "labelFontWeight", "error", "background", "outlined", "padding", "borderRadius", "placeholder", "value", "isDarkMode", "onChange", "rows", "onKeyDown", "children", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "flex", "cols", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/TextAreas/index.js"], "sourcesContent": ["import React from 'react';\nimport {\n  LabeledTAContainer,\n  VerticalLabeledTAContainer,\n  TextAreaLabel,\n  TextArea,\n  TextAreaError\n} from './Elements';\n\nconst NATextArea = ({\n  label=null,\n  verticalLabel=false,\n  fontSize='16px',\n  labelFontWeight='500',\n  error=null,\n  background='#fff',\n  outlined=false,\n  padding=null,\n  borderRadius=null,\n  placeholder=null,\n  value=null,\n  isDarkMode=false,\n  onChange,\n  rows='4',\n  onKeyDown\n}) => {\n  if (label !== null) {\n    if (!verticalLabel) {\n      return (\n        <>\n          <LabeledTAContainer>\n            <TextAreaLabel fontSize={fontSize} fontWeight={labelFontWeight} isDarkMode={isDarkMode}>\n              {label}\n            </TextAreaLabel>\n            <div style={{flex: '1 1 75%'}}>\n              <TextArea background={background} fontSize={fontSize} placeholder={placeholder} padding={padding}\n                rows={rows} cols=\"50\"\n                isDarkMode={isDarkMode}\n                onKeyDown={onKeyDown}\n                borderRadius={borderRadius} outlined={outlined} value={value} onChange={onChange} />\n              {\n                error !== null ?\n                <TextAreaError>\n                  {error}\n                </TextAreaError> :\n                <></>\n              }\n            </div>\n          </LabeledTAContainer>\n        </>\n      );\n    } else {\n      return (\n        <>\n          <VerticalLabeledTAContainer>\n            <TextAreaLabel verticalLabel fontSize={fontSize} fontWeight={labelFontWeight} isDarkMode={isDarkMode}>\n              {label}\n            </TextAreaLabel>\n            <TextArea background={background} fontSize={fontSize} placeholder={placeholder} padding={padding}\n              rows={rows} cols=\"50\"\n              isDarkMode={isDarkMode}\n              onKeyDown={onKeyDown}\n              borderRadius={borderRadius} outlined={outlined} value={value} onChange={onChange} />\n            {\n              error !== null ?\n              <TextAreaError>\n                {error}\n              </TextAreaError> :\n              <></>\n            }\n          </VerticalLabeledTAContainer>\n        </>\n      );\n    }\n  }\n  return (\n    <>\n      <TextArea background={background} fontSize={fontSize} placeholder={placeholder} padding={padding}\n        rows={rows} cols=\"50\"\n        isDarkMode={isDarkMode}\n        onKeyDown={onKeyDown}\n        borderRadius={borderRadius} outlined={outlined} value={value} onChange={onChange} />\n      <TextAreaError>\n        {error}\n      </TextAreaError>\n    </>\n  );\n};\n\nexport default NATextArea;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,kBAAkB,EAClBC,0BAA0B,EAC1BC,aAAa,EACbC,QAAQ,EACRC,aAAa,QACR,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpB,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK,GAAC,IAAI;EACVC,aAAa,GAAC,KAAK;EACnBC,QAAQ,GAAC,MAAM;EACfC,eAAe,GAAC,KAAK;EACrBC,KAAK,GAAC,IAAI;EACVC,UAAU,GAAC,MAAM;EACjBC,QAAQ,GAAC,KAAK;EACdC,OAAO,GAAC,IAAI;EACZC,YAAY,GAAC,IAAI;EACjBC,WAAW,GAAC,IAAI;EAChBC,KAAK,GAAC,IAAI;EACVC,UAAU,GAAC,KAAK;EAChBC,QAAQ;EACRC,IAAI,GAAC,GAAG;EACRC;AACF,CAAC,KAAK;EACJ,IAAId,KAAK,KAAK,IAAI,EAAE;IAClB,IAAI,CAACC,aAAa,EAAE;MAClB,oBACEL,OAAA,CAAAE,SAAA;QAAAiB,QAAA,eACEnB,OAAA,CAACN,kBAAkB;UAAAyB,QAAA,gBACjBnB,OAAA,CAACJ,aAAa;YAACU,QAAQ,EAAEA,QAAS;YAACc,UAAU,EAAEb,eAAgB;YAACQ,UAAU,EAAEA,UAAW;YAAAI,QAAA,EACpFf;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAChBxB,OAAA;YAAKyB,KAAK,EAAE;cAACC,IAAI,EAAE;YAAS,CAAE;YAAAP,QAAA,gBAC5BnB,OAAA,CAACH,QAAQ;cAACY,UAAU,EAAEA,UAAW;cAACH,QAAQ,EAAEA,QAAS;cAACO,WAAW,EAAEA,WAAY;cAACF,OAAO,EAAEA,OAAQ;cAC/FM,IAAI,EAAEA,IAAK;cAACU,IAAI,EAAC,IAAI;cACrBZ,UAAU,EAAEA,UAAW;cACvBG,SAAS,EAAEA,SAAU;cACrBN,YAAY,EAAEA,YAAa;cAACF,QAAQ,EAAEA,QAAS;cAACI,KAAK,EAAEA,KAAM;cAACE,QAAQ,EAAEA;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAEpFhB,KAAK,KAAK,IAAI,gBACdR,OAAA,CAACF,aAAa;cAAAqB,QAAA,EACXX;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,gBAChBxB,OAAA,CAAAE,SAAA,mBAAI,CAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC,gBACrB,CAAC;IAEP,CAAC,MAAM;MACL,oBACExB,OAAA,CAAAE,SAAA;QAAAiB,QAAA,eACEnB,OAAA,CAACL,0BAA0B;UAAAwB,QAAA,gBACzBnB,OAAA,CAACJ,aAAa;YAACS,aAAa;YAACC,QAAQ,EAAEA,QAAS;YAACc,UAAU,EAAEb,eAAgB;YAACQ,UAAU,EAAEA,UAAW;YAAAI,QAAA,EAClGf;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAChBxB,OAAA,CAACH,QAAQ;YAACY,UAAU,EAAEA,UAAW;YAACH,QAAQ,EAAEA,QAAS;YAACO,WAAW,EAAEA,WAAY;YAACF,OAAO,EAAEA,OAAQ;YAC/FM,IAAI,EAAEA,IAAK;YAACU,IAAI,EAAC,IAAI;YACrBZ,UAAU,EAAEA,UAAW;YACvBG,SAAS,EAAEA,SAAU;YACrBN,YAAY,EAAEA,YAAa;YAACF,QAAQ,EAAEA,QAAS;YAACI,KAAK,EAAEA,KAAM;YAACE,QAAQ,EAAEA;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAEpFhB,KAAK,KAAK,IAAI,gBACdR,OAAA,CAACF,aAAa;YAAAqB,QAAA,EACXX;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,gBAChBxB,OAAA,CAAAE,SAAA,mBAAI,CAAC;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEmB;MAAC,gBAC7B,CAAC;IAEP;EACF;EACA,oBACExB,OAAA,CAAAE,SAAA;IAAAiB,QAAA,gBACEnB,OAAA,CAACH,QAAQ;MAACY,UAAU,EAAEA,UAAW;MAACH,QAAQ,EAAEA,QAAS;MAACO,WAAW,EAAEA,WAAY;MAACF,OAAO,EAAEA,OAAQ;MAC/FM,IAAI,EAAEA,IAAK;MAACU,IAAI,EAAC,IAAI;MACrBZ,UAAU,EAAEA,UAAW;MACvBG,SAAS,EAAEA,SAAU;MACrBN,YAAY,EAAEA,YAAa;MAACF,QAAQ,EAAEA,QAAS;MAACI,KAAK,EAAEA,KAAM;MAACE,QAAQ,EAAEA;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtFxB,OAAA,CAACF,aAAa;MAAAqB,QAAA,EACXX;IAAK;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA,eAChB,CAAC;AAEP,CAAC;AAACI,EAAA,GA9EIzB,UAAU;AAgFhB,eAAeA,UAAU;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}