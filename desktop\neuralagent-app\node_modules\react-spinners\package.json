{"name": "react-spinners", "version": "0.15.0", "description": "A collection of react loading spinners", "repository": {"type": "git", "url": "https://github.com/davidhu2000/react-spinners.git"}, "author": "<PERSON> (https://www.davidhu.io)", "license": "MIT", "bugs": {"url": "https://github.com/davidhu2000/react-spinners/issues"}, "homepage": "https://www.davidhu.io/react-spinners/", "contributors": ["C<PERSON> Guillaume (https://github.com/GuillaumeCisco)"], "keywords": ["react-spinners", "react-spinner", "react", "reactjs", "loader", "loaders", "loading", "spinner", "spinners", "halogen", "progress", "activity"], "main": "index.js", "module": "esm/index.js", "scripts": {"prepare": "$npm_execpath run build:cjs & $npm_execpath run build:esm & $npm_execpath run build:umd && $npm_execpath run build", "build": "tsc --project tsconfig.cjs.json", "build:cjs": "tsc --project tsconfig.cjs.json --outDir cjs", "build:esm": "tsc --project tsconfig.esm.json --outDir esm", "build:umd": "tsc --project tsconfig.umd.json --outDir umd", "build:demo": "$npm_execpath run vite build", "dev:demo": "vite dev", "patch": "npm version patch && npm publish && npm run clean", "minor": "npm version minor && npm publish && npm run clean", "major": "npm version major && npm publish && npm run clean", "clean": "rm -rf helpers/; rm -f *Loader.js; rm -f *Loader.d.ts; rm -f index.js; rm -f index.d.ts; rm -rf docs/js; rm -rf cjs; rm -rf esm; rm -rf umd", "lint": "eslint", "test": "jest", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls", "remove:tag": "npm dist-tag rm react-spinners next", "publish:next": "npm publish --tag next && npm run clean", "storybook": "storybook dev --docs -p 6006", "build-storybook": "storybook build --docs -o ./dist/storybook"}, "devDependencies": {"@storybook/addon-actions": "^8.1.10", "@storybook/addon-docs": "^8.1.10", "@storybook/addon-essentials": "^8.1.10", "@storybook/addon-interactions": "^8.1.10", "@storybook/addon-links": "^8.1.10", "@storybook/addon-webpack5-compiler-swc": "^1.0.4", "@storybook/addons": "7.6.17", "@storybook/react": "^8.1.10", "@storybook/react-vite": "^8.1.10", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/jest": "^29.5.12", "@types/react": "^18.3.3", "@types/react-click-outside": "^3.0.7", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.1", "coveralls": "^3.1.1", "eslint": "^9.5.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-unicorn": "^54.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.2", "react": "^18.3.1", "react-click-outside": "^3.0.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "storybook": "^8.1.10", "storybook-dark-mode": "^4.0.2", "ts-jest": "^29.1.5", "typescript": "^5.5.2", "vite": "^5.3.1"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "sideEffects": false, "packageManager": "yarn@4.3.1"}