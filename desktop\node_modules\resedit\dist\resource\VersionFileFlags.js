"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Flag values used by VersionEntry.fixedInfo field.
 * Zero or more enum values are stored (with OR operator).
 */
var VersionFileFlags;
(function (VersionFileFlags) {
    VersionFileFlags[VersionFileFlags["Debug"] = 1] = "Debug";
    VersionFileFlags[VersionFileFlags["Prerelease"] = 2] = "Prerelease";
    VersionFileFlags[VersionFileFlags["Patched"] = 4] = "Patched";
    VersionFileFlags[VersionFileFlags["PrivateBuild"] = 8] = "PrivateBuild";
    VersionFileFlags[VersionFileFlags["InfoInferred"] = 16] = "InfoInferred";
    VersionFileFlags[VersionFileFlags["SpecialBuild"] = 32] = "SpecialBuild";
})(VersionFileFlags || (VersionFileFlags = {}));
exports.default = VersionFileFlags;
