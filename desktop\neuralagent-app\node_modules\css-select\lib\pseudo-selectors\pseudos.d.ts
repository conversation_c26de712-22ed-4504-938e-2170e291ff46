import { PseudoSelector } from "css-what";
import type { InternalOptions } from "../types";
export declare type Pseudo = <Node, ElementNode extends Node>(elem: ElementNode, options: InternalOptions<Node, ElementNode>, subselect?: ElementNode | string | null) => boolean;
export declare const pseudos: Record<string, Pseudo>;
export declare function verifyPseudoArgs(func: Pseudo, name: string, subselect: PseudoSelector["data"]): void;
//# sourceMappingURL=pseudos.d.ts.map