{"ast": null, "code": "import axios from \"axios\";\nimport constants from \"./constants\";\nexport default axios.create({\n  baseURL: constants.BASE_URL\n});\nexport const API_KEY_HEADER = {\n  headers: {\n    'Authorization': 'Api-Key ' + constants.API_KEY\n  }\n};", "map": {"version": 3, "names": ["axios", "constants", "create", "baseURL", "BASE_URL", "API_KEY_HEADER", "headers", "API_KEY"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/utils/axios.js"], "sourcesContent": ["import axios from \"axios\";\nimport constants from \"./constants\";\n\nexport default axios.create({\n  baseURL: constants.BASE_URL\n});\n\nexport const API_KEY_HEADER = {\n  headers: {\n    'Authorization': 'Api-Key ' + constants.API_KEY,\n  }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,aAAa;AAEnC,eAAeD,KAAK,CAACE,MAAM,CAAC;EAC1BC,OAAO,EAAEF,SAAS,CAACG;AACrB,CAAC,CAAC;AAEF,OAAO,MAAMC,cAAc,GAAG;EAC5BC,OAAO,EAAE;IACP,eAAe,EAAE,UAAU,GAAGL,SAAS,CAACM;EAC1C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}