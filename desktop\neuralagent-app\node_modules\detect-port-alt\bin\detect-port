#!/usr/bin/env node

'use strict';

const pkg = require('../package');

const args = process.argv.slice(2);
const arg_0 = args[0];

if (!!~['-v', '--version'].indexOf(arg_0)) {
  console.log(pkg.version);
  process.exit(0);
}

const port = parseInt(arg_0, 10);

const main = require('..');

if (!arg_0) {
  const random = Math.floor(9000 + Math.random() * (65535 - 9000));

  main(random, (err, port) => {
    if (err) {
      console.log(`get available port failed with ${err}`);
    }
    console.log(`get available port ${port} randomly`);
  });
} else if (isNaN(port)) {
  console.log();
  console.log(`  \u001b[37m${pkg.description}\u001b[0m`);
  console.log();
  console.log('  Usage:');
  console.log();
  console.log(`    ${pkg.name} [port]`);
  console.log();
  console.log('  Options:');
  console.log();
  console.log('    -v, --version    show version and exit');
  console.log('    -h, --help       output usage information');
  console.log();
  console.log('  Further help:');
  console.log();
  console.log(`    ${pkg.homepage}`);
  console.log();
} else {
  main(port, (err, _port) => {
    if (err) {
      console.log(`get available port failed with ${err}`);
    }

    if (port !== _port) {
      console.log(`port ${port} was occupied`);
    }
    console.log(`get available port ${_port}`);
  });
}
