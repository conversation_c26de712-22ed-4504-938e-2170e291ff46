{"name": "<PERSON><PERSON><PERSON>er", "description": "Simple JSON Addressing.", "tags": ["util", "simple", "util", "utility"], "version": "5.0.1", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "https://github.com/janl/node-jsonpointer.git"}, "bugs": {"url": "http://github.com/janl/node-jsonpointer/issues"}, "engines": {"node": ">=0.10.0"}, "main": "./jsonpointer", "typings": "jsonpointer.d.ts", "files": ["jsonpointer.js", "jsonpointer.d.ts"], "scripts": {"test": "npm run test:standard && npm run test:all", "test:standard": "standard", "test:all": "node test.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "license": "MIT", "devDependencies": {"semantic-release": "^18.0.0", "standard": "^16.0.4"}, "standard": {"ignore": ["test.js"]}}