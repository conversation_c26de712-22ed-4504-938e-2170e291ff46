# global-modules [![NPM version](https://img.shields.io/npm/v/global-modules.svg?style=flat)](https://www.npmjs.com/package/global-modules) [![NPM monthly downloads](https://img.shields.io/npm/dm/global-modules.svg?style=flat)](https://npmjs.org/package/global-modules) [![NPM total downloads](https://img.shields.io/npm/dt/global-modules.svg?style=flat)](https://npmjs.org/package/global-modules) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/global-modules.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/global-modules)

> The directory used by npm for globally installed npm modules.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save global-modules
```

## Usage

```js
const globalModules = require('global-modules');
console.log(globalModules);
//=> '/usr/local/lib/node_modules' 
```
_(Note that this path might be different based on platform, user-defined configuration settings, etc)_

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [git-config-path](https://www.npmjs.com/package/git-config-path): Resolve the path to the user's local or global .gitconfig. | [homepage](https://github.com/jonschlinkert/git-config-path "Resolve the path to the user's local or global .gitconfig.")
* [global-prefix](https://www.npmjs.com/package/global-prefix): Get the npm global path prefix. | [homepage](https://github.com/jonschlinkert/global-prefix "Get the npm global path prefix.")
* [npm-paths](https://www.npmjs.com/package/npm-paths): Returns an array of unique "npm" directories based on the user's platform and environment. | [homepage](https://github.com/jonschlinkert/npm-paths "Returns an array of unique "npm" directories based on the user's platform and environment.")

### Contributors

| **Commits** | **Contributor** |  
| --- | --- |  
| 20 | [jonschlinkert](https://github.com/jonschlinkert) |  
| 1  | [Kikobeats](https://github.com/Kikobeats) |  

### Author

**Jon Schlinkert**

* [GitHub Profile](https://github.com/jonschlinkert)
* [Twitter Profile](https://twitter.com/jonschlinkert)
* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on December 14, 2018._