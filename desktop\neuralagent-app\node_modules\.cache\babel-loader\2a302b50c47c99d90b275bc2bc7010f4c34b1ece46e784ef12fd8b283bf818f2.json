{"ast": null, "code": "import styled from 'styled-components';\nimport { NavLink } from 'react-router-dom';\nexport const SidebarContainer = styled.div`\n  width: 260px;\n  display: flex;\n  flex-direction: column;\n  padding: 12px;\n  border-right: thin solid rgba(255, 255, 255, 0.3);\n`;\nexport const LogoWrapper = styled(NavLink)`\n  display: flex;\n  justify-content: center;\n  margin-top: 8px;\n  margin-bottom: 15px;\n`;\nexport const Logo = styled.img`\n  object-fit: contain;\n  pointer-events: none;\n  user-select: none;\n`;", "map": {"version": 3, "names": ["styled", "NavLink", "SidebarContainer", "div", "LogoWrapper", "Logo", "img"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/layouts/Sidebar/SidebarElements.js"], "sourcesContent": ["import styled from 'styled-components';\nimport { NavLink } from 'react-router-dom';\n\nexport const SidebarContainer = styled.div`\n  width: 260px;\n  display: flex;\n  flex-direction: column;\n  padding: 12px;\n  border-right: thin solid rgba(255, 255, 255, 0.3);\n`;\n\nexport const LogoWrapper = styled(NavLink)`\n  display: flex;\n  justify-content: center;\n  margin-top: 8px;\n  margin-bottom: 15px;\n`;\n\nexport const Logo = styled.img`\n  object-fit: contain;\n  pointer-events: none;\n  user-select: none;\n`;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,kBAAkB;AAE1C,OAAO,MAAMC,gBAAgB,GAAGF,MAAM,CAACG,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGJ,MAAM,CAACC,OAAO,CAAC;AAC1C;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMI,IAAI,GAAGL,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}