"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "checkFlowFileAnnotation", {
  enumerable: true,
  get: function () {
    return _checkFlowFileAnnotation.default;
  }
});
Object.defineProperty(exports, "fuzzyStringMatch", {
  enumerable: true,
  get: function () {
    return _fuzzyStringMatch.default;
  }
});
Object.defineProperty(exports, "getParameterName", {
  enumerable: true,
  get: function () {
    return _getParameterName.default;
  }
});
Object.defineProperty(exports, "getTokenAfterParens", {
  enumerable: true,
  get: function () {
    return _getTokenAfterParens.default;
  }
});
Object.defineProperty(exports, "getTokenBeforeParens", {
  enumerable: true,
  get: function () {
    return _getTokenBeforeParens.default;
  }
});
Object.defineProperty(exports, "isFlowFile", {
  enumerable: true,
  get: function () {
    return _isFlowFile.default;
  }
});
Object.defineProperty(exports, "isFlowFileAnnotation", {
  enumerable: true,
  get: function () {
    return _isFlowFileAnnotation.default;
  }
});
Object.defineProperty(exports, "isNoFlowFile", {
  enumerable: true,
  get: function () {
    return _isNoFlowFile.default;
  }
});
Object.defineProperty(exports, "iterateFunctionNodes", {
  enumerable: true,
  get: function () {
    return _iterateFunctionNodes.default;
  }
});
Object.defineProperty(exports, "quoteName", {
  enumerable: true,
  get: function () {
    return _quoteName.default;
  }
});
exports.spacingFixers = void 0;

var spacingFixers = _interopRequireWildcard(require("./spacingFixers"));

exports.spacingFixers = spacingFixers;

var _checkFlowFileAnnotation = _interopRequireDefault(require("./checkFlowFileAnnotation"));

var _fuzzyStringMatch = _interopRequireDefault(require("./fuzzyStringMatch"));

var _getParameterName = _interopRequireDefault(require("./getParameterName"));

var _getTokenAfterParens = _interopRequireDefault(require("./getTokenAfterParens"));

var _getTokenBeforeParens = _interopRequireDefault(require("./getTokenBeforeParens"));

var _isFlowFile = _interopRequireDefault(require("./isFlowFile"));

var _isNoFlowFile = _interopRequireDefault(require("./isNoFlowFile"));

var _isFlowFileAnnotation = _interopRequireDefault(require("./isFlowFileAnnotation"));

var _iterateFunctionNodes = _interopRequireDefault(require("./iterateFunctionNodes"));

var _quoteName = _interopRequireDefault(require("./quoteName"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }