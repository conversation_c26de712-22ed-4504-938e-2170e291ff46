{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\Elements\\\\MessageBar\\\\index.js\";\nimport React from 'react';\nimport { MessageBarContainer } from './MessageBarElements';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MessageBar = ({\n  message,\n  backgroundColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(MessageBarContainer, {\n    backgroundColor: backgroundColor,\n    children: /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = MessageBar;\nexport default MessageBar;\nvar _c;\n$RefreshReg$(_c, \"MessageBar\");", "map": {"version": 3, "names": ["React", "MessageBarContainer", "jsxDEV", "_jsxDEV", "MessageBar", "message", "backgroundColor", "children", "style", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/MessageBar/index.js"], "sourcesContent": ["import React from 'react';\nimport { MessageBarContainer } from './MessageBarElements';\n\nconst MessageBar = ({ message, backgroundColor }) => {\n  return (\n    <MessageBarContainer backgroundColor={backgroundColor}>\n      <p style={{textAlign: 'center'}}>\n        {message}\n      </p>\n    </MessageBarContainer>\n  )\n}\n\nexport default MessageBar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,mBAAmB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAgB,CAAC,KAAK;EACnD,oBACEH,OAAA,CAACF,mBAAmB;IAACK,eAAe,EAAEA,eAAgB;IAAAC,QAAA,eACpDJ,OAAA;MAAGK,KAAK,EAAE;QAACC,SAAS,EAAE;MAAQ,CAAE;MAAAF,QAAA,EAC7BF;IAAO;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE1B,CAAC;AAAAC,EAAA,GARKV,UAAU;AAUhB,eAAeA,UAAU;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}