{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomian<PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\views\\\\BackgroundSetup.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Wrapper = styled.div`\n  padding: 30px;\n  color: white;\n  text-align: center;\n`;\n_c = Wrapper;\nconst ProgressBar = styled.div`\n  height: 10px;\n  width: 100%;\n  background: rgba(255,255,255,0.1);\n  border-radius: 8px;\n  margin-top: 20px;\n`;\n_c2 = ProgressBar;\nconst Fill = styled.div`\n  height: 100%;\n  width: ${props => props.pct}%;\n  background: #4BB543;\n  border-radius: 8px;\n  transition: width 0.3s ease;\n`;\n_c3 = Fill;\nexport default function BackgroundSetup() {\n  _s();\n  const [status, setStatus] = useState('Working...');\n  const [progress, setProgress] = useState(0);\n  useEffect(() => {\n    window.electronAPI.onSetupStatus(setStatus);\n    window.electronAPI.onSetupProgress(setProgress);\n    window.electronAPI.onSetupComplete(result => {\n      if (result.success) {\n        setStatus('Setup Complete! You can now use Background Mode.');\n        setTimeout(() => window.close(), 4000);\n      } else {\n        setStatus(`${result.error || 'Setup Failed: Please ensure you have Windows 10 or higher and that virtualization is enabled in BIOS.'}`);\n      }\n    });\n    window.electronAPI.startBackgroundSetup();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Wrapper, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '18px',\n        fontWeight: '600'\n      },\n      children: \"Setting up Background Mode\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: '16px',\n        fontWeight: '400'\n      },\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n      children: /*#__PURE__*/_jsxDEV(Fill, {\n        pct: progress\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n}\n_s(BackgroundSetup, \"92WSrqwo7lBDYisnmTewcXD5jH8=\");\n_c4 = BackgroundSetup;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Wrapper\");\n$RefreshReg$(_c2, \"ProgressBar\");\n$RefreshReg$(_c3, \"Fill\");\n$RefreshReg$(_c4, \"BackgroundSetup\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styled", "jsxDEV", "_jsxDEV", "Wrapper", "div", "_c", "ProgressBar", "_c2", "Fill", "props", "pct", "_c3", "BackgroundSetup", "_s", "status", "setStatus", "progress", "setProgress", "window", "electronAPI", "onSetupStatus", "onSetupProgress", "onSetupComplete", "result", "success", "setTimeout", "close", "error", "startBackgroundSetup", "children", "style", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c4", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/views/BackgroundSetup.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\n\nconst Wrapper = styled.div`\n  padding: 30px;\n  color: white;\n  text-align: center;\n`;\n\nconst ProgressBar = styled.div`\n  height: 10px;\n  width: 100%;\n  background: rgba(255,255,255,0.1);\n  border-radius: 8px;\n  margin-top: 20px;\n`;\n\nconst Fill = styled.div`\n  height: 100%;\n  width: ${props => props.pct}%;\n  background: #4BB543;\n  border-radius: 8px;\n  transition: width 0.3s ease;\n`;\n\nexport default function BackgroundSetup() {\n  const [status, setStatus] = useState('Working...');\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    window.electronAPI.onSetupStatus(setStatus);\n    window.electronAPI.onSetupProgress(setProgress);\n    window.electronAPI.onSetupComplete(result => {\n      if (result.success) {\n        setStatus('Setup Complete! You can now use Background Mode.');\n        setTimeout(() => window.close(), 4000);\n      } else {\n        setStatus(`${result.error || 'Setup Failed: Please ensure you have Windows 10 or higher and that virtualization is enabled in BIOS.'}`);\n      }\n    });\n    window.electronAPI.startBackgroundSetup();\n  }, []);\n\n  return (\n    <Wrapper>\n      <div style={{fontSize: '18px', fontWeight: '600'}}>Setting up Background Mode</div>\n      <p style={{fontSize: '16px', fontWeight: '400'}}>{status}</p>\n      <ProgressBar>\n        <Fill pct={progress} />\n      </ProgressBar>\n    </Wrapper>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGH,MAAM,CAACI,GAAG;AAC1B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,OAAO;AAMb,MAAMG,WAAW,GAAGN,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,WAAW;AAQjB,MAAME,IAAI,GAAGR,MAAM,CAACI,GAAG;AACvB;AACA,WAAWK,KAAK,IAAIA,KAAK,CAACC,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIH,IAAI;AAQV,eAAe,SAASI,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,YAAY,CAAC;EAClD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAE3CD,SAAS,CAAC,MAAM;IACdoB,MAAM,CAACC,WAAW,CAACC,aAAa,CAACL,SAAS,CAAC;IAC3CG,MAAM,CAACC,WAAW,CAACE,eAAe,CAACJ,WAAW,CAAC;IAC/CC,MAAM,CAACC,WAAW,CAACG,eAAe,CAACC,MAAM,IAAI;MAC3C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClBT,SAAS,CAAC,kDAAkD,CAAC;QAC7DU,UAAU,CAAC,MAAMP,MAAM,CAACQ,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLX,SAAS,CAAC,GAAGQ,MAAM,CAACI,KAAK,IAAI,uGAAuG,EAAE,CAAC;MACzI;IACF,CAAC,CAAC;IACFT,MAAM,CAACC,WAAW,CAACS,oBAAoB,CAAC,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE1B,OAAA,CAACC,OAAO;IAAA0B,QAAA,gBACN3B,OAAA;MAAK4B,KAAK,EAAE;QAACC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAK,CAAE;MAAAH,QAAA,EAAC;IAA0B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACnFlC,OAAA;MAAG4B,KAAK,EAAE;QAACC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAK,CAAE;MAAAH,QAAA,EAAEf;IAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7DlC,OAAA,CAACI,WAAW;MAAAuB,QAAA,eACV3B,OAAA,CAACM,IAAI;QAACE,GAAG,EAAEM;MAAS;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEd;AAACvB,EAAA,CA3BuBD,eAAe;AAAAyB,GAAA,GAAfzB,eAAe;AAAA,IAAAP,EAAA,EAAAE,GAAA,EAAAI,GAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAAjC,EAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}