{"version": 3, "file": "prefer-readonly.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-readonly.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAAoE;AACpE,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAChC,kCAA8C;AAS9C,MAAM,uBAAuB,GAAG;IAC9B,sBAAc,CAAC,uBAAuB;IACtC,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,kBAAkB;IACjC,sBAAc,CAAC,gBAAgB;CAChC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEb,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,yGAAyG;YAC3G,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,cAAc,EACZ,+DAA+D;SAClE;QACD,MAAM,EAAE;YACN;gBACE,yBAAyB,EAAE,KAAK;gBAChC,UAAU,EAAE;oBACV,iBAAiB,EAAE;wBACjB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,IAAI,EAAE,QAAQ;aACf;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;IAC9C,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,iBAAiB,EAAE,CAAC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,eAAe,GAAiB,EAAE,CAAC;QAEzC,SAAS,8BAA8B,CACrC,IAAiC,EACjC,MAAe,EACf,UAAsB;YAEtB,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE;gBACjC,4BAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBACvD,OAAO;aACR;YAED,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE;gBACpE,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBACzC,OAAO;aACR;YAED,IACE,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC;gBACnC,EAAE,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAClC;gBACA,0CAA0C,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aAChE;QACH,CAAC;QAED,SAAS,4BAA4B,CACnC,IAAiC,EACjC,MAA2B,EAC3B,UAAsB;YAEtB,IACE,MAAM,CAAC,IAAI,KAAK,IAAI;gBACpB,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EACnD;gBACA,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;aAC1C;QACH,CAAC;QAED,SAAS,0CAA0C,CACjD,IAA0D,EAC1D,UAAsB;YAEtB,IACE,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC7C,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EAC/C;gBACA,UAAU,CAAC,uBAAuB,CAChC,IAAI,CAAC,OAAsC,CAC5C,CAAC;aACH;QACH,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAiC;YAEjC,IAAI,OAAO,GAAY,IAAI,CAAC,MAAM,CAAC;YAEnC,OAAO,OAAO,EAAE;gBACd,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAE9B,IACE,EAAE,CAAC,yBAAyB,CAAC,MAAM,CAAC;oBACpC,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC;oBACnC,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC7B,CAAC,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;wBACzB,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAC7C;oBACA,OAAO,GAAG,MAAM,CAAC;iBAClB;qBAAM,IACL,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC7B,CAAC,EAAE,CAAC,0BAA0B,CAAC,OAAO,CAAC,EACvC;oBACA,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,OAAO;wBACvB,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CACxD,CAAC;iBACH;qBAAM;oBACL,MAAM;iBACP;aACF;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,8BAA8B,CACrC,IAI6B;YAE7B,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9D,IAAI,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE;gBACvC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QAED,SAAS,2BAA2B,CAClC,aAA6C;YAE7C,IACE,EAAE,CAAC,8BAA8B,CAAC,aAAa,EAAE,aAAa,CAAC,MAAM,CAAC,EACtE;gBACA,OAAO;oBACL,MAAM,EAAE,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;oBACpE,QAAQ,EAAE,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAChD,aAAa,CAAC,IAAI,CACnB;iBACF,CAAC;aACH;YAED,OAAO;gBACL,MAAM,EAAE,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC;gBAC/D,QAAQ,EAAE,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;aACvE,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mCAAmC,CACjC,IAA0D;gBAE1D,eAAe,CAAC,IAAI,CAClB,IAAI,UAAU,CACZ,OAAO,EACP,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAClB,CACF,CAAC;YACJ,CAAC;YACD,wCAAwC;gBACtC,MAAM,mBAAmB,GAAG,eAAe,CAAC,GAAG,EAAG,CAAC;gBACnD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;gBAE3C,KAAK,MAAM,aAAa,IAAI,mBAAmB,CAAC,qCAAqC,EAAE,EAAE;oBACvF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GACxB,2BAA2B,CAAC,aAAa,CAAC,CAAC;oBAC7C,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE;4BACJ,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC;yBACnC;wBACD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC;wBAC3D,SAAS,EAAE,gBAAgB;wBAC3B,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,gBAAgB,CAAC,IAAI;gBACnB,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACrD,IAAI,CAC0B,CAAC;oBACjC,8BAA8B,CAC5B,MAAM,EACN,MAAM,CAAC,MAAM,EACb,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAC5C,CAAC;iBACH;YACH,CAAC;YACD,CAAC,uBAAuB,CAAC,CACvB,IAI6B;gBAE7B,IAAI,gBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;oBAChC,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAC1D,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAC/C,CAAC;iBACH;qBAAM,IAAI,8BAA8B,CAAC,IAAI,CAAC,EAAE;oBAC/C,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC;iBACnE;YACH,CAAC;YACD,CAAC,GAAG,uBAAuB,OAAO,CAAC,CACjC,IAI6B;gBAE7B,IAAI,gBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;oBAChC,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;iBAC/D;qBAAM,IAAI,8BAA8B,CAAC,IAAI,CAAC,EAAE;oBAC/C,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;iBAClE;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAMH,MAAM,mBAAmB,GAAG,CAAC,CAAC,CAAC;AAC/B,MAAM,2BAA2B,GAAG,CAAC,CAAC;AAEtC,MAAM,UAAU;IAgBd,YACmB,OAAuB,EACxC,SAAkC,EACjB,iBAA2B;QAF3B,YAAO,GAAP,OAAO,CAAgB;QAEvB,sBAAiB,GAAjB,iBAAiB,CAAU;QAlB7B,6BAAwB,GAAG,IAAI,GAAG,EAGhD,CAAC;QACa,6BAAwB,GAAG,IAAI,GAAG,EAGhD,CAAC;QACa,gCAA2B,GAAG,IAAI,GAAG,EAAU,CAAC;QAChD,gCAA2B,GAAG,IAAI,GAAG,EAAU,CAAC;QAIzD,0BAAqB,GAAG,mBAAmB,CAAC;QAOlD,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;YACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACrC;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC5B;QAED,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE;YACtC,IAAI,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE;gBACpC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;aAClC;SACF;IACH,CAAC;IAEM,mBAAmB,CAAC,IAAoC;QAC7D,IACE,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;YAC1D,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC1D,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC;YACA,OAAO;SACR;QAED,IACE,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,WAAW,KAAK,SAAS;YAC9B,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,EACrC;YACA,OAAO;SACR;QAED,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;YACvD,CAAC,CAAC,IAAI,CAAC,wBAAwB;YAC/B,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAChC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAEM,uBAAuB,CAAC,IAAiC;QAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrE,IACE,CAAC,YAAY,CAAC,SAAS,EAAE;YACzB,CAAC,IAAA,0BAAmB,EAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,EAClD;YACA,OAAO;SACR;QAED,MAAM,eAAe,GACnB,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;YAClC,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAClE,IACE,CAAC,eAAe;YAChB,IAAI,CAAC,qBAAqB,KAAK,2BAA2B,EAC1D;YACA,OAAO;SACR;QAED,CAAC,eAAe;YACd,CAAC,CAAC,IAAI,CAAC,2BAA2B;YAClC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CACnC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAEM,gBAAgB,CACrB,IAI6B;QAE7B,IAAI,CAAC,qBAAqB,GAAG,2BAA2B,CAAC;QAEzD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;YACvC,IAAI,OAAO,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBAClE,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;aACrC;SACF;IACH,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC;IACnD,CAAC;IAEM,mBAAmB;QACxB,IAAI,IAAI,CAAC,qBAAqB,KAAK,mBAAmB,EAAE;YACtD,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAC;SACjC;IACH,CAAC;IAEM,kBAAkB;QACvB,IAAI,IAAI,CAAC,qBAAqB,KAAK,mBAAmB,EAAE;YACtD,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAC;SACjC;IACH,CAAC;IAEM,qCAAqC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACtD,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACtD,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;YACrD,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;SACtD,CAAC;IACJ,CAAC;CACF"}