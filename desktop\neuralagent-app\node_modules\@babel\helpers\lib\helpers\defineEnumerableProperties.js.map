{"version": 3, "names": ["_defineEnumerableProperties", "obj", "descs", "key", "desc", "configurable", "enumerable", "writable", "Object", "defineProperty", "getOwnPropertySymbols", "objectSymbols", "i", "length", "sym"], "sources": ["../../src/helpers/defineEnumerableProperties.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n/* @onlyBabel7 */\nexport default function _defineEnumerableProperties<T>(\n  obj: T,\n  descs: { [key: string | symbol]: PropertyDescriptor },\n): T {\n  // eslint-disable-next-line guard-for-in\n  for (var key in descs) {\n    var desc = descs[key];\n    desc.configurable = desc.enumerable = true;\n    if (\"value\" in desc) desc.writable = true;\n    Object.defineProperty(obj, key, desc);\n  }\n\n  // Symbols are not enumerated over by for-in loops. If native\n  // Symbols are available, fetch all of the descs object's own\n  // symbol properties and define them on our target object too.\n  if (Object.getOwnPropertySymbols) {\n    var objectSymbols = Object.getOwnPropertySymbols(descs);\n    for (var i = 0; i < objectSymbols.length; i++) {\n      var sym = objectSymbols[i];\n      desc = descs[sym];\n      desc.configurable = desc.enumerable = true;\n      if (\"value\" in desc) desc.writable = true;\n      Object.defineProperty(obj, sym, desc);\n    }\n  }\n  return obj;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,2BAA2BA,CACjDC,GAAM,EACNC,KAAqD,EAClD;EAEH,KAAK,IAAIC,GAAG,IAAID,KAAK,EAAE;IACrB,IAAIE,IAAI,GAAGF,KAAK,CAACC,GAAG,CAAC;IACrBC,IAAI,CAACC,YAAY,GAAGD,IAAI,CAACE,UAAU,GAAG,IAAI;IAC1C,IAAI,OAAO,IAAIF,IAAI,EAAEA,IAAI,CAACG,QAAQ,GAAG,IAAI;IACzCC,MAAM,CAACC,cAAc,CAACR,GAAG,EAAEE,GAAG,EAAEC,IAAI,CAAC;EACvC;EAKA,IAAII,MAAM,CAACE,qBAAqB,EAAE;IAChC,IAAIC,aAAa,GAAGH,MAAM,CAACE,qBAAqB,CAACR,KAAK,CAAC;IACvD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAIE,GAAG,GAAGH,aAAa,CAACC,CAAC,CAAC;MAC1BR,IAAI,GAAGF,KAAK,CAACY,GAAG,CAAC;MACjBV,IAAI,CAACC,YAAY,GAAGD,IAAI,CAACE,UAAU,GAAG,IAAI;MAC1C,IAAI,OAAO,IAAIF,IAAI,EAAEA,IAAI,CAACG,QAAQ,GAAG,IAAI;MACzCC,MAAM,CAACC,cAAc,CAACR,GAAG,EAAEa,GAAG,EAAEV,IAAI,CAAC;IACvC;EACF;EACA,OAAOH,GAAG;AACZ", "ignoreList": []}