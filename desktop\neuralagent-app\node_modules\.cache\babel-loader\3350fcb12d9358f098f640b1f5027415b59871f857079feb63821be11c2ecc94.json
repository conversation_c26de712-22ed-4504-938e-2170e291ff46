{"ast": null, "code": "function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\nexport { memoize as default };", "map": {"version": 3, "names": ["memoize", "fn", "cache", "Object", "create", "arg", "undefined", "default"], "sources": ["F:/zuomian<PERSON>jian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/@emotion/memoize/dist/emotion-memoize.esm.js"], "sourcesContent": ["function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\n\nexport { memoize as default };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,EAAE,EAAE;EACnB,IAAIC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC/B,OAAO,UAAUC,GAAG,EAAE;IACpB,IAAIH,KAAK,CAACG,GAAG,CAAC,KAAKC,SAAS,EAAEJ,KAAK,CAACG,GAAG,CAAC,GAAGJ,EAAE,CAACI,GAAG,CAAC;IAClD,OAAOH,KAAK,CAACG,GAAG,CAAC;EACnB,CAAC;AACH;AAEA,SAASL,OAAO,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}