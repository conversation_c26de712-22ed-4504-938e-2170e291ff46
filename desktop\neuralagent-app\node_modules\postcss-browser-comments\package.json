{"name": "postcss-browser-comments", "version": "4.0.0", "description": "Keep only the CSS you need based on comments and your browserslist", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "repository": "csstools/postcss-browser-comments", "homepage": "https://github.com/csstools/postcss-browser-comments#readme", "bugs": "https://github.com/csstools/postcss-browser-comments/issues", "main": "index.cjs", "module": "index.mjs", "files": ["index.cjs", "index.cjs.map", "index.mjs", "index.mjs.map"], "scripts": {"prepublishOnly": "npm test", "build": "rollup --config .rollup.js --silent", "lint": "eslint *.js --cache --ignore-path .gitignore --quiet", "test": "npm run lint && npm run build && npm run tape", "tape": "postcss-tape"}, "engines": {"node": ">=8"}, "peerDependencies": {"browserslist": ">=4", "postcss": ">=8"}, "devDependencies": {"@babel/core": "7.13.16", "@babel/preset-env": "7.13.15", "browserslist": "4.1.1", "eslint": "7.25.0", "postcss": "8.2.13", "postcss-tape": "6.0.1", "pre-commit": "1.2.2", "rollup": "2.46.0", "rollup-plugin-babel": "4.3.2"}, "eslintConfig": {"extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "ecmaFeatures": {"modules": true}}, "rules": {"semi": ["error", "never"]}}, "keywords": ["postcss", "css", "postcss-plugin", "browserslists", "browserlists", "browsers", "lists", "support", "caniuse", "target"]}