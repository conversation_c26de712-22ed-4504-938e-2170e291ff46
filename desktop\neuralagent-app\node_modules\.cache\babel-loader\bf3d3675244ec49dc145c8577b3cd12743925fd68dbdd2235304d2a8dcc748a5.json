{"ast": null, "code": "import styled from 'styled-components';\nexport const Avatar = styled.div`\n  width: ${props => props.size ? '' + props.size + 'px' : '50px'};\n  height: ${props => props.size ? '' + props.size + 'px' : '50px'};\n  border-radius: 50%;\n  background: ${props => props.color ? props.color : 'var(--primary-color)'};\n  color: ${props => props.light ? '#000' : '#fff'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: ${props => props.size ? '' + props.size / 2.2 + 'px' : '20px'};\n  box-shadow: ${props => props.raised ? '0 4pt 8pt rgb(0 0 0 / 20%)' : 'none'};\n  user-select: none;\n`;\nexport const AvatarImg = styled.img`\n  width: 100%;\n  height: 100%;\n  background: transparent;\n  border-radius: 50%;\n  object-fit: cover;\n  -o-object-fit: cover;\n  object-position: center;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  user-select: none;\n  pointer-events: none;\n`;\nexport const Divider = styled.div`\n  height: 0.8px;\n  background: ${props => props.color ? props.color : 'rgba(0, 0, 0, 0.2)'};\n`;\nexport const FlexSpacer = styled.div`\n  margin-left: ${props => props.isRTL ? '0px' : 'auto'};\n  margin-right: ${props => props.isRTL ? 'auto' : '0px'};\n`;\nexport const Badge = styled.div`\n  height: ${props => props.size ? '' + props.size + 'px' : '30px'};\n  width: ${props => props.size ? '' + props.size + 'px' : '30px'};\n  border-radius: 50%;\n  color: #fff;\n  background: ${props => props.color ? props.color : 'var(--danger-color)'};\n  font-size: ${props => props.size ? '' + props.size / 2.2 + 'px' : '20px'};\n  position: absolute;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n`;", "map": {"version": 3, "names": ["styled", "Avatar", "div", "props", "size", "color", "light", "raised", "AvatarImg", "img", "Divider", "FlexSpacer", "isRTL", "Badge"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/SmallElements.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Avatar = styled.div`\n  width: ${props => (props.size) ? (''  + props.size + 'px') : '50px'};\n  height: ${props => (props.size) ? (''  + props.size + 'px') : '50px'};\n  border-radius: 50%;\n  background: ${props => (props.color) ? props.color : 'var(--primary-color)'};\n  color: ${props => (props.light) ? '#000' : '#fff'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: ${props => (props.size) ? (''  + (props.size / 2.2) + 'px') : '20px'};\n  box-shadow: ${props => props.raised ? '0 4pt 8pt rgb(0 0 0 / 20%)' : 'none'};\n  user-select: none;\n`\n\nexport const AvatarImg = styled.img`\n  width: 100%;\n  height: 100%;\n  background: transparent;\n  border-radius: 50%;\n  object-fit: cover;\n  -o-object-fit: cover;\n  object-position: center;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  user-select: none;\n  pointer-events: none;\n`\n\nexport const Divider = styled.div`\n  height: 0.8px;\n  background: ${props => props.color ? props.color : 'rgba(0, 0, 0, 0.2)'};\n`\n\nexport const FlexSpacer = styled.div`\n  margin-left: ${props => props.isRTL ? '0px' : 'auto'};\n  margin-right: ${props => props.isRTL ? 'auto' : '0px'};\n`\n\nexport const Badge = styled.div`\n  height: ${props => (props.size) ? (''  + props.size + 'px') : '30px'};\n  width: ${props => (props.size) ? (''  + props.size + 'px') : '30px'};\n  border-radius: 50%;\n  color: #fff;\n  background: ${props => (props.color) ? props.color : 'var(--danger-color)'};\n  font-size: ${props => (props.size) ? (''  + (props.size / 2.2) + 'px') : '20px'};\n  position: absolute;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,MAAM,GAAGD,MAAM,CAACE,GAAG;AAChC,WAAWC,KAAK,IAAKA,KAAK,CAACC,IAAI,GAAK,EAAE,GAAID,KAAK,CAACC,IAAI,GAAG,IAAI,GAAI,MAAM;AACrE,YAAYD,KAAK,IAAKA,KAAK,CAACC,IAAI,GAAK,EAAE,GAAID,KAAK,CAACC,IAAI,GAAG,IAAI,GAAI,MAAM;AACtE;AACA,gBAAgBD,KAAK,IAAKA,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACE,KAAK,GAAG,sBAAsB;AAC7E,WAAWF,KAAK,IAAKA,KAAK,CAACG,KAAK,GAAI,MAAM,GAAG,MAAM;AACnD;AACA;AACA;AACA,eAAeH,KAAK,IAAKA,KAAK,CAACC,IAAI,GAAK,EAAE,GAAKD,KAAK,CAACC,IAAI,GAAG,GAAI,GAAG,IAAI,GAAI,MAAM;AACjF,gBAAgBD,KAAK,IAAIA,KAAK,CAACI,MAAM,GAAG,4BAA4B,GAAG,MAAM;AAC7E;AACA,CAAC;AAED,OAAO,MAAMC,SAAS,GAAGR,MAAM,CAACS,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,OAAO,GAAGV,MAAM,CAACE,GAAG;AACjC;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,oBAAoB;AACzE,CAAC;AAED,OAAO,MAAMM,UAAU,GAAGX,MAAM,CAACE,GAAG;AACpC,iBAAiBC,KAAK,IAAIA,KAAK,CAACS,KAAK,GAAG,KAAK,GAAG,MAAM;AACtD,kBAAkBT,KAAK,IAAIA,KAAK,CAACS,KAAK,GAAG,MAAM,GAAG,KAAK;AACvD,CAAC;AAED,OAAO,MAAMC,KAAK,GAAGb,MAAM,CAACE,GAAG;AAC/B,YAAYC,KAAK,IAAKA,KAAK,CAACC,IAAI,GAAK,EAAE,GAAID,KAAK,CAACC,IAAI,GAAG,IAAI,GAAI,MAAM;AACtE,WAAWD,KAAK,IAAKA,KAAK,CAACC,IAAI,GAAK,EAAE,GAAID,KAAK,CAACC,IAAI,GAAG,IAAI,GAAI,MAAM;AACrE;AACA;AACA,gBAAgBD,KAAK,IAAKA,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACE,KAAK,GAAG,qBAAqB;AAC5E,eAAeF,KAAK,IAAKA,KAAK,CAACC,IAAI,GAAK,EAAE,GAAKD,KAAK,CAACC,IAAI,GAAG,GAAI,GAAG,IAAI,GAAI,MAAM;AACjF;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}