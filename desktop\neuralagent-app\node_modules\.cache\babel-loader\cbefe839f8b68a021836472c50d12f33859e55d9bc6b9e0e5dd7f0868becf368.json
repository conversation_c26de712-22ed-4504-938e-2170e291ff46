{"ast": null, "code": "'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */\nexport default function combineURLs(baseURL, relativeURL) {\n  return relativeURL ? baseURL.replace(/\\/?\\/$/, '') + '/' + relativeURL.replace(/^\\/+/, '') : baseURL;\n}", "map": {"version": 3, "names": ["combineURLs", "baseURL", "relativeURL", "replace"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/node_modules/axios/lib/helpers/combineURLs.js"], "sourcesContent": ["'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */\nexport default function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/?\\/$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAAWA,CAACC,OAAO,EAAEC,WAAW,EAAE;EACxD,OAAOA,WAAW,GACdD,OAAO,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGD,WAAW,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GACrEF,OAAO;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}