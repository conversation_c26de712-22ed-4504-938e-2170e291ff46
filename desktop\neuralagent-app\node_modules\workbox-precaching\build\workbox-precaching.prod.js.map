{"version": 3, "file": "workbox-precaching.prod.js", "sources": ["../_version.js", "../utils/createCacheKey.js", "../utils/PrecacheInstallReportPlugin.js", "../utils/PrecacheCacheKeyPlugin.js", "../PrecacheStrategy.js", "../PrecacheController.js", "../utils/getOrCreatePrecacheController.js", "../PrecacheRoute.js", "../utils/generateURLVariations.js", "../utils/removeIgnoredSearchParams.js", "../addRoute.js", "../precache.js", "../PrecacheFallbackPlugin.js", "../addPlugins.js", "../cleanupOutdatedCaches.js", "../utils/deleteOutdatedCaches.js", "../createHandlerBoundToURL.js", "../getCacheKeyForURL.js", "../matchPrecache.js", "../precacheAndRoute.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state &&\n                    state.originalRequest &&\n                    state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A {@link workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * {@link workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] {@link https://developers.google.com/web/tools/workbox/guides/using-plugins|Plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters|init}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * {@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions|CacheQueryOptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            // Do not add integrity if the original request is no-cors\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            response = await handler.fetch(new Request(request, {\n                integrity: request.mode !== 'no-cors'\n                    ? integrityInRequest || integrityInManifest\n                    : undefined,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            // Also if the original request users no-cors we don't use integrity.\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            if (integrityInManifest &&\n                noIntegrityConflict &&\n                request.mode !== 'no-cors') {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true, } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = typeof entry !== 'string' && entry.revision ? 'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of {@link workbox-routing.Route} that takes a\n * {@link workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof workbox-precaching\n * @extends workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request, }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` + getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See the {@link workbox-precaching.PrecacheRoute}\n * options.\n *\n * @memberof workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * {@link workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * {@link workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * `PrecacheFallbackPlugin` allows you to specify an \"offline fallback\"\n * response to be used when a given strategy is unable to generate a response.\n *\n * It does this by intercepting the `handlerDidError` plugin callback\n * and returning a precached response, taking the expected revision parameter\n * into account automatically.\n *\n * Unless you explicitly pass in a `PrecacheController` instance to the\n * constructor, the default instance will be used. Generally speaking, most\n * developers will end up using the default.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheFallbackPlugin {\n    /**\n     * Constructs a new PrecacheFallbackPlugin with the associated fallbackURL.\n     *\n     * @param {Object} config\n     * @param {string} config.fallbackURL A precached URL to use as the fallback\n     *     if the associated strategy can't generate a response.\n     * @param {PrecacheController} [config.precacheController] An optional\n     *     PrecacheController instance. If not provided, the default\n     *     PrecacheController will be used.\n     */\n    constructor({ fallbackURL, precacheController, }) {\n        /**\n         * @return {Promise<Response>} The precache response for the fallback URL.\n         *\n         * @private\n         */\n        this.handlerDidError = () => this._precacheController.matchPrecache(this._fallbackURL);\n        this._fallbackURL = fallbackURL;\n        this._precacheController =\n            precacheController || getOrCreatePrecacheController();\n    }\n}\nexport { PrecacheFallbackPlugin };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds plugins to the precaching strategy.\n *\n * @param {Array<Object>} plugins\n *\n * @memberof workbox-precaching\n */\nfunction addPlugins(plugins) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.strategy.plugins.push(...plugins);\n}\nexport { addPlugins };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { deleteOutdatedCaches } from './utils/deleteOutdatedCaches.js';\nimport './_version.js';\n/**\n * Adds an `activate` event listener which will clean up incompatible\n * precaches that were created by older versions of Workbox.\n *\n * @memberof workbox-precaching\n */\nfunction cleanupOutdatedCaches() {\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-436484705\n    self.addEventListener('activate', ((event) => {\n        const cacheName = cacheNames.getPrecacheName();\n        event.waitUntil(deleteOutdatedCaches(cacheName).then((cachesDeleted) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachesDeleted.length > 0) {\n                    logger.log(`The following out-of-date precaches were cleaned up ` +\n                        `automatically:`, cachesDeleted);\n                }\n            }\n        }));\n    }));\n}\nexport { cleanupOutdatedCaches };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst SUBSTRING_TO_FIND = '-precache-';\n/**\n * Cleans up incompatible precaches that were created by older versions of\n * Workbox, by a service worker registered under the current scope.\n *\n * This is meant to be called as part of the `activate` event.\n *\n * This should be safe to use as long as you don't include `substringToFind`\n * (defaulting to `-precache-`) in your non-precache cache names.\n *\n * @param {string} currentPrecacheName The cache name currently in use for\n * precaching. This cache won't be deleted.\n * @param {string} [substringToFind='-precache-'] Cache names which include this\n * substring will be deleted (excluding `currentPrecacheName`).\n * @return {Array<string>} A list of all the cache names that were deleted.\n *\n * @private\n * @memberof workbox-precaching\n */\nconst deleteOutdatedCaches = async (currentPrecacheName, substringToFind = SUBSTRING_TO_FIND) => {\n    const cacheNames = await self.caches.keys();\n    const cacheNamesToDelete = cacheNames.filter((cacheName) => {\n        return (cacheName.includes(substringToFind) &&\n            cacheName.includes(self.registration.scope) &&\n            cacheName !== currentPrecacheName);\n    });\n    await Promise.all(cacheNamesToDelete.map((cacheName) => self.caches.delete(cacheName)));\n    return cacheNamesToDelete;\n};\nexport { deleteOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {workbox-routing~handlerCallback}\n *\n * @memberof workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Takes in a URL, and returns the corresponding URL that could be used to\n * lookup the entry in the precache.\n *\n * If a relative URL is provided, the location of the service worker file will\n * be used as the base.\n *\n * For precached entries without revision information, the cache key will be the\n * same as the original URL.\n *\n * For precached entries with revision information, the cache key will be the\n * original URL with the addition of a query parameter used for keeping track of\n * the revision info.\n *\n * @param {string} url The URL whose cache key to look up.\n * @return {string} The cache key that corresponds to that URL.\n *\n * @memberof workbox-precaching\n */\nfunction getCacheKeyForURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.getCacheKeyForURL(url);\n}\nexport { getCacheKeyForURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#matchPrecache} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call\n * {@link PrecacheController#matchPrecache} on that instance,\n * instead of using this function.\n *\n * @param {string|Request} request The key (without revisioning parameters)\n * to look up in the precache.\n * @return {Promise<Response|undefined>}\n *\n * @memberof workbox-precaching\n */\nfunction matchPrecache(request) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.matchPrecache(request);\n}\nexport { matchPrecache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * {@link workbox-precaching.precache} and\n * {@link workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See the\n * {@link workbox-precaching.PrecacheRoute} options.\n *\n * @memberof workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n"], "names": ["self", "_", "e", "createCacheKey", "entry", "WorkboxError", "urlObject", "URL", "location", "href", "cache<PERSON>ey", "url", "revision", "cacheKeyURL", "originalURL", "searchParams", "set", "PrecacheInstallReportPlugin", "constructor", "updatedURLs", "notUpdatedURLs", "handlerWillStart", "async", "request", "state", "originalRequest", "cachedResponseWillBeUsed", "event", "cachedResponse", "type", "Request", "push", "PrecacheCacheKeyPlugin", "precacheController", "cacheKeyWillBeUsed", "params", "this", "_precacheController", "getCacheKeyForURL", "headers", "PrecacheStrategy", "Strategy", "options", "cacheName", "cacheNames", "getPrecacheName", "_fallbackToNetwork", "fallbackToNetwork", "plugins", "copyRedirectedCacheableResponsesPlugin", "handler", "response", "cacheMatch", "_handleInstall", "_handleFetch", "integrityInManifest", "integrity", "integrityInRequest", "noIntegrityConflict", "fetch", "mode", "undefined", "_useDefaultCacheabilityPluginIfNeeded", "cachePut", "clone", "status", "defaultPluginIndex", "cacheWillUpdatePluginCount", "index", "plugin", "entries", "defaultPrecacheCacheabilityPlugin", "cacheWillUpdate", "splice", "redirected", "copyResponse", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "_urlsToCacheModes", "_cacheKeysToIntegrities", "_strategy", "install", "bind", "activate", "strategy", "precache", "addToCacheList", "_installAndActiveListenersAdded", "addEventListener", "urlsToWarnAbout", "cacheMode", "has", "get", "firstEntry", "secondEntry", "length", "warningMessage", "join", "console", "warn", "waitUntil", "installReportPlugin", "cache", "credentials", "Promise", "all", "handleAll", "caches", "open", "currentlyCachedRequests", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "values", "deletedURLs", "delete", "getURLsToCacheKeys", "getCachedURLs", "getIntegrityForCacheKey", "match", "createHandlerBoundToURL", "Object", "assign", "handle", "getOrCreatePrecacheController", "PrecacheRoute", "Route", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "ignoreURLParametersMatching", "directoryIndex", "cleanURLs", "urlManipulation", "hash", "urlWithoutIgnoredParams", "paramName", "some", "regExp", "test", "removeIgnoredSearchParams", "pathname", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "generateURLVariations", "addRoute", "precacheRoute", "registerRoute", "fallbackURL", "handlerDidError", "matchPrecache", "_fallbackURL", "currentPrecacheName", "substringToFind", "cacheNamesToDelete", "filter", "includes", "registration", "scope", "map", "deleteOutdatedCaches", "then", "cachesDeleted"], "mappings": "6FAEA,IACIA,KAAK,6BAA+BC,GACvC,CACD,MAAOC,ICeA,SAASC,EAAeC,OACtBA,QACK,IAAIC,eAAa,oCAAqC,CAAED,aAI7C,iBAAVA,EAAoB,OACrBE,EAAY,IAAIC,IAAIH,EAAOI,SAASC,YACnC,CACHC,SAAUJ,EAAUG,KACpBE,IAAKL,EAAUG,YAGjBG,SAAEA,EAAFD,IAAYA,GAAQP,MACrBO,QACK,IAAIN,eAAa,oCAAqC,CAAED,cAI7DQ,EAAU,OACLN,EAAY,IAAIC,IAAII,EAAKH,SAASC,YACjC,CACHC,SAAUJ,EAAUG,KACpBE,IAAKL,EAAUG,YAKjBI,EAAc,IAAIN,IAAII,EAAKH,SAASC,MACpCK,EAAc,IAAIP,IAAII,EAAKH,SAASC,aAC1CI,EAAYE,aAAaC,IAxCC,kBAwC0BJ,GAC7C,CACHF,SAAUG,EAAYJ,KACtBE,IAAKG,EAAYL,KAExB,CCzCD,MAAMQ,EACFC,mBACSC,YAAc,QACdC,eAAiB,QACjBC,iBAAmBC,OAASC,UAASC,YAElCA,IACAA,EAAMC,gBAAkBF,SAG3BG,yBAA2BJ,OAASK,QAAOH,QAAOI,wBAChC,YAAfD,EAAME,MACFL,GACAA,EAAMC,iBACND,EAAMC,2BAA2BK,QAAS,OAEpCnB,EAAMa,EAAMC,gBAAgBd,IAC9BiB,OACKR,eAAeW,KAAKpB,QAGpBQ,YAAYY,KAAKpB,UAI3BiB,CAAP,GCzBZ,MAAMI,EACFd,aAAYe,mBAAEA,SACLC,mBAAqBZ,OAASC,UAASY,mBAGlCzB,GAAYyB,aAAuC,EAASA,EAAOzB,WACrE0B,KAAKC,GAAoBC,kBAAkBf,EAAQZ,YAEhDD,EACD,IAAIoB,QAAQpB,EAAU,CAAE6B,QAAShB,EAAQgB,UACzChB,CAFN,OAICc,GAAsBJ,GCAnC,MAAMO,UAAyBC,WAkB3BvB,YAAYwB,EAAU,IAClBA,EAAQC,UAAYC,aAAWC,gBAAgBH,EAAQC,iBACjDD,QACDI,IAC6B,IAA9BJ,EAAQK,uBAKPC,QAAQjB,KAAKS,EAAiBS,sDASzB1B,EAAS2B,SACbC,QAAiBD,EAAQE,WAAW7B,UACtC4B,IAKAD,EAAQvB,OAAgC,YAAvBuB,EAAQvB,MAAME,WAClBO,KAAKiB,GAAe9B,EAAS2B,SAIjCd,KAAKkB,GAAa/B,EAAS2B,aAEzB3B,EAAS2B,OACpBC,QACEhB,EAAUe,EAAQf,QAAU,OAE9BC,KAAKU,SAuCC,IAAIzC,eAAa,yBAA0B,CAC7CsC,UAAWP,KAAKO,UAChBhC,IAAKY,EAAQZ,MAzCQ,OAMnB4C,EAAsBpB,EAAOqB,UAC7BC,EAAqBlC,EAAQiC,UAC7BE,GAAuBD,GAAsBA,IAAuBF,EAG1EJ,QAAiBD,EAAQS,MAAM,IAAI7B,QAAQP,EAAS,CAChDiC,UAA4B,YAAjBjC,EAAQqC,KACbH,GAAsBF,OACtBM,KASNN,GACAG,GACiB,YAAjBnC,EAAQqC,YACHE,WACmBZ,EAAQa,SAASxC,EAAS4B,EAASa,iBA+B5Db,WAEU5B,EAAS2B,QACrBY,WACCX,QAAiBD,EAAQS,MAAMpC,aAGb2B,EAAQa,SAASxC,EAAS4B,EAASa,eAIjD,IAAI3D,eAAa,0BAA2B,CAC9CM,IAAKY,EAAQZ,IACbsD,OAAQd,EAASc,gBAGlBd,EA6BXW,SACQI,EAAqB,KACrBC,EAA6B,MAC5B,MAAOC,EAAOC,KAAWjC,KAAKY,QAAQsB,UAEnCD,IAAW7B,EAAiBS,yCAI5BoB,IAAW7B,EAAiB+B,oCAC5BL,EAAqBE,GAErBC,EAAOG,iBACPL,KAG2B,IAA/BA,OACKnB,QAAQjB,KAAKS,EAAiB+B,mCAE9BJ,EAA6B,GAA4B,OAAvBD,QAElClB,QAAQyB,OAAOP,EAAoB,IAKpD1B,EAAiB+B,kCAAoC,iBACjD,OAAsBpB,SAAEA,MACfA,GAAYA,EAASc,QAAU,IACzB,KAEJd,GAGfX,EAAiBS,uCAAyC,iBACtD,OAAsBE,SAAEA,KACbA,EAASuB,iBAAmBC,eAAaxB,GAAYA,GCnMpE,MAAMyB,EAWF1D,aAAYyB,UAAEA,EAAFK,QAAaA,EAAU,GAAvBD,kBAA2BA,GAAoB,GAAU,SAC5D8B,GAAmB,IAAIC,SACvBC,GAAoB,IAAID,SACxBE,GAA0B,IAAIF,SAC9BG,GAAY,IAAIzC,EAAiB,CAClCG,UAAWC,aAAWC,gBAAgBF,GACtCK,QAAS,IACFA,EACH,IAAIhB,EAAuB,CAAEC,mBAAoBG,QAErDW,2BAGCmC,QAAU9C,KAAK8C,QAAQC,KAAK/C,WAC5BgD,SAAWhD,KAAKgD,SAASD,KAAK/C,MAMnCiD,sBACOjD,KAAK6C,GAYhBK,SAAShB,QACAiB,eAAejB,GACflC,KAAKoD,KACNxF,KAAKyF,iBAAiB,UAAWrD,KAAK8C,SACtClF,KAAKyF,iBAAiB,WAAYrD,KAAKgD,eAClCI,IAAkC,GAU/CD,eAAejB,SASLoB,EAAkB,OACnB,MAAMtF,KAASkE,EAAS,CAEJ,iBAAVlE,EACPsF,EAAgB3D,KAAK3B,GAEhBA,QAA4ByD,IAAnBzD,EAAMQ,UACpB8E,EAAgB3D,KAAK3B,EAAMO,WAEzBD,SAAEA,EAAFC,IAAYA,GAAQR,EAAeC,GACnCuF,EAA6B,iBAAVvF,GAAsBA,EAAMQ,SAAW,SAAW,aACvEwB,KAAKyC,GAAiBe,IAAIjF,IAC1ByB,KAAKyC,GAAiBgB,IAAIlF,KAASD,QAC7B,IAAIL,eAAa,wCAAyC,CAC5DyF,WAAY1D,KAAKyC,GAAiBgB,IAAIlF,GACtCoF,YAAarF,OAGA,iBAAVN,GAAsBA,EAAMoD,UAAW,IAC1CpB,KAAK4C,GAAwBY,IAAIlF,IACjC0B,KAAK4C,GAAwBa,IAAInF,KAAcN,EAAMoD,gBAC/C,IAAInD,eAAa,4CAA6C,CAChEM,aAGHqE,GAAwBhE,IAAIN,EAAUN,EAAMoD,mBAEhDqB,GAAiB7D,IAAIL,EAAKD,QAC1BqE,GAAkB/D,IAAIL,EAAKgF,GAC5BD,EAAgBM,OAAS,EAAG,OACtBC,EACD,qDAAQP,EAAgBQ,KAAK,8EAK9BC,QAAQC,KAAKH,KAkB7Bf,QAAQvD,UAGG0E,YAAU1E,GAAOL,gBACdgF,EAAsB,IAAIrF,OAC3BoE,SAASrC,QAAQjB,KAAKuE,OAGtB,MAAO3F,EAAKD,KAAa0B,KAAKyC,GAAkB,OAC3CrB,EAAYpB,KAAK4C,GAAwBa,IAAInF,GAC7CiF,EAAYvD,KAAK2C,GAAkBc,IAAIlF,GACvCY,EAAU,IAAIO,QAAQnB,EAAK,CAC7B6C,YACA+C,MAAOZ,EACPa,YAAa,sBAEXC,QAAQC,IAAItE,KAAKiD,SAASsB,UAAU,CACtCxE,OAAQ,CAAEzB,YACVa,UACAI,iBAGFR,YAAEA,EAAFC,eAAeA,GAAmBkF,QAIjC,CAAEnF,cAAaC,iBAAtB,IAaRgE,SAASzD,UAGE0E,YAAU1E,GAAOL,gBACdiF,QAAcvG,KAAK4G,OAAOC,KAAKzE,KAAKiD,SAAS1C,WAC7CmE,QAAgCP,EAAMQ,OACtCC,EAAoB,IAAIC,IAAI7E,KAAKyC,GAAiBqC,UAClDC,EAAc,OACf,MAAM5F,KAAWuF,EACbE,EAAkBpB,IAAIrE,EAAQZ,aACzB4F,EAAMa,OAAO7F,GACnB4F,EAAYpF,KAAKR,EAAQZ,YAM1B,CAAEwG,cAAT,IASRE,4BACWjF,KAAKyC,GAQhByC,sBACW,IAAIlF,KAAKyC,GAAiBkC,QAWrCzE,kBAAkB3B,SACRL,EAAY,IAAIC,IAAII,EAAKH,SAASC,aACjC2B,KAAKyC,GAAiBgB,IAAIvF,EAAUG,MAO/C8G,wBAAwB7G,UACb0B,KAAK4C,GAAwBa,IAAInF,uBAoBxBa,SACVZ,EAAMY,aAAmBO,QAAUP,EAAQZ,IAAMY,EACjDb,EAAW0B,KAAKE,kBAAkB3B,MACpCD,EAAU,cACUV,KAAK4G,OAAOC,KAAKzE,KAAKiD,SAAS1C,YACtC6E,MAAM9G,IAY3B+G,wBAAwB9G,SACdD,EAAW0B,KAAKE,kBAAkB3B,OACnCD,QACK,IAAIL,eAAa,oBAAqB,CAAEM,eAE1C+B,IACJA,EAAQnB,QAAU,IAAIO,QAAQnB,GAC9B+B,EAAQP,OAASuF,OAAOC,OAAO,CAAEjH,YAAYgC,EAAQP,QAC9CC,KAAKiD,SAASuC,OAAOlF,KCtRxC,IAAIT,EAKG,MAAM4F,EAAgC,KACpC5F,IACDA,EAAqB,IAAI2C,GAEtB3C,GCGX,MAAM6F,UAAsBC,QAiBxB7G,YAAYe,EAAoBS,UACd,EAAGnB,oBACPyG,EAAkB/F,EAAmBoF,yBACtC,MAAMY,KCtBhB,UAAgCtH,GAAKuH,4BAAEA,EAA8B,CAAC,QAAS,YAA1CC,eAAuDA,EAAiB,aAAxEC,UAAsFA,GAAY,EAAlGC,gBAAwGA,GAAqB,UAC/J/H,EAAY,IAAIC,IAAII,EAAKH,SAASC,MACxCH,EAAUgI,KAAO,SACXhI,EAAUG,WACV8H,ECHH,SAAmCjI,EAAW4H,EAA8B,QAG1E,MAAMM,IAAa,IAAIlI,EAAUS,aAAagG,QAC3CmB,EAA4BO,MAAMC,GAAWA,EAAOC,KAAKH,MACzDlI,EAAUS,aAAaqG,OAAOoB,UAG/BlI,CACV,CDNmCsI,CAA0BtI,EAAW4H,YAC/DK,EAAwB9H,KAC1B0H,GAAkBI,EAAwBM,SAASC,SAAS,KAAM,OAC5DC,EAAe,IAAIxI,IAAIgI,EAAwB9H,MACrDsI,EAAaF,UAAYV,QACnBY,EAAatI,QAEnB2H,EAAW,OACLY,EAAW,IAAIzI,IAAIgI,EAAwB9H,MACjDuI,EAASH,UAAY,cACfG,EAASvI,QAEf4H,EAAiB,OACXY,EAAiBZ,EAAgB,CAAE1H,IAAKL,QACzC,MAAM4I,KAAgBD,QACjBC,EAAazI,KAG9B,CDAqC0I,CAAsB5H,EAAQZ,IAAK+B,GAAU,OAC7DhC,EAAWsH,EAAgBnC,IAAIoC,MACjCvH,EAAU,OAEH,CAAEA,WAAU8C,UADDvB,EAAmBsF,wBAAwB7G,QAS5DuB,EAAmBoD,WG5BxC,SAAS+D,EAAS1G,SACRT,EAAqB4F,IACrBwB,EAAgB,IAAIvB,EAAc7F,EAAoBS,GAC5D4G,gBAAcD,EACjB,CCDD,SAAS/D,EAAShB,GACauD,IACRvC,SAAShB,EAC/B,wDCRD,MAWIpD,aAAYqI,YAAEA,EAAFtH,mBAAeA,SAMlBuH,gBAAkB,IAAMpH,KAAKC,GAAoBoH,cAAcrH,KAAKsH,SACpEA,GAAeH,OACflH,GACDJ,GAAsB4F,0DC3BlC,SAAoB7E,GACW6E,IACRxC,SAASrC,QAAQjB,QAAQiB,EAC/C,uCCFD,WAEIhD,KAAKyF,iBAAiB,YAAc9D,UAC1BgB,EAAYC,aAAWC,kBAC7BlB,EAAM0E,UCMe/E,OAAOqI,EAAqBC,EAnB/B,sBAqBhBC,SADmB7J,KAAK4G,OAAOG,QACC+C,QAAQnH,GAClCA,EAAUoH,SAASH,IACvBjH,EAAUoH,SAAS/J,KAAKgK,aAAaC,QACrCtH,IAAcgH,iBAEhBlD,QAAQC,IAAImD,EAAmBK,KAAKvH,GAAc3C,KAAK4G,OAAOQ,OAAOzE,MACpEkH,CAAP,EDdoBM,CAAqBxH,GAAWyH,MAAMC,WAS7D,4BEJD,SAAiC1J,UACFkH,IACDJ,wBAAwB9G,EACrD,sBCDD,SAA2BA,UACIkH,IACDvF,kBAAkB3B,EAC/C,kBCPD,SAAuBY,UACQsG,IACD4B,cAAclI,EAC3C,kCCHD,SAA0B+C,EAAS5B,GAC/B4C,EAAShB,GACT8E,EAAS1G,EACZ"}