import e from"postcss-value-parser";function r(r){const l=r.value.toLowerCase();if(!function(e,r){let n=!1,t=!1,u=!1;const o=r.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));for(let a=0;a<o.length;a++){const l=o[a];if("word"===l.type&&"from"===l.value.toLowerCase())return!1;"div"!==l.type||","!==l.value?e&&"word"===l.type&&l.value.endsWith("%")?u=!0:a===r.length-1&&"word"===l.type&&l.value.endsWith("%")&&(t=!0):n=!0}if(n&&(t||u))return!0;if(n)return!1;return!0}("rgb"===l||"rgba"===l,r.nodes))return;const i=function(e){let r=0;for(let n=0;n<e.length;n++){const t=e[n];if("div"===t.type&&","===t.value){if(r<2&&(t.value=" ",t.type="space"),2===r&&(t.value="/"),r>2)return;r++}}return e}(r.nodes),s=i.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));let c=null;if("hsl"===l||"hsla"===l?c=function(r){if(!function(r){if(!r||"word"!==r.type)return!1;if(!a(r))return!1;const n=e.unit(r.value);if(!n)return!1;const t=n.unit.toLowerCase();return!!n.number&&("deg"===t||"grad"===t||"rad"===t||"turn"===t||""===n.unit)}(r[0]))return null;if(!n(r[1]))return null;if(!n(r[2]))return null;const l={h:e.unit(r[0].value),hNode:r[0],s:e.unit(r[1].value),sNode:r[1],l:e.unit(r[2].value),lNode:r[2]};if(function(e){switch(e.unit.toLowerCase()){case"deg":return void(e.unit="");case"rad":return e.unit="",void(e.number=Math.round(180*parseFloat(e.number)/Math.PI).toString());case"grad":return e.unit="",void(e.number=Math.round(.9*parseFloat(e.number)).toString());case"turn":e.unit="",e.number=Math.round(360*parseFloat(e.number)).toString()}}(l.h),""!==l.h.unit)return null;l.hNode.value=l.h.number,o(r[3])&&(l.slash=r[3]);(n(r[4])||t(r[4])||u(r[4]))&&(l.alpha=r[4]);return l}(s):"rgb"!==l&&"rgba"!==l||(c=function(r){if(!n(r[0]))return null;if(!n(r[1]))return null;if(!n(r[2]))return null;const a={r:e.unit(r[0].value),rNode:r[0],g:e.unit(r[1].value),gNode:r[1],b:e.unit(r[2].value),bNode:r[2]};"%"===a.r.unit&&(a.r.number=String(Math.floor(Number(a.r.number)/100*255)),a.rNode.value=a.r.number);"%"===a.g.unit&&(a.g.number=String(Math.floor(Number(a.g.number)/100*255)),a.gNode.value=a.g.number);"%"===a.b.unit&&(a.b.number=String(Math.floor(Number(a.b.number)/100*255)),a.bNode.value=a.b.number);o(r[3])&&(a.slash=r[3]);(n(r[4])||t(r[4])||u(r[4]))&&(a.alpha=r[4]);return a}(s)),!c)return;if(s.length>3&&(!c.slash||!c.alpha))return;!function(r,n,t){"hsl"===r.value.toLowerCase()||"hsla"===r.value.toLowerCase()?r.value="hsl":"rgb"!==r.value.toLowerCase()&&"rgba"!==r.value.toLowerCase()||(r.value="rgb");if(!n||!t)return;"hsl"===r.value.toLowerCase()?r.value="hsla":r.value="rgba";if(n.value=",",n.before="",!function(r){if(!r||"word"!==r.type)return!1;if(!a(r))return!1;const n=e.unit(r.value);if(!n)return!1;return!!n.number}(t))return;const u=e.unit(t.value);if(!u)return;"%"===u.unit&&(u.number=String(parseFloat(u.number)/100),t.value=String(u.number))}(r,c.slash,c.alpha);const[f,d]=function(e){if(function(e){if(void 0!==e.r)return!0;return!1}(e))return[e.rNode,e.gNode,e.bNode];return[e.hNode,e.sNode,e.lNode]}(c);r.nodes.splice(r.nodes.indexOf(f)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),r.nodes.splice(r.nodes.indexOf(d)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""})}function n(r){if(!r||"word"!==r.type)return!1;if(!a(r))return!1;const n=e.unit(r.value);return!!n&&("%"===n.unit||""===n.unit)}function t(e){return e&&"function"===e.type&&"calc"===e.value.toLowerCase()}function u(e){return e&&"function"===e.type&&"var"===e.value.toLowerCase()}function o(e){return e&&"div"===e.type&&"/"===e.value}function a(r){if(!r||!r.value)return!1;try{return!1!==e.unit(r.value)}catch(e){return!1}}const l=n=>{const t="preserve"in Object(n)&&Boolean(n.preserve);return{postcssPlugin:"postcss-color-functional-notation",Declaration:(n,{result:u,postcss:o})=>{if(function(e){let r=e.parent;for(;r;)if("atrule"===r.type){if("supports"===r.name&&-1!==r.params.toLowerCase().indexOf("(color: rgb(0 0 0 / 0.5)) and (color: hsl(0 0% 0% / 0.5))"))return!0;r=r.parent}else r=r.parent;return!1}(n))return;const a=n.value,l=a.toLowerCase();if(!(l.includes("rgb")||l.includes("rgba")||l.includes("hsl")||l.includes("hsla")))return;let i;try{i=e(a)}catch(e){n.warn(u,`Failed to parse value '${a}' as a hsl or rgb function. Leaving the original value intact.`)}if(void 0===i)return;i.walk((e=>{if(!e.type||"function"!==e.type)return;const n=e.value.toLowerCase();"hsl"!==n&&"hsla"!==n&&"rgb"!==n&&"rgba"!==n||r(e)}));const s=String(i);if(s!==a)if(t&&n.variable){const e=n.parent,r="(color: rgb(0 0 0 / 0.5)) and (color: hsl(0 0% 0% / 0.5))",t=o.atRule({name:"supports",params:r,source:n.source}),u=e.clone();u.removeAll(),u.append(n.clone()),t.append(u);let a=e,l=e.next();for(;a&&l&&"atrule"===l.type&&"supports"===l.name&&l.params===r;)a=l,l=l.next();a.after(t),n.replaceWith(n.clone({value:s}))}else t?n.cloneBefore({value:s}):n.replaceWith(n.clone({value:s}))}}};l.postcss=!0;export{l as default};
