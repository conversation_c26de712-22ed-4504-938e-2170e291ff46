{"version": 3, "file": "filesize.esm.min.js", "sources": ["../src/filesize.js"], "sourcesContent": ["const b = /^(b|B)$/,\r\n\tsymbol = {\r\n\t\tiec: {\r\n\t\t\tbits: [\"bit\", \"Kibit\", \"Mibit\", \"Gibit\", \"Tibit\", \"Pibit\", \"Eibit\", \"Zibit\", \"Yibit\"],\r\n\t\t\tbytes: [\"B\", \"Ki<PERSON>\", \"Mi<PERSON>\", \"GiB\", \"TiB\", \"Pi<PERSON>\", \"EiB\", \"ZiB\", \"YiB\"]\r\n\t\t},\r\n\t\tjedec: {\r\n\t\t\tbits: [\"bit\", \"Kbit\", \"Mbit\", \"Gbit\", \"Tbit\", \"Pbit\", \"Ebit\", \"Zbit\", \"Ybit\"],\r\n\t\t\tbytes: [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\"]\r\n\t\t}\r\n\t},\r\n\tfullform = {\r\n\t\tiec: [\"\", \"kibi\", \"mebi\", \"gibi\", \"tebi\", \"pebi\", \"exbi\", \"zebi\", \"yobi\"],\r\n\t\tjedec: [\"\", \"kilo\", \"mega\", \"giga\", \"tera\", \"peta\", \"exa\", \"zetta\", \"yotta\"]\r\n\t},\r\n\troundingFuncs = {\r\n\t\tfloor: Math.floor,\r\n\t\tceil: Math.ceil\r\n\t};\r\n\r\n/**\r\n * filesize\r\n *\r\n * @method filesize\r\n * @param  {Mixed}   arg        String, Int or Float to transform\r\n * @param  {Object}  descriptor [Optional] Flags\r\n * @return {String}             Readable file size String\r\n */\r\nfunction filesize (arg, descriptor = {}) {\r\n\tlet result = [],\r\n\t\tval = 0,\r\n\t\te, base, bits, ceil, full, fullforms, locale, localeOptions, neg, num, output, pad, round, u, unix, separator, spacer, standard, symbols, roundingFunc, precision;\r\n\r\n\tif (isNaN(arg)) {\r\n\t\tthrow new TypeError(\"Invalid number\");\r\n\t}\r\n\r\n\tbits = descriptor.bits === true;\r\n\tunix = descriptor.unix === true;\r\n\tpad = descriptor.pad === true;\r\n\tbase = descriptor.base || 10;\r\n\tround = descriptor.round !== void 0 ? descriptor.round : unix ? 1 : 2;\r\n\tlocale = descriptor.locale !== void 0 ? descriptor.locale : \"\";\r\n\tlocaleOptions = descriptor.localeOptions || {};\r\n\tseparator = descriptor.separator !== void 0 ? descriptor.separator : \"\";\r\n\tspacer = descriptor.spacer !== void 0 ? descriptor.spacer : unix ? \"\" : \" \";\r\n\tsymbols = descriptor.symbols || {};\r\n\tstandard = base === 2 ? descriptor.standard || \"iec\" : \"jedec\";\r\n\toutput = descriptor.output || \"string\";\r\n\tfull = descriptor.fullform === true;\r\n\tfullforms = descriptor.fullforms instanceof Array ? descriptor.fullforms : [];\r\n\te = descriptor.exponent !== void 0 ? descriptor.exponent : -1;\r\n\troundingFunc = roundingFuncs[descriptor.roundingMethod] || Math.round;\r\n\tnum = Number(arg);\r\n\tneg = num < 0;\r\n\tceil = base > 2 ? 1000 : 1024;\r\n\tprecision = isNaN(descriptor.precision) === false ? parseInt(descriptor.precision, 10) : 0;\r\n\r\n\t// Flipping a negative number to determine the size\r\n\tif (neg) {\r\n\t\tnum = -num;\r\n\t}\r\n\r\n\t// Determining the exponent\r\n\tif (e === -1 || isNaN(e)) {\r\n\t\te = Math.floor(Math.log(num) / Math.log(ceil));\r\n\r\n\t\tif (e < 0) {\r\n\t\t\te = 0;\r\n\t\t}\r\n\t}\r\n\r\n\t// Exceeding supported length, time to reduce & multiply\r\n\tif (e > 8) {\r\n\t\tif (precision > 0) {\r\n\t\t\tprecision += 8 - e;\r\n\t\t}\r\n\r\n\t\te = 8;\r\n\t}\r\n\r\n\tif (output === \"exponent\") {\r\n\t\treturn e;\r\n\t}\r\n\r\n\t// Zero is now a special case because bytes divide by 1\r\n\tif (num === 0) {\r\n\t\tresult[0] = 0;\r\n\t\tu = result[1] = unix ? \"\" : symbol[standard][bits ? \"bits\" : \"bytes\"][e];\r\n\t} else {\r\n\t\tval = num / (base === 2 ? Math.pow(2, e * 10) : Math.pow(1000, e));\r\n\r\n\t\tif (bits) {\r\n\t\t\tval = val * 8;\r\n\r\n\t\t\tif (val >= ceil && e < 8) {\r\n\t\t\t\tval = val / ceil;\r\n\t\t\t\te++;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst p = Math.pow(10, e > 0 ? round : 0);\r\n\t\tresult[0] = roundingFunc(val * p) / p;\r\n\r\n\t\tif (result[0] === ceil && e < 8 && descriptor.exponent === void 0) {\r\n\t\t\tresult[0] = 1;\r\n\t\t\te++;\r\n\t\t}\r\n\r\n\t\tu = result[1] = base === 10 && e === 1 ? bits ? \"kbit\" : \"kB\" : symbol[standard][bits ? \"bits\" : \"bytes\"][e];\r\n\r\n\t\tif (unix) {\r\n\t\t\tresult[1] = result[1].charAt(0);\r\n\r\n\t\t\tif (b.test(result[1])) {\r\n\t\t\t\tresult[0] = Math.floor(result[0]);\r\n\t\t\t\tresult[1] = \"\";\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Decorating a 'diff'\r\n\tif (neg) {\r\n\t\tresult[0] = -result[0];\r\n\t}\r\n\r\n\t// Setting optional precision\r\n\tif (precision > 0) {\r\n\t\tresult[0] = result[0].toPrecision(precision);\r\n\t}\r\n\r\n\t// Applying custom symbol\r\n\tresult[1] = symbols[result[1]] || result[1];\r\n\r\n\tif (locale === true) {\r\n\t\tresult[0] = result[0].toLocaleString();\r\n\t} else if (locale.length > 0) {\r\n\t\tresult[0] = result[0].toLocaleString(locale, localeOptions);\r\n\t} else if (separator.length > 0) {\r\n\t\tresult[0] = result[0].toString().replace(\".\", separator);\r\n\t}\r\n\r\n\tif (pad && Number.isInteger(result[0]) === false && round > 0) {\r\n\t\tconst x = separator || \".\",\r\n\t\t\ttmp = result[0].toString().split(x),\r\n\t\t\ts = tmp[1] || \"\",\r\n\t\t\tl = s.length,\r\n\t\t\tn = round - l;\r\n\r\n\t\tresult[0] = `${tmp[0]}${x}${s.padEnd(l + n, \"0\")}`;\r\n\t}\r\n\r\n\tif (full) {\r\n\t\tresult[1] = fullforms[e] ? fullforms[e] : fullform[standard][e] + (bits ? \"bit\" : \"byte\") + (result[0] === 1 ? \"\" : \"s\");\r\n\t}\r\n\r\n\t// Returning Array, Object, or String (default)\r\n\treturn output === \"array\" ? result : output === \"object\" ? {value: result[0], symbol: result[1], exponent: e, unit: u} : result.join(spacer);\r\n}\r\n\r\n// Partial application for functional programming\r\nfilesize.partial = opt => arg => filesize(arg, opt);\r\n\r\nexport default filesize;\r\n"], "names": ["b", "symbol", "iec", "bits", "bytes", "jedec", "fullform", "roundingFuncs", "floor", "Math", "ceil", "filesize", "arg", "descriptor", "e", "base", "full", "fullforms", "locale", "localeOptions", "neg", "num", "output", "pad", "round", "u", "unix", "separator", "spacer", "standard", "symbols", "roundingFunc", "precision", "result", "val", "isNaN", "TypeError", "Array", "exponent", "roundingMethod", "Number", "parseInt", "log", "pow", "p", "char<PERSON>t", "test", "toPrecision", "toLocaleString", "length", "toString", "replace", "isInteger", "x", "tmp", "split", "s", "l", "n", "padEnd", "value", "unit", "join", "partial", "opt"], "mappings": ";;;;AAAA,MAAMA,EAAI,UACTC,EAAS,CACRC,IAAK,CACJC,KAAM,CAAC,MAAO,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,SAC7EC,MAAO,CAAC,IAAK,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QAE/DC,MAAO,CACNF,KAAM,CAAC,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACtEC,MAAO,CAAC,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,QAGzDE,EAAW,CACVJ,IAAK,CAAC,GAAI,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAClEG,MAAO,CAAC,GAAI,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,QAAS,UAErEE,EAAgB,CACfC,MAAOC,KAAKD,MACZE,KAAMD,KAAKC,MAWb,SAASC,EAAUC,EAAKC,EAAa,IACpC,IAECC,EAAGC,EAAMZ,EAAMO,EAAMM,EAAMC,EAAWC,EAAQC,EAAeC,EAAKC,EAAKC,EAAQC,EAAKC,EAAOC,EAAGC,EAAMC,EAAWC,EAAQC,EAAUC,EAASC,EAAcC,EAFrJC,EAAS,GACZC,EAAM,EAGP,GAAIC,MAAMvB,GACT,MAAM,IAAIwB,UAAU,kBA+CrB,GA5CAjC,GAA2B,IAApBU,EAAWV,KAClBuB,GAA2B,IAApBb,EAAWa,KAClBH,GAAyB,IAAnBV,EAAWU,IACjBR,EAAOF,EAAWE,MAAQ,GAC1BS,OAA6B,IAArBX,EAAWW,MAAmBX,EAAWW,MAAQE,EAAO,EAAI,EACpER,OAA+B,IAAtBL,EAAWK,OAAoBL,EAAWK,OAAS,GAC5DC,EAAgBN,EAAWM,eAAiB,GAC5CQ,OAAqC,IAAzBd,EAAWc,UAAuBd,EAAWc,UAAY,GACrEC,OAA+B,IAAtBf,EAAWe,OAAoBf,EAAWe,OAASF,EAAO,GAAK,IACxEI,EAAUjB,EAAWiB,SAAW,GAChCD,EAAoB,IAATd,EAAaF,EAAWgB,UAAY,MAAQ,QACvDP,EAAST,EAAWS,QAAU,SAC9BN,GAA+B,IAAxBH,EAAWP,SAClBW,EAAYJ,EAAWI,qBAAqBoB,MAAQxB,EAAWI,UAAY,GAC3EH,OAA4B,IAAxBD,EAAWyB,SAAsBzB,EAAWyB,UAAY,EAC5DP,EAAexB,EAAcM,EAAW0B,iBAAmB9B,KAAKe,MAChEH,EAAMmB,OAAO5B,GACbQ,EAAMC,EAAM,EACZX,EAAOK,EAAO,EAAI,IAAO,KACzBiB,GAA4C,IAAhCG,MAAMtB,EAAWmB,WAAuBS,SAAS5B,EAAWmB,UAAW,IAAM,EAGrFZ,IACHC,GAAOA,KAIG,IAAPP,GAAYqB,MAAMrB,MACrBA,EAAIL,KAAKD,MAAMC,KAAKiC,IAAIrB,GAAOZ,KAAKiC,IAAIhC,IAEpCI,EAAI,IACPA,EAAI,IAKFA,EAAI,IACHkB,EAAY,IACfA,GAAa,EAAIlB,GAGlBA,EAAI,GAGU,aAAXQ,EACH,OAAOR,EAIR,GAAY,IAARO,EACHY,EAAO,GAAK,EACZR,EAAIQ,EAAO,GAAKP,EAAO,GAAKzB,EAAO4B,GAAU1B,EAAO,OAAS,SAASW,OAChE,CACNoB,EAAMb,GAAgB,IAATN,EAAaN,KAAKkC,IAAI,EAAO,GAAJ7B,GAAUL,KAAKkC,IAAI,IAAM7B,IAE3DX,IACH+B,GAAY,EAERA,GAAOxB,GAAQI,EAAI,IACtBoB,GAAYxB,EACZI,MAIF,MAAM8B,EAAInC,KAAKkC,IAAI,GAAI7B,EAAI,EAAIU,EAAQ,GACvCS,EAAO,GAAKF,EAAaG,EAAMU,GAAKA,EAEhCX,EAAO,KAAOvB,GAAQI,EAAI,QAA6B,IAAxBD,EAAWyB,WAC7CL,EAAO,GAAK,EACZnB,KAGDW,EAAIQ,EAAO,GAAc,KAATlB,GAAqB,IAAND,EAAUX,EAAO,OAAS,KAAOF,EAAO4B,GAAU1B,EAAO,OAAS,SAASW,GAEtGY,IACHO,EAAO,GAAKA,EAAO,GAAGY,OAAO,GAEzB7C,EAAE8C,KAAKb,EAAO,MACjBA,EAAO,GAAKxB,KAAKD,MAAMyB,EAAO,IAC9BA,EAAO,GAAK,KA0Bf,GApBIb,IACHa,EAAO,IAAMA,EAAO,IAIjBD,EAAY,IACfC,EAAO,GAAKA,EAAO,GAAGc,YAAYf,IAInCC,EAAO,GAAKH,EAAQG,EAAO,KAAOA,EAAO,IAE1B,IAAXf,EACHe,EAAO,GAAKA,EAAO,GAAGe,iBACZ9B,EAAO+B,OAAS,EAC1BhB,EAAO,GAAKA,EAAO,GAAGe,eAAe9B,EAAQC,GACnCQ,EAAUsB,OAAS,IAC7BhB,EAAO,GAAKA,EAAO,GAAGiB,WAAWC,QAAQ,IAAKxB,IAG3CJ,IAAuC,IAAhCiB,OAAOY,UAAUnB,EAAO,KAAiBT,EAAQ,EAAG,CAC9D,MAAM6B,EAAI1B,GAAa,IACtB2B,EAAMrB,EAAO,GAAGiB,WAAWK,MAAMF,GACjCG,EAAIF,EAAI,IAAM,GACdG,EAAID,EAAEP,OACNS,EAAIlC,EAAQiC,EAEbxB,EAAO,GAAK,GAAGqB,EAAI,KAAKD,IAAIG,EAAEG,OAAOF,EAAIC,EAAG,OAQ7C,OALI1C,IACHiB,EAAO,GAAKhB,EAAUH,GAAKG,EAAUH,GAAKR,EAASuB,GAAUf,IAAMX,EAAO,MAAQ,SAAyB,IAAd8B,EAAO,GAAW,GAAK,MAInG,UAAXX,EAAqBW,EAAoB,WAAXX,EAAsB,CAACsC,MAAO3B,EAAO,GAAIhC,OAAQgC,EAAO,GAAIK,SAAUxB,EAAG+C,KAAMpC,GAAKQ,EAAO6B,KAAKlC,GAItIjB,EAASoD,QAAUC,GAAOpD,GAAOD,EAASC,EAAKoD"}