[{"F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\index.js": "1", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\App.js": "2", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\store\\index.js": "3", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\constants.js": "4", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\axios.js": "5", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\helpers.js": "6", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Home.js": "7", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Login.js": "8", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\BackgroundAuth.js": "9", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\BackgroundTask.js": "10", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\SignUp.js": "11", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\FullLoading.js": "12", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\RedirectTo.js": "13", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Overlay.js": "14", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Thread.js": "15", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\BackgroundSetup.js": "16", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\LoadingDialog\\index.js": "17", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\layouts\\Sidebar\\index.js": "18", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\layouts\\Containers\\index.js": "19", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\MessageBar\\index.js": "20", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\regex.js": "21", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\OuterElements.js": "22", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\ChatMessage.js": "23", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Button.js": "24", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\SmallElements.js": "25", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\YesNoDialog.js": "26", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\DataDialogs\\ThreadDialog.js": "27", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\LoadingDialog\\LoadingDialogElements.js": "28", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Typography\\index.js": "29", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\layouts\\Sidebar\\SidebarElements.js": "30", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\List.js": "31", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\breakpoint.js": "32", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextAreas\\index.js": "33", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\MessageBar\\MessageBarElements.js": "34", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Tag.js": "35", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextAreas\\Elements.js": "36", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextFields\\index.js": "37", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Dialog\\index.js": "38", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Dialog\\DialogElements.js": "39", "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextFields\\Elements.js": "40"}, {"size": 359, "mtime": 1753876452000, "results": "41", "hashOfConfig": "42"}, {"size": 6071, "mtime": 1753876452000, "results": "43", "hashOfConfig": "42"}, {"size": 2224, "mtime": 1753876452000, "results": "44", "hashOfConfig": "42"}, {"size": 612, "mtime": 1753876452000, "results": "45", "hashOfConfig": "42"}, {"size": 233, "mtime": 1753876452000, "results": "46", "hashOfConfig": "42"}, {"size": 3045, "mtime": 1753876452000, "results": "47", "hashOfConfig": "42"}, {"size": 7456, "mtime": 1753876452000, "results": "48", "hashOfConfig": "42"}, {"size": 5319, "mtime": 1753876452000, "results": "49", "hashOfConfig": "42"}, {"size": 1279, "mtime": 1753876452000, "results": "50", "hashOfConfig": "42"}, {"size": 389, "mtime": 1753876452000, "results": "51", "hashOfConfig": "42"}, {"size": 5937, "mtime": 1753876452000, "results": "52", "hashOfConfig": "42"}, {"size": 482, "mtime": 1753876452000, "results": "53", "hashOfConfig": "42"}, {"size": 582, "mtime": 1753876452000, "results": "54", "hashOfConfig": "42"}, {"size": 10196, "mtime": 1753876452000, "results": "55", "hashOfConfig": "42"}, {"size": 12226, "mtime": 1753876452000, "results": "56", "hashOfConfig": "42"}, {"size": 1458, "mtime": 1753876452000, "results": "57", "hashOfConfig": "42"}, {"size": 446, "mtime": 1753876452000, "results": "58", "hashOfConfig": "42"}, {"size": 3063, "mtime": 1753876452000, "results": "59", "hashOfConfig": "42"}, {"size": 1879, "mtime": 1753876452000, "results": "60", "hashOfConfig": "42"}, {"size": 344, "mtime": 1753876452000, "results": "61", "hashOfConfig": "42"}, {"size": 150, "mtime": 1753876452000, "results": "62", "hashOfConfig": "42"}, {"size": 1435, "mtime": 1753876452000, "results": "63", "hashOfConfig": "42"}, {"size": 12156, "mtime": 1753876452000, "results": "64", "hashOfConfig": "42"}, {"size": 3374, "mtime": 1753876452000, "results": "65", "hashOfConfig": "42"}, {"size": 1700, "mtime": 1753876452000, "results": "66", "hashOfConfig": "42"}, {"size": 1309, "mtime": 1753876452000, "results": "67", "hashOfConfig": "42"}, {"size": 3064, "mtime": 1753876452000, "results": "68", "hashOfConfig": "42"}, {"size": 645, "mtime": 1753876452000, "results": "69", "hashOfConfig": "42"}, {"size": 342, "mtime": 1753876452000, "results": "70", "hashOfConfig": "42"}, {"size": 499, "mtime": 1753876452000, "results": "71", "hashOfConfig": "42"}, {"size": 3107, "mtime": 1753876452000, "results": "72", "hashOfConfig": "42"}, {"size": 1708, "mtime": 1753876452000, "results": "73", "hashOfConfig": "42"}, {"size": 2542, "mtime": 1753876452000, "results": "74", "hashOfConfig": "42"}, {"size": 555, "mtime": 1753876452000, "results": "75", "hashOfConfig": "42"}, {"size": 337, "mtime": 1753876452000, "results": "76", "hashOfConfig": "42"}, {"size": 1813, "mtime": 1753876452000, "results": "77", "hashOfConfig": "42"}, {"size": 2555, "mtime": 1753876452000, "results": "78", "hashOfConfig": "42"}, {"size": 1901, "mtime": 1753876452000, "results": "79", "hashOfConfig": "42"}, {"size": 1495, "mtime": 1753876452000, "results": "80", "hashOfConfig": "42"}, {"size": 1825, "mtime": 1753876452000, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "payi90", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\App.js", ["202", "203", "204", "205", "206", "207"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\store\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\constants.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\axios.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\helpers.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Home.js", ["208"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Login.js", ["209", "210", "211"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\BackgroundAuth.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\BackgroundTask.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\SignUp.js", ["212"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\FullLoading.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\RedirectTo.js", ["213"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Overlay.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\Thread.js", ["214", "215"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\views\\BackgroundSetup.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\LoadingDialog\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\layouts\\Sidebar\\index.js", ["216"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\layouts\\Containers\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\MessageBar\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\regex.js", ["217", "218", "219", "220", "221", "222", "223", "224", "225", "226"], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\OuterElements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\ChatMessage.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Button.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\SmallElements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\YesNoDialog.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\DataDialogs\\ThreadDialog.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\LoadingDialog\\LoadingDialogElements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Typography\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\layouts\\Sidebar\\SidebarElements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\List.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\utils\\breakpoint.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextAreas\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\MessageBar\\MessageBarElements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Tag.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextAreas\\Elements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextFields\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Dialog\\index.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\Dialog\\DialogElements.js", [], [], "F:\\zuomianwenjian\\ss\\neuralagent-main\\desktop\\neuralagent-app\\src\\components\\Elements\\TextFields\\Elements.js", [], [], {"ruleId": "227", "severity": 1, "message": "228", "line": 90, "column": 10, "nodeType": "229", "messageId": "230", "endLine": 90, "endColumn": 21}, {"ruleId": "227", "severity": 1, "message": "231", "line": 92, "column": 10, "nodeType": "229", "messageId": "230", "endLine": 92, "endColumn": 25}, {"ruleId": "227", "severity": 1, "message": "232", "line": 92, "column": 27, "nodeType": "229", "messageId": "230", "endLine": 92, "endColumn": 43}, {"ruleId": "233", "severity": 1, "message": "234", "line": 121, "column": 6, "nodeType": "235", "endLine": 121, "endColumn": 8, "suggestions": "236"}, {"ruleId": "233", "severity": 1, "message": "237", "line": 148, "column": 6, "nodeType": "235", "endLine": 148, "endColumn": 8, "suggestions": "238"}, {"ruleId": "233", "severity": 1, "message": "239", "line": 176, "column": 6, "nodeType": "235", "endLine": 176, "endColumn": 8, "suggestions": "240"}, {"ruleId": "233", "severity": 1, "message": "241", "line": 168, "column": 6, "nodeType": "235", "endLine": 168, "endColumn": 8, "suggestions": "242"}, {"ruleId": "227", "severity": 1, "message": "243", "line": 13, "column": 18, "nodeType": "229", "messageId": "230", "endLine": 13, "endColumn": 30}, {"ruleId": "227", "severity": 1, "message": "244", "line": 13, "column": 32, "nodeType": "229", "messageId": "230", "endLine": 13, "endColumn": 45}, {"ruleId": "227", "severity": 1, "message": "245", "line": 21, "column": 10, "nodeType": "229", "messageId": "230", "endLine": 21, "endColumn": 21}, {"ruleId": "227", "severity": 1, "message": "246", "line": 1, "column": 17, "nodeType": "229", "messageId": "230", "endLine": 1, "endColumn": 26}, {"ruleId": "233", "severity": 1, "message": "247", "line": 22, "column": 6, "nodeType": "235", "endLine": 22, "endColumn": 8, "suggestions": "248"}, {"ruleId": "233", "severity": 1, "message": "249", "line": 256, "column": 6, "nodeType": "235", "endLine": 256, "endColumn": 11, "suggestions": "250"}, {"ruleId": "233", "severity": 1, "message": "249", "line": 277, "column": 6, "nodeType": "235", "endLine": 277, "endColumn": 8, "suggestions": "251"}, {"ruleId": "233", "severity": 1, "message": "252", "line": 54, "column": 6, "nodeType": "235", "endLine": 54, "endColumn": 8, "suggestions": "253"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 2, "column": 14, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 15, "suggestions": "258"}, {"ruleId": "254", "severity": 1, "message": "259", "line": 2, "column": 22, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 23, "suggestions": "260"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 2, "column": 38, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 39, "suggestions": "261"}, {"ruleId": "254", "severity": 1, "message": "259", "line": 2, "column": 46, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 47, "suggestions": "262"}, {"ruleId": "254", "severity": 1, "message": "259", "line": 2, "column": 55, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 56, "suggestions": "263"}, {"ruleId": "254", "severity": 1, "message": "259", "line": 2, "column": 59, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 60, "suggestions": "264"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 2, "column": 75, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 76, "suggestions": "265"}, {"ruleId": "254", "severity": 1, "message": "259", "line": 2, "column": 83, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 84, "suggestions": "266"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 2, "column": 100, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 101, "suggestions": "267"}, {"ruleId": "254", "severity": 1, "message": "259", "line": 2, "column": 108, "nodeType": "256", "messageId": "257", "endLine": 2, "endColumn": 109, "suggestions": "268"}, "no-unused-vars", "'_windowDims' is assigned a value but never used.", "Identifier", "unusedVar", "'isMobileBarOpen' is assigned a value but never used.", "'setMobileBarOpen' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserInfo'. Either include them or remove the dependency array.", "ArrayExpression", ["269"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["270"], "React Hook useEffect has a missing dependency: 'cancelAllRunningTasks'. Either include it or remove the dependency array.", ["271"], "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["272"], "'AvatarButton' is defined but never used.", "'AvatarBtnIcon' is defined but never used.", "'FaFacebookF' is defined but never used.", "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'linkType', 'navigate', 'redirectType', and 'to'. Either include them or remove the dependency array.", ["273"], "React Hook useEffect has missing dependencies: 'getThread' and 'getThreadMessages'. Either include them or remove the dependency array.", ["274"], ["275"], "React Hook useEffect has a missing dependency: 'getThreads'. Either include it or remove the dependency array.", ["276"], "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["277", "278"], "Unnecessary escape character: \\\".", ["279", "280"], ["281", "282"], ["283", "284"], ["285", "286"], ["287", "288"], ["289", "290"], ["291", "292"], ["293", "294"], ["295", "296"], {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"messageId": "313", "fix": "314", "desc": "315"}, {"messageId": "316", "fix": "317", "desc": "318"}, {"messageId": "313", "fix": "319", "desc": "315"}, {"messageId": "316", "fix": "320", "desc": "318"}, {"messageId": "313", "fix": "321", "desc": "315"}, {"messageId": "316", "fix": "322", "desc": "318"}, {"messageId": "313", "fix": "323", "desc": "315"}, {"messageId": "316", "fix": "324", "desc": "318"}, {"messageId": "313", "fix": "325", "desc": "315"}, {"messageId": "316", "fix": "326", "desc": "318"}, {"messageId": "313", "fix": "327", "desc": "315"}, {"messageId": "316", "fix": "328", "desc": "318"}, {"messageId": "313", "fix": "329", "desc": "315"}, {"messageId": "316", "fix": "330", "desc": "318"}, {"messageId": "313", "fix": "331", "desc": "315"}, {"messageId": "316", "fix": "332", "desc": "318"}, {"messageId": "313", "fix": "333", "desc": "315"}, {"messageId": "316", "fix": "334", "desc": "318"}, {"messageId": "313", "fix": "335", "desc": "315"}, {"messageId": "316", "fix": "336", "desc": "318"}, "Update the dependencies array to be: [dispatch, getUserInfo]", {"range": "337", "text": "338"}, "Update the dependencies array to be: [dispatch]", {"range": "339", "text": "340"}, "Update the dependencies array to be: [cancelAllRunningTasks]", {"range": "341", "text": "342"}, "Update the dependencies array to be: [navigate]", {"range": "343", "text": "344"}, "Update the dependencies array to be: [linkType, navigate, redirectType, to]", {"range": "345", "text": "346"}, "Update the dependencies array to be: [getThread, getThreadMessages, tid]", {"range": "347", "text": "348"}, "Update the dependencies array to be: [getThread, getThreadMessages]", {"range": "349", "text": "350"}, "Update the dependencies array to be: [getThreads]", {"range": "351", "text": "352"}, "removeEscape", {"range": "353", "text": "354"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "355", "text": "356"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "357", "text": "354"}, {"range": "358", "text": "356"}, {"range": "359", "text": "354"}, {"range": "360", "text": "356"}, {"range": "361", "text": "354"}, {"range": "362", "text": "356"}, {"range": "363", "text": "354"}, {"range": "364", "text": "356"}, {"range": "365", "text": "354"}, {"range": "366", "text": "356"}, {"range": "367", "text": "354"}, {"range": "368", "text": "356"}, {"range": "369", "text": "354"}, {"range": "370", "text": "356"}, {"range": "371", "text": "354"}, {"range": "372", "text": "356"}, {"range": "373", "text": "354"}, {"range": "374", "text": "356"}, [4313, 4315], "[dispatch, getUserInfo]", [5032, 5034], "[dispatch]", [5763, 5765], "[cancelAllRunningTasks]", [5215, 5217], "[navigate]", [519, 521], "[linkType, navigate, redirectType, to]", [7805, 7810], "[getThread, getThreadMessages, tid]", [8276, 8278], "[getThread, getThreadMessages]", [1521, 1523], "[getThreads]", [41, 42], "", [41, 41], "\\", [49, 50], [49, 49], [65, 66], [65, 65], [73, 74], [73, 73], [82, 83], [82, 82], [86, 87], [86, 86], [102, 103], [102, 102], [110, 111], [110, 110], [127, 128], [127, 127], [135, 136], [135, 135]]