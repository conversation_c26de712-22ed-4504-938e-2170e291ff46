import{j as e}from"./jsx-runtime-QvZ8i92b.js";import{c as b,a as n}from"./animation-CmKoZPRv.js";import"./index-uubelm5h.js";const x=b("SyncLoader",`33% {transform: translateY(10px)}
  66% {transform: translateY(-10px)}
  100% {transform: translateY(0)}`,"sync");function s({loading:t=!0,color:d="#000000",speedMultiplier:u=1,cssOverride:c={},size:i=15,margin:m=2,...y}){const f={display:"inherit",...c},a=g=>({backgroundColor:d,width:n(i),height:n(i),margin:n(m),borderRadius:"100%",display:"inline-block",animation:`${x} ${.6/u}s ${g*.07}s infinite ease-in-out`,animationFillMode:"both"});return t?e.jsxs("span",{style:f,...y,children:[e.jsx("span",{style:a(1)}),e.jsx("span",{style:a(2)}),e.jsx("span",{style:a(3)})]}):null}try{s.displayName="SyncLoader",s.__docgenInfo={description:"",displayName:"SyncLoader",props:{size:{defaultValue:{value:"15"},description:"",name:"size",required:!1,type:{name:"LengthType"}},margin:{defaultValue:{value:"2"},description:"",name:"margin",required:!1,type:{name:"LengthType"}},color:{defaultValue:{value:"#000000"},description:"",name:"color",required:!1,type:{name:"string"}},loading:{defaultValue:{value:"true"},description:"",name:"loading",required:!1,type:{name:"boolean"}},cssOverride:{defaultValue:{value:"{}"},description:"",name:"cssOverride",required:!1,type:{name:"CSSProperties"}},speedMultiplier:{defaultValue:{value:"1"},description:"",name:"speedMultiplier",required:!1,type:{name:"number"}}}}}catch{}const S={component:s,argTypes:{size:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}},margin:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}}}},r={};var o,l,p;r.parameters={...r.parameters,docs:{...(o=r.parameters)==null?void 0:o.docs,source:{originalSource:"{}",...(p=(l=r.parameters)==null?void 0:l.docs)==null?void 0:p.source}}};const V=["Primary"];export{r as Primary,V as __namedExportsOrder,S as default};
