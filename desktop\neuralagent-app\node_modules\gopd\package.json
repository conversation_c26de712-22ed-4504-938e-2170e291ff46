{"name": "gopd", "version": "1.2.0", "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "main": "index.js", "exports": {".": "./index.js", "./gOPD": "./gOPD.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "tsc -p . && attw -P", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/gopd.git"}, "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "homepage": "https://github.com/ljharb/gopd#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}