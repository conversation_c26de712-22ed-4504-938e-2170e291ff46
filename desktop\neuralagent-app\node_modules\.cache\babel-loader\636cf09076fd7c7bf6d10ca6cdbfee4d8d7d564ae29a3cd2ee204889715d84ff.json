{"ast": null, "code": "import styled from 'styled-components';\nexport const MainContainer = styled.div`\n  width: 100%;\n  background: var(--primary-color);\n`;\nexport const AccountContainer = styled.div`\n  min-height: 100vh;\n  padding: 15px;\n  max-width: var(--max-login-width);\n  margin-left: auto;\n  margin-right: auto;\n`;\nexport const AccountHeader = styled.div`\n  display: flex;\n  align-items: center;\n  margin-top: 20px;\n`;\nexport const AccountDiv = styled.div`\n  min-height: 80vh;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nexport const InfoContainer = styled.div`\n  width: 100%;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n  color: #fff;\n  display: flex;\n  flex-direction: column;\n`;\nexport const FormTitle = styled.div`\n  font-size: 40px;\n  text-align: center;\n  font-weight: 300;\n`;\nexport const AccountTextField = styled.input`\n  background: #fff;\n  width: 100%;\n  padding: 16px 20px;\n  color: #000;\n  font-size: 18px;\n  margin-bottom: 20px;\n  border-radius: 10px;\n  font-family: inherit;\n  transition: 0.1s ease;\n  resize: none;\n  outline: none;\n  border: none;\n\n  &::placeholder {\n    color: rgba(0, 0, 0, 0.6);\n    font-size: 18px;\n    font-weight: 500;\n    user-select: none;\n  }\n`;\nexport const OrDiv = styled.div`\n  display: flex;\n  align-items: center;\n  text-align: center;\n\n  &::before, &::after {\n  content: '';\n  flex: 1;\n  border-bottom: 2px solid #fff;\n  margin: 0 10px;\n}\n`;", "map": {"version": 3, "names": ["styled", "MainContainer", "div", "A<PERSON><PERSON><PERSON><PERSON><PERSON>", "Acco<PERSON><PERSON><PERSON><PERSON>", "AccountDiv", "InfoContainer", "FormTitle", "AccountTextField", "input", "OrDiv"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/OuterElements.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const MainContainer = styled.div`\n  width: 100%;\n  background: var(--primary-color);\n`\n\nexport const AccountContainer = styled.div`\n  min-height: 100vh;\n  padding: 15px;\n  max-width: var(--max-login-width);\n  margin-left: auto;\n  margin-right: auto;\n`\n\nexport const AccountHeader = styled.div`\n  display: flex;\n  align-items: center;\n  margin-top: 20px;\n`\n\nexport const AccountDiv = styled.div`\n  min-height: 80vh;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`\n\nexport const InfoContainer = styled.div`\n  width: 100%;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n  color: #fff;\n  display: flex;\n  flex-direction: column;\n`\n\nexport const FormTitle = styled.div`\n  font-size: 40px;\n  text-align: center;\n  font-weight: 300;\n`\n\nexport const AccountTextField = styled.input`\n  background: #fff;\n  width: 100%;\n  padding: 16px 20px;\n  color: #000;\n  font-size: 18px;\n  margin-bottom: 20px;\n  border-radius: 10px;\n  font-family: inherit;\n  transition: 0.1s ease;\n  resize: none;\n  outline: none;\n  border: none;\n\n  &::placeholder {\n    color: rgba(0, 0, 0, 0.6);\n    font-size: 18px;\n    font-weight: 500;\n    user-select: none;\n  }\n`\n\nexport const OrDiv = styled.div`\n  display: flex;\n  align-items: center;\n  text-align: center;\n\n  &::before, &::after {\n  content: '';\n  flex: 1;\n  border-bottom: 2px solid #fff;\n  margin: 0 10px;\n}\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,aAAa,GAAGD,MAAM,CAACE,GAAG;AACvC;AACA;AACA,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGH,MAAM,CAACE,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAME,aAAa,GAAGJ,MAAM,CAACE,GAAG;AACvC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMG,UAAU,GAAGL,MAAM,CAACE,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMI,aAAa,GAAGN,MAAM,CAACE,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMK,SAAS,GAAGP,MAAM,CAACE,GAAG;AACnC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMM,gBAAgB,GAAGR,MAAM,CAACS,KAAK;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,KAAK,GAAGV,MAAM,CAACE,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}