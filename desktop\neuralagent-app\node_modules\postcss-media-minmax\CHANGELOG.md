# 5.0.0 2021-01-12

- Updated: Support for PostCSS v8+
- Updated: Support for Node v10+

# 4.0.0 2017-09-17

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

# 3.0.0 2017-05-10

Change: Use PostCSS 6 API.

# 2.1.2 2016-04-01

Fix: incorrect output when using both > and >= (or similar).(#12)

# 2.1.1 2015-11-26

Fix: Pixels rounding errors in fractional pixels media queries.

# 2.1.0 2015-09-08

Add: Support for `<` and `>` without `=`.

# 2.0.0 2015-09-05

Change: Use PostCSS 5.0 API.

# 1.2.0 2015-07-06

Change: Use PostCSS 4.1 plugin API.

# 1.1.0 2014-12-15

Add: `( 300px <= width <= 900px)` or `( 900px >= width >= 300px)` syntax.

# 1.0.0

The first release.
