{"ast": null, "code": "const SERVER_DNS = process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS;\nconst WEBSOCKET_DNS = process.env.REACT_APP_WEBSOCKET_PROTOCOL + '://' + process.env.REACT_APP_DNS;\nconst constants = {\n  BASE_URL: SERVER_DNS + '/apps',\n  WEBSOCKET_URL: WEBSOCKET_DNS + '/apps',\n  API_KEY: process.env.REACT_APP_API_KEY,\n  APP_NAME: 'NeuralAgent',\n  NEURALAGENT_LINK: 'https://www.getneuralagent.com',\n  GENERAL_ERROR: 'Something wrong happened, please try again.',\n  status: {\n    INTERNAL_SERVER_ERROR: 500,\n    UNAUTHORIZED: 401,\n    FORBIDDEN: 403,\n    BAD_REQUEST: 400\n  }\n};\nexport default constants;", "map": {"version": 3, "names": ["SERVER_DNS", "process", "env", "REACT_APP_PROTOCOL", "REACT_APP_DNS", "WEBSOCKET_DNS", "REACT_APP_WEBSOCKET_PROTOCOL", "constants", "BASE_URL", "WEBSOCKET_URL", "API_KEY", "REACT_APP_API_KEY", "APP_NAME", "NEURALAGENT_LINK", "GENERAL_ERROR", "status", "INTERNAL_SERVER_ERROR", "UNAUTHORIZED", "FORBIDDEN", "BAD_REQUEST"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/utils/constants.js"], "sourcesContent": ["const SERVER_DNS = process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS;\nconst WEBSOCKET_DNS = process.env.REACT_APP_WEBSOCKET_PROTOCOL + '://' + process.env.REACT_APP_DNS;\n\nconst constants = {\n  BASE_URL: SERVER_DNS + '/apps',\n  WEBSOCKET_URL: WEBSOCKET_DNS + '/apps',\n  API_KEY: process.env.REACT_APP_API_KEY,\n  APP_NAME: 'NeuralAgent',\n  NEURALAGENT_LINK: 'https://www.getneuralagent.com',\n  GENERAL_ERROR: 'Something wrong happened, please try again.',\n  status: {\n    INTERNAL_SERVER_ERROR: 500,\n    UNAUTHORIZED: 401,\n    FORBIDDEN: 403,\n    BAD_REQUEST: 400\n  }\n};\n\nexport default constants;"], "mappings": "AAAA,MAAMA,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,GAAG,KAAK,GAAGF,OAAO,CAACC,GAAG,CAACE,aAAa;AACrF,MAAMC,aAAa,GAAGJ,OAAO,CAACC,GAAG,CAACI,4BAA4B,GAAG,KAAK,GAAGL,OAAO,CAACC,GAAG,CAACE,aAAa;AAElG,MAAMG,SAAS,GAAG;EAChBC,QAAQ,EAAER,UAAU,GAAG,OAAO;EAC9BS,aAAa,EAAEJ,aAAa,GAAG,OAAO;EACtCK,OAAO,EAAET,OAAO,CAACC,GAAG,CAACS,iBAAiB;EACtCC,QAAQ,EAAE,aAAa;EACvBC,gBAAgB,EAAE,gCAAgC;EAClDC,aAAa,EAAE,6CAA6C;EAC5DC,MAAM,EAAE;IACNC,qBAAqB,EAAE,GAAG;IAC1BC,YAAY,EAAE,GAAG;IACjBC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE;EACf;AACF,CAAC;AAED,eAAeZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}