import{j as m}from"./jsx-runtime-QvZ8i92b.js";import{c as f,a as o}from"./animation-CmKoZPRv.js";import"./index-uubelm5h.js";const g=f("SquareLoader",`25% {transform: rotateX(180deg) rotateY(0)}
  50% {transform: rotateX(180deg) rotateY(180deg)} 
  75% {transform: rotateX(0) rotateY(180deg)} 
  100% {transform: rotateX(0) rotateY(0)}`,"square");function r({loading:a=!0,color:d="#000000",speedMultiplier:u=1,cssOverride:l={},size:t=50,...p}){const c={backgroundColor:d,width:o(t),height:o(t),display:"inline-block",animation:`${g} ${3/u}s 0s infinite cubic-bezier(0.09, 0.57, 0.49, 0.9)`,animationFillMode:"both",...l};return a?m.jsx("span",{style:c,...p}):null}try{r.displayName="SquareLoader",r.__docgenInfo={description:"",displayName:"SquareLoader",props:{size:{defaultValue:{value:"50"},description:"",name:"size",required:!1,type:{name:"LengthType"}},color:{defaultValue:{value:"#000000"},description:"",name:"color",required:!1,type:{name:"string"}},loading:{defaultValue:{value:"true"},description:"",name:"loading",required:!1,type:{name:"boolean"}},cssOverride:{defaultValue:{value:"{}"},description:"",name:"cssOverride",required:!1,type:{name:"CSSProperties"}},speedMultiplier:{defaultValue:{value:"1"},description:"",name:"speedMultiplier",required:!1,type:{name:"number"}}}}}catch{}const _={component:r,argTypes:{size:{description:"Can be number or string. When number, unit is assumed as px. When string, a unit is expected to be passed in",control:{type:"number"}}}},e={};var s,n,i;e.parameters={...e.parameters,docs:{...(s=e.parameters)==null?void 0:s.docs,source:{originalSource:"{}",...(i=(n=e.parameters)==null?void 0:n.docs)==null?void 0:i.source}}};const h=["Primary"];export{e as Primary,h as __namedExportsOrder,_ as default};
