{"name": "jsx-ast-utils", "version": "3.3.5", "description": "AST utility module for statically analyzing JSX", "main": "lib/index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run build", "prebuild": "<PERSON><PERSON><PERSON> lib", "build": "babel src --out-dir lib", "prepublishOnly": "safe-publish-latest && npm test", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "npm run build", "lint": "eslint .", "pretest": "npm run lint", "test": "npm run tests-only --", "posttest": "aud --production", "tests-only": "jest --coverage", "test:watch": "npm run tests-only -- --watch", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/eslint-parser": "^7.22.9", "@babel/parser": "^7.22.7", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-jest": "^20.0.3", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-replace-object-assign": "^1.0.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "babylon": "^6.18.0", "eslint": "^8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "flow-parser": "^0.126.1", "in-publish": "^2.0.1", "jest": "^20.0.4", "jest-cli": "^20.0.4", "npmignore": "^0.3.0", "object.entries": "^1.1.6", "object.fromentries": "^2.0.6", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0"}, "engines": {"node": ">=4.0"}, "keywords": ["jsx", "ast", "lint", "eslint"], "author": "<PERSON>", "repository": {"type": "git", "url": "https://github.com/jsx-eslint/jsx-ast-utils"}, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "3.3.4"}, "publishConfig": {"ignore": [".github/workflows", "jest.config.js"]}}