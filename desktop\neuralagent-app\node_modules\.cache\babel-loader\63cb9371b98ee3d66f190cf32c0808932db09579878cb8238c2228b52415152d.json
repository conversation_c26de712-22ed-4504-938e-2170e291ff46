{"ast": null, "code": "import styled from 'styled-components';\nexport const Button = styled.button`\n  border-radius: ${props => props.borderRadius ? '' + props.borderRadius + 'px' : '0px'};\n  background: ${props => props.color && !props.outlined ? props.color : props.outlined ? 'transparent' : 'var(--primary-color)'};\n  padding: ${props => props.padding ? props.padding : '0px'};\n  color: ${props => props.outlined && props.color ? props.color : props.dark ? '#fff' : '#000'};\n  text-decoration: none;\n  border: none;\n  width: ${props => props.block ? '100%' : 'auto'};\n  outline: ${props => props.outlined ? props.color ? props.color + ' 2px solid' : '#000 2px solid' : 'none'};\n  box-shadow: ${props => props.elevated ? '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)' : 'none'};\n  pointer-events: ${props => props.disabled ? 'none' : 'auto'};\n  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};\n  font-family: inherit;\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '500'};\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n  opacity: ${props => props.disabled ? '0.5' : '1.0'};\n\n  &:hover {\n    opacity: ${props => props.disabled ? '0.5' : '0.8'};\n  }\n`;\nexport const BtnIcon = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  padding-right: ${props => props.left ? '10px' : '0px'};\n  padding-left: ${props => props.right ? '10px' : '0px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nexport const IconButton = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  cursor: pointer;\n  border-radius: 50%;\n  pointer-events: ${props => props.disabled ? 'none' : 'auto'};\n  opacity: ${props => props.disabled ? '0.5' : '1.0'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.05);\n  }\n`;\nexport const AvatarButton = styled.div`\n  width: ${props => props.size ? props.size : '50px'};\n  height: ${props => props.size ? props.size : '50px'};\n  background: ${props => props.color ? props.color : 'var(--primary-color)'};\n  box-shadow: ${props => props.raised ? '0 4pt 8pt rgb(0 0 0 / 20%)' : 'none'};\n  cursor: pointer;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n\n  &:hover {\n    opacity: ${props => props.disabled ? '0.5' : '0.8'};\n  }\n`;\nexport const AvatarBtnIcon = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nexport const AvatarBtnText = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  padding: ${props => props.padding ? props.padding : '0px 5px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '700'};\n  color: ${props => props.color ? props.color : '#000'};\n`;", "map": {"version": 3, "names": ["styled", "<PERSON><PERSON>", "button", "props", "borderRadius", "color", "outlined", "padding", "dark", "block", "elevated", "disabled", "fontSize", "fontWeight", "BtnIcon", "div", "iconSize", "left", "right", "IconButton", "AvatarButton", "size", "raised", "AvatarBtnIcon", "AvatarBtnText"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/Button.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Button = styled.button`\n  border-radius: ${props => (props.borderRadius) ? '' + props.borderRadius + 'px' : '0px'};\n  background: ${props => (props.color && !props.outlined) ? (props.color) : (props.outlined ? 'transparent' : 'var(--primary-color)')};\n  padding: ${props => props.padding ? props.padding : '0px'};\n  color: ${props => (props.outlined && props.color) ? props.color : (props.dark) ? '#fff' : '#000'};\n  text-decoration: none;\n  border: none;\n  width: ${props => props.block ? '100%' : 'auto'};\n  outline: ${props => (props.outlined) ? (props.color ? (props.color + ' 2px solid') : ('#000 2px solid')) : 'none'};\n  box-shadow: ${props => (props.elevated ? '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)' : 'none')};\n  pointer-events: ${props => props.disabled ? 'none' : 'auto'};\n  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};\n  font-family: inherit;\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '500'};\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n  opacity: ${props => props.disabled ? '0.5' : '1.0'};\n\n  &:hover {\n    opacity: ${props => props.disabled ? '0.5' : '0.8'};\n  }\n`\n\nexport const BtnIcon = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  padding-right: ${props => props.left ? '10px' : '0px'};\n  padding-left: ${props => props.right ? '10px' : '0px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`\n\nexport const IconButton = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  cursor: pointer;\n  border-radius: 50%;\n  pointer-events: ${props => props.disabled ? 'none' : 'auto'};\n  opacity: ${props => props.disabled ? '0.5' : '1.0'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.05);\n  }\n`\n\nexport const AvatarButton = styled.div`\n  width: ${props => props.size ? props.size : '50px'};\n  height: ${props => props.size ? props.size : '50px'};\n  background: ${props => props.color ? props.color : 'var(--primary-color)'};\n  box-shadow: ${props => props.raised ? '0 4pt 8pt rgb(0 0 0 / 20%)' : 'none'};\n  cursor: pointer;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n\n  &:hover {\n    opacity: ${props => props.disabled ? '0.5' : '0.8'};\n  }\n`\n\nexport const AvatarBtnIcon = styled.div`\n  font-size: ${props => props.iconSize ? props.iconSize : '23px'};\n  height: ${props => props.iconSize ? props.iconSize : '23px'};\n  color: ${props => props.color ? props.color : '#000'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`\n\nexport const AvatarBtnText = styled.div`\n  font-size: ${props => props.fontSize ? props.fontSize : '16px'};\n  padding: ${props => props.padding ? props.padding : '0px 5px'};\n  font-weight: ${props => props.fontWeight ? props.fontWeight : '700'};\n  color: ${props => props.color ? props.color : '#000'};\n`\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AAEtC,OAAO,MAAMC,MAAM,GAAGD,MAAM,CAACE,MAAM;AACnC,mBAAmBC,KAAK,IAAKA,KAAK,CAACC,YAAY,GAAI,EAAE,GAAGD,KAAK,CAACC,YAAY,GAAG,IAAI,GAAG,KAAK;AACzF,gBAAgBD,KAAK,IAAKA,KAAK,CAACE,KAAK,IAAI,CAACF,KAAK,CAACG,QAAQ,GAAKH,KAAK,CAACE,KAAK,GAAKF,KAAK,CAACG,QAAQ,GAAG,aAAa,GAAG,sBAAuB;AACrI,aAAaH,KAAK,IAAIA,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACI,OAAO,GAAG,KAAK;AAC3D,WAAWJ,KAAK,IAAKA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACK,IAAI,GAAI,MAAM,GAAG,MAAM;AAClG;AACA;AACA,WAAWL,KAAK,IAAIA,KAAK,CAACM,KAAK,GAAG,MAAM,GAAG,MAAM;AACjD,aAAaN,KAAK,IAAKA,KAAK,CAACG,QAAQ,GAAKH,KAAK,CAACE,KAAK,GAAIF,KAAK,CAACE,KAAK,GAAG,YAAY,GAAK,gBAAiB,GAAI,MAAM;AACnH,gBAAgBF,KAAK,IAAKA,KAAK,CAACO,QAAQ,GAAG,0DAA0D,GAAG,MAAO;AAC/G,oBAAoBP,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,MAAM,GAAG,MAAM;AAC7D,YAAYR,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,aAAa,GAAG,SAAS;AAC/D;AACA,eAAeR,KAAK,IAAIA,KAAK,CAACS,QAAQ,GAAGT,KAAK,CAACS,QAAQ,GAAG,MAAM;AAChE,iBAAiBT,KAAK,IAAIA,KAAK,CAACU,UAAU,GAAGV,KAAK,CAACU,UAAU,GAAG,KAAK;AACrE;AACA;AACA;AACA;AACA;AACA,aAAaV,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,KAAK,GAAG,KAAK;AACpD;AACA;AACA,eAAeR,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtD;AACA,CAAC;AAED,OAAO,MAAMG,OAAO,GAAGd,MAAM,CAACe,GAAG;AACjC,eAAeZ,KAAK,IAAIA,KAAK,CAACa,QAAQ,GAAGb,KAAK,CAACa,QAAQ,GAAG,MAAM;AAChE,YAAYb,KAAK,IAAIA,KAAK,CAACa,QAAQ,GAAGb,KAAK,CAACa,QAAQ,GAAG,MAAM;AAC7D,WAAWb,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,MAAM;AACtD,mBAAmBF,KAAK,IAAIA,KAAK,CAACc,IAAI,GAAG,MAAM,GAAG,KAAK;AACvD,kBAAkBd,KAAK,IAAIA,KAAK,CAACe,KAAK,GAAG,MAAM,GAAG,KAAK;AACvD;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGnB,MAAM,CAACe,GAAG;AACpC,eAAeZ,KAAK,IAAIA,KAAK,CAACa,QAAQ,GAAGb,KAAK,CAACa,QAAQ,GAAG,MAAM;AAChE,YAAYb,KAAK,IAAIA,KAAK,CAACa,QAAQ,GAAGb,KAAK,CAACa,QAAQ,GAAG,MAAM;AAC7D,WAAWb,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,MAAM;AACtD;AACA;AACA,oBAAoBF,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,MAAM,GAAG,MAAM;AAC7D,aAAaR,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,KAAK,GAAG,KAAK;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMS,YAAY,GAAGpB,MAAM,CAACe,GAAG;AACtC,WAAWZ,KAAK,IAAIA,KAAK,CAACkB,IAAI,GAAGlB,KAAK,CAACkB,IAAI,GAAG,MAAM;AACpD,YAAYlB,KAAK,IAAIA,KAAK,CAACkB,IAAI,GAAGlB,KAAK,CAACkB,IAAI,GAAG,MAAM;AACrD,gBAAgBlB,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,sBAAsB;AAC3E,gBAAgBF,KAAK,IAAIA,KAAK,CAACmB,MAAM,GAAG,4BAA4B,GAAG,MAAM;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAenB,KAAK,IAAIA,KAAK,CAACQ,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtD;AACA,CAAC;AAED,OAAO,MAAMY,aAAa,GAAGvB,MAAM,CAACe,GAAG;AACvC,eAAeZ,KAAK,IAAIA,KAAK,CAACa,QAAQ,GAAGb,KAAK,CAACa,QAAQ,GAAG,MAAM;AAChE,YAAYb,KAAK,IAAIA,KAAK,CAACa,QAAQ,GAAGb,KAAK,CAACa,QAAQ,GAAG,MAAM;AAC7D,WAAWb,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,MAAM;AACtD;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMmB,aAAa,GAAGxB,MAAM,CAACe,GAAG;AACvC,eAAeZ,KAAK,IAAIA,KAAK,CAACS,QAAQ,GAAGT,KAAK,CAACS,QAAQ,GAAG,MAAM;AAChE,aAAaT,KAAK,IAAIA,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACI,OAAO,GAAG,SAAS;AAC/D,iBAAiBJ,KAAK,IAAIA,KAAK,CAACU,UAAU,GAAGV,KAAK,CAACU,UAAU,GAAG,KAAK;AACrE,WAAWV,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG,MAAM;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}