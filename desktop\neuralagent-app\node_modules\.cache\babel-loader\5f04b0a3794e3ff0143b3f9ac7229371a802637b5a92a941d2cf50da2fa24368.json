{"ast": null, "code": "var _jsxFileName = \"F:\\\\zu<PERSON><PERSON><PERSON>jian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\views\\\\Overlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport neuralagent_logo_ic_only_white from '../assets/neuralagent_logo_ic_only_white.png';\nimport { AvatarButton, IconButton } from '../components/Elements/Button';\nimport { useSelector } from 'react-redux';\nimport axios from '../utils/axios';\nimport { FaStopCircle } from 'react-icons/fa';\nimport constants from '../utils/constants';\nimport { MdOutlineSchedule } from 'react-icons/md';\nimport { GiBrain } from 'react-icons/gi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  background: transparent;\n  padding: 0px 8px;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  height: 100vh;\n  width: 100%;\n  transition: height 0.3s ease;\n`;\n_c = Container;\nconst Input = styled.input`\n  flex: 1;\n  border: none;\n  background: transparent;\n  color: white;\n  font-size: 14px;\n  outline: none;\n`;\n_c2 = Input;\nconst spin = keyframes`\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n`;\nconst Spinner = styled.div`\n  margin-left: 8px;\n  width: 21px;\n  height: 21px;\n  border: 2px solid white;\n  border-top: 2px solid transparent;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n`;\n_c3 = Spinner;\nconst SuggestionsPanel = styled.div`\n  margin-top: 5px;\n  background-color: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  padding: 10px;\n  flex: 1;\n  overflow-y: auto;\n`;\n_c4 = SuggestionsPanel;\nconst SuggestionItem = styled.div`\n  padding: 8px;\n  margin-bottom: 6px;\n  background: rgba(255,255,255,0.07);\n  border-radius: 6px;\n  color: white;\n  font-size: 13px;\n  cursor: pointer;\n  transition: background 0.2s;\n\n  &:hover {\n    background: rgba(255,255,255,0.15);\n  }\n`;\n_c5 = SuggestionItem;\nconst shimmer = keyframes`\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n`;\nconst SkeletonItem = styled.div`\n  height: 36px;\n  margin-bottom: 6px;\n  border-radius: 6px;\n  background: linear-gradient(\n    90deg,\n    rgba(255, 255, 255, 0.07) 25%,\n    rgba(255, 255, 255, 0.15) 50%,\n    rgba(255, 255, 255, 0.07) 75%\n  );\n  background-size: 200px 100%;\n  animation: ${shimmer} 1.2s infinite;\n`;\n_c6 = SkeletonItem;\nconst ToggleContainer = styled.div`\n  display: flex;\n  align-items: center;\n`;\n_c7 = ToggleContainer;\nconst ModeToggle = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  background-color: ${({\n  active\n}) => active ? 'rgba(255,255,255,0.1)' : 'transparent'};\n  color: #fff;\n  border: thin solid rgba(255,255,255,0.2);\n  border-radius: 999px;\n  padding: 4px 10px;\n  font-size: 11.5px;\n  transition: background-color 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255,255,255,0.1);\n  }\n\n  svg {\n    font-size: 15px;\n  }\n`;\n_c8 = ModeToggle;\nexport default function Overlay() {\n  _s();\n  const [expanded, setExpanded] = useState(false);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [messageText, setMessageText] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [runningThreadId, setRunningThreadId] = useState(null);\n  const [suggestions, setSuggestions] = useState([]);\n  const [backgroundMode, setBackgroundMode] = useState(false);\n  const [thinkingMode, setThinkingMode] = useState(false);\n  const accessToken = useSelector(state => state.accessToken);\n  const executeTask = () => {\n    if (loading) {\n      return;\n    }\n    createThread();\n  };\n  const executeSuggestion = prompt => {\n    if (loading) return;\n    window.electronAPI.expandOverlay(false);\n    setShowSuggestions(false);\n    createThread(prompt);\n  };\n  const toggleOverlay = async () => {\n    if (!expanded) {\n      if (runningThreadId === null) {\n        window.electronAPI.expandOverlay(true);\n        setExpanded(true);\n        setShowSuggestions(true);\n        if (suggestions.length === 0) {\n          getSuggestions();\n        }\n      } else {\n        window.electronAPI.expandOverlay(false);\n        setExpanded(true);\n      }\n    } else {\n      window.electronAPI.minimizeOverlay();\n      setExpanded(false);\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  };\n  const getSuggestions = async () => {\n    const suggestedTasks = await window.electronAPI.getSuggestions(process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS);\n    setSuggestions(suggestedTasks.suggestions);\n  };\n  const cancelRunningTask = tid => {\n    setLoading(true);\n    axios.post(`/threads/${tid}/cancel_task`, {}, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(response => {\n      setLoading(false);\n      window.electronAPI.stopAIAgent();\n      setRunningThreadId(null);\n    }).catch(error => {\n      var _error$response;\n      setLoading(false);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n  const createThread = async (prompt = null) => {\n    if (messageText.length === 0 && prompt === null) {\n      return;\n    }\n    const data = {\n      task: prompt !== null ? prompt : messageText,\n      background_mode: backgroundMode,\n      extended_thinking_mode: thinkingMode\n    };\n    setMessageText('');\n    setLoading(true);\n    axios.post('/threads', data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken\n      }\n    }).then(async response => {\n      setLoading(false);\n      if (response.data.type === 'desktop_task') {\n        if (!backgroundMode && response.data.is_background_mode_requested) {\n          const ready = await window.electronAPI.isBackgroundModeReady();\n          if (!ready) {\n            cancelRunningTask();\n            return;\n          }\n        }\n        setBackgroundMode(backgroundMode || response.data.is_background_mode_requested);\n        setThinkingMode(thinkingMode || response.data.is_extended_thinking_mode_requested);\n        window.electronAPI.setLastThinkingModeValue((thinkingMode || response.data.is_extended_thinking_mode_requested).toString());\n        window.electronAPI.launchAIAgent(process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS, response.data.thread_id, backgroundMode || response.data.is_background_mode_requested);\n        setRunningThreadId(response.data.thread_id);\n      }\n    }).catch(error => {\n      var _error$response2;\n      setLoading(false);\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n  const onBGModeToggleChange = async value => {\n    if (value) {\n      const ready = await window.electronAPI.isBackgroundModeReady();\n      if (!ready) {\n        window.electronAPI.startBackgroundSetup();\n        return;\n      }\n    }\n    setBackgroundMode(value);\n  };\n  useEffect(() => {\n    var _window$electronAPI;\n    if ((_window$electronAPI = window.electronAPI) !== null && _window$electronAPI !== void 0 && _window$electronAPI.onAIAgentLaunch) {\n      window.electronAPI.onAIAgentLaunch(threadId => {\n        window.electronAPI.expandOverlay(false);\n        setExpanded(true);\n        setRunningThreadId(threadId);\n        setShowSuggestions(false);\n      });\n    }\n  }, []);\n  useEffect(() => {\n    var _window$electronAPI2;\n    if ((_window$electronAPI2 = window.electronAPI) !== null && _window$electronAPI2 !== void 0 && _window$electronAPI2.onAIAgentExit) {\n      window.electronAPI.onAIAgentExit(() => {\n        setRunningThreadId(null);\n        window.electronAPI.expandOverlay(true);\n        setShowSuggestions(true);\n        setSuggestions([]);\n        getSuggestions();\n      });\n    }\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastBackgroundModeValue = await window.electronAPI.getLastBackgroundModeValue();\n      setBackgroundMode(lastBackgroundModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastThinkingModeValue = await window.electronAPI.getLastThinkingModeValue();\n      setThinkingMode(lastThinkingModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        width: '100%',\n        height: '60px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AvatarButton, {\n        onClick: () => toggleOverlay(),\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: neuralagent_logo_ic_only_white,\n          alt: \"NeuralAgent\",\n          height: 30,\n          style: {\n            userSelect: 'none',\n            pointerEvents: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), expanded && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          value: messageText,\n          onChange: e => setMessageText(e.target.value),\n          placeholder: \"Ask NeuralAgent...\",\n          onKeyDown: e => e.key === 'Enter' && executeTask()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), !loading && runningThreadId === null && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '5px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ToggleContainer, {\n            children: /*#__PURE__*/_jsxDEV(ModeToggle, {\n              active: backgroundMode,\n              onClick: () => onBGModeToggleChange(!backgroundMode),\n              children: /*#__PURE__*/_jsxDEV(MdOutlineSchedule, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '5px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ToggleContainer, {\n            children: /*#__PURE__*/_jsxDEV(ModeToggle, {\n              active: thinkingMode,\n              onClick: () => setThinkingMode(!thinkingMode),\n              children: /*#__PURE__*/_jsxDEV(GiBrain, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), (loading || runningThreadId !== null) && /*#__PURE__*/_jsxDEV(Spinner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 55\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '5px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), runningThreadId !== null && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            iconSize: \"21px\",\n            color: \"white\",\n            onClick: () => cancelRunningTask(runningThreadId),\n            disabled: loading,\n            children: /*#__PURE__*/_jsxDEV(FaStopCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this)\n        }, void 0, false)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), expanded && showSuggestions && /*#__PURE__*/_jsxDEV(SuggestionsPanel, {\n      children: suggestions.length === 0 ? Array.from({\n        length: 7\n      }).map((_, idx) => /*#__PURE__*/_jsxDEV(SkeletonItem, {}, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 17\n      }, this)) : suggestions.map((s, idx) => /*#__PURE__*/_jsxDEV(SuggestionItem, {\n        onClick: () => executeSuggestion(s.ai_prompt),\n        children: s.title\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 17\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 285,\n    columnNumber: 5\n  }, this);\n}\n_s(Overlay, \"xvwhVQs87AV/9zmNCDO8OAO5Ifw=\", false, function () {\n  return [useSelector];\n});\n_c9 = Overlay;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Input\");\n$RefreshReg$(_c3, \"Spinner\");\n$RefreshReg$(_c4, \"SuggestionsPanel\");\n$RefreshReg$(_c5, \"SuggestionItem\");\n$RefreshReg$(_c6, \"SkeletonItem\");\n$RefreshReg$(_c7, \"ToggleContainer\");\n$RefreshReg$(_c8, \"ModeToggle\");\n$RefreshReg$(_c9, \"Overlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "keyframes", "neuralagent_logo_ic_only_white", "AvatarButton", "IconButton", "useSelector", "axios", "FaStopCircle", "constants", "MdOutlineSchedule", "GiBrain", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Container", "div", "_c", "Input", "input", "_c2", "spin", "Spinner", "_c3", "SuggestionsPanel", "_c4", "SuggestionItem", "_c5", "shimmer", "SkeletonItem", "_c6", "ToggleContainer", "_c7", "ModeToggle", "button", "active", "_c8", "Overlay", "_s", "expanded", "setExpanded", "showSuggestions", "setShowSuggestions", "messageText", "setMessageText", "loading", "setLoading", "runningThreadId", "setRunningThreadId", "suggestions", "setSuggestions", "backgroundMode", "setBackgroundMode", "thinkingMode", "setThinkingMode", "accessToken", "state", "executeTask", "createThread", "executeSuggestion", "prompt", "window", "electronAPI", "expandOverlay", "to<PERSON><PERSON><PERSON><PERSON>", "length", "getSuggestions", "minimizeOverlay", "suggestedTasks", "process", "env", "REACT_APP_PROTOCOL", "REACT_APP_DNS", "cancelRunningTask", "tid", "post", "headers", "then", "response", "stopAIAgent", "catch", "error", "_error$response", "status", "UNAUTHORIZED", "location", "reload", "data", "task", "background_mode", "extended_thinking_mode", "type", "is_background_mode_requested", "ready", "isBackgroundModeReady", "is_extended_thinking_mode_requested", "setLastThinkingModeValue", "toString", "launchAIAgent", "thread_id", "_error$response2", "onBGModeToggleChange", "value", "startBackgroundSetup", "_window$electronAPI", "onAIAgentLaunch", "threadId", "_window$electronAPI2", "onAIAgentExit", "asyncTask", "lastBackgroundModeValue", "getLastBackgroundModeValue", "lastThinkingModeValue", "getLastThinkingModeValue", "children", "style", "display", "alignItems", "width", "height", "onClick", "src", "alt", "userSelect", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "placeholder", "onKeyDown", "key", "iconSize", "color", "disabled", "Array", "from", "map", "_", "idx", "s", "ai_prompt", "title", "_c9", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/views/Overlay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport neuralagent_logo_ic_only_white from '../assets/neuralagent_logo_ic_only_white.png'\nimport { AvatarButton, IconButton } from '../components/Elements/Button';\nimport { useSelector } from 'react-redux';\nimport axios from '../utils/axios';\nimport { FaStopCircle } from 'react-icons/fa';\nimport constants from '../utils/constants';\nimport { MdOutlineSchedule } from 'react-icons/md';\nimport { GiBrain } from 'react-icons/gi';\n\nconst Container = styled.div`\n  background: transparent;\n  padding: 0px 8px;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  height: 100vh;\n  width: 100%;\n  transition: height 0.3s ease;\n`;\n\nconst Input = styled.input`\n  flex: 1;\n  border: none;\n  background: transparent;\n  color: white;\n  font-size: 14px;\n  outline: none;\n`;\n\nconst spin = keyframes`\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n`;\n\nconst Spinner = styled.div`\n  margin-left: 8px;\n  width: 21px;\n  height: 21px;\n  border: 2px solid white;\n  border-top: 2px solid transparent;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n`;\n\nconst SuggestionsPanel = styled.div`\n  margin-top: 5px;\n  background-color: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  padding: 10px;\n  flex: 1;\n  overflow-y: auto;\n`;\n\nconst SuggestionItem = styled.div`\n  padding: 8px;\n  margin-bottom: 6px;\n  background: rgba(255,255,255,0.07);\n  border-radius: 6px;\n  color: white;\n  font-size: 13px;\n  cursor: pointer;\n  transition: background 0.2s;\n\n  &:hover {\n    background: rgba(255,255,255,0.15);\n  }\n`;\n\nconst shimmer = keyframes`\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n`;\n\nconst SkeletonItem = styled.div`\n  height: 36px;\n  margin-bottom: 6px;\n  border-radius: 6px;\n  background: linear-gradient(\n    90deg,\n    rgba(255, 255, 255, 0.07) 25%,\n    rgba(255, 255, 255, 0.15) 50%,\n    rgba(255, 255, 255, 0.07) 75%\n  );\n  background-size: 200px 100%;\n  animation: ${shimmer} 1.2s infinite;\n`;\n\nconst ToggleContainer = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\nconst ModeToggle = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  background-color: ${({ active }) => (active ? 'rgba(255,255,255,0.1)' : 'transparent')};\n  color: #fff;\n  border: thin solid rgba(255,255,255,0.2);\n  border-radius: 999px;\n  padding: 4px 10px;\n  font-size: 11.5px;\n  transition: background-color 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255,255,255,0.1);\n  }\n\n  svg {\n    font-size: 15px;\n  }\n`;\n\nexport default function Overlay() {\n  const [expanded, setExpanded] = useState(false);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [messageText, setMessageText] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [runningThreadId, setRunningThreadId] = useState(null);\n  const [suggestions, setSuggestions] = useState([]);\n  const [backgroundMode, setBackgroundMode] = useState(false);\n  const [thinkingMode, setThinkingMode] = useState(false);\n\n  const accessToken = useSelector(state => state.accessToken);\n\n  const executeTask = () => {\n    if (loading) {\n      return;\n    }\n    createThread();\n  };\n\n  const executeSuggestion = (prompt) => {\n    if (loading) return;\n\n    window.electronAPI.expandOverlay(false);\n    setShowSuggestions(false);\n    createThread(prompt);\n  };\n\n  const toggleOverlay = async () => {\n    if (!expanded) {\n      if (runningThreadId === null) {\n        window.electronAPI.expandOverlay(true);\n        setExpanded(true);\n        setShowSuggestions(true);\n        if (suggestions.length === 0) {\n          getSuggestions();\n        }\n      } else {\n        window.electronAPI.expandOverlay(false);\n        setExpanded(true);\n      }\n    } else {\n      window.electronAPI.minimizeOverlay();\n      setExpanded(false);\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  };\n\n  const getSuggestions = async () => {\n    const suggestedTasks = await window.electronAPI.getSuggestions(\n      process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS,\n    );\n    setSuggestions(suggestedTasks.suggestions);\n  };\n\n  const cancelRunningTask = (tid) => {\n    setLoading(true);\n    axios.post(`/threads/${tid}/cancel_task`, {}, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then((response) => {\n      setLoading(false);\n      window.electronAPI.stopAIAgent();\n      setRunningThreadId(null);\n    }).catch((error) => {\n      setLoading(false);\n      if (error.response?.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n\n  const createThread = async (prompt = null) => {\n    if (messageText.length === 0 && prompt === null) {\n      return;\n    }\n\n    const data = {task: prompt !== null ? prompt : messageText, background_mode: backgroundMode, extended_thinking_mode: thinkingMode};\n    setMessageText('');\n    setLoading(true);\n    axios.post('/threads', data, {\n      headers: {\n        'Authorization': 'Bearer ' + accessToken,\n      }\n    }).then(async (response) => {\n      setLoading(false);\n      if (response.data.type === 'desktop_task') {\n        if (!backgroundMode && response.data.is_background_mode_requested) {\n          const ready = await window.electronAPI.isBackgroundModeReady();\n          if (!ready) {\n            cancelRunningTask();\n            return;\n          }\n        }\n        setBackgroundMode(backgroundMode || response.data.is_background_mode_requested);\n        setThinkingMode(thinkingMode || response.data.is_extended_thinking_mode_requested);\n        window.electronAPI.setLastThinkingModeValue((thinkingMode || response.data.is_extended_thinking_mode_requested).toString());\n        window.electronAPI.launchAIAgent(\n          process.env.REACT_APP_PROTOCOL + '://' + process.env.REACT_APP_DNS,\n          response.data.thread_id,\n          backgroundMode || response.data.is_background_mode_requested\n        );\n        setRunningThreadId(response.data.thread_id);\n      }\n    }).catch((error) => {\n      setLoading(false);\n      if (error.response?.status === constants.status.UNAUTHORIZED) {\n        window.location.reload();\n      }\n    });\n  };\n\n  const onBGModeToggleChange = async (value) => {\n    if (value) {\n      const ready = await window.electronAPI.isBackgroundModeReady();\n      if (!ready) {\n        window.electronAPI.startBackgroundSetup();\n        return;\n      }\n    }\n    setBackgroundMode(value);\n  };\n\n  useEffect(() => {\n    if (window.electronAPI?.onAIAgentLaunch) {\n      window.electronAPI.onAIAgentLaunch((threadId) => {\n        window.electronAPI.expandOverlay(false);\n        setExpanded(true);\n        setRunningThreadId(threadId);\n        setShowSuggestions(false);\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    if (window.electronAPI?.onAIAgentExit) {\n      window.electronAPI.onAIAgentExit(() => {\n        setRunningThreadId(null);\n        window.electronAPI.expandOverlay(true);\n        setShowSuggestions(true);\n        setSuggestions([]);\n        getSuggestions();\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastBackgroundModeValue = await window.electronAPI.getLastBackgroundModeValue();\n      setBackgroundMode(lastBackgroundModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n\n  useEffect(() => {\n    const asyncTask = async () => {\n      const lastThinkingModeValue = await window.electronAPI.getLastThinkingModeValue();\n      setThinkingMode(lastThinkingModeValue === 'true');\n    };\n    asyncTask();\n  }, []);\n\n  return (\n    <Container>\n      <div style={{display: 'flex', alignItems: 'center', width: '100%', height: '60px'}}>\n        <AvatarButton onClick={() => toggleOverlay()}>\n          <img\n            src={neuralagent_logo_ic_only_white}\n            alt='NeuralAgent'\n            height={30}\n            style={{userSelect: 'none', pointerEvents: 'none'}}\n          />\n        </AvatarButton>\n        {expanded && (\n          <>\n            <div style={{width: '10px'}} />\n            <Input\n              value={messageText}\n              onChange={(e) => setMessageText(e.target.value)}\n              placeholder=\"Ask NeuralAgent...\"\n              onKeyDown={(e) => e.key === 'Enter' && executeTask()}\n            />\n            {!loading && runningThreadId === null && (\n              <> \n                <div style={{width: '5px'}} />\n                <ToggleContainer>\n                  <ModeToggle\n                    active={backgroundMode}\n                    onClick={() => onBGModeToggleChange(!backgroundMode)}\n                  >\n                    <MdOutlineSchedule />\n                  </ModeToggle>\n                </ToggleContainer>\n                <div style={{width: '5px'}} />\n                <ToggleContainer>\n                  <ModeToggle\n                    active={thinkingMode}\n                    onClick={() => setThinkingMode(!thinkingMode)}\n                  >\n                    <GiBrain />\n                  </ModeToggle>\n                </ToggleContainer>\n              </>\n            )}\n            {(loading || runningThreadId !== null) && <Spinner />}\n            <div style={{width: '5px'}} />\n            {\n            runningThreadId !== null && <>\n                <IconButton iconSize='21px' color='white' onClick={() => cancelRunningTask(runningThreadId)}\n                  disabled={loading}>\n                  <FaStopCircle />\n                </IconButton>\n              </>\n            }\n          </>\n        )}\n      </div>\n      {expanded && showSuggestions && (\n        <SuggestionsPanel>\n          {suggestions.length === 0\n            ? Array.from({ length: 7 }).map((_, idx) => (\n                <SkeletonItem key={idx} />\n              ))\n            : suggestions.map((s, idx) => (\n                <SuggestionItem\n                  key={idx}\n                  onClick={() => executeSuggestion(s.ai_prompt)}\n                >\n                  {s.title}\n                </SuggestionItem>\n              ))}\n        </SuggestionsPanel>\n      )}\n    </Container>\n  );\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,OAAOC,8BAA8B,MAAM,8CAA8C;AACzF,SAASC,YAAY,EAAEC,UAAU,QAAQ,+BAA+B;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,KAAK,MAAM,gBAAgB;AAClC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,SAAS,GAAGf,MAAM,CAACgB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIF,SAAS;AAWf,MAAMG,KAAK,GAAGlB,MAAM,CAACmB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,KAAK;AASX,MAAMG,IAAI,GAAGpB,SAAS;AACtB;AACA;AACA,CAAC;AAED,MAAMqB,OAAO,GAAGtB,MAAM,CAACgB,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeK,IAAI;AACnB,CAAC;AAACE,GAAA,GARID,OAAO;AAUb,MAAME,gBAAgB,GAAGxB,MAAM,CAACgB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAPID,gBAAgB;AAStB,MAAME,cAAc,GAAG1B,MAAM,CAACgB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAbID,cAAc;AAepB,MAAME,OAAO,GAAG3B,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM4B,YAAY,GAAG7B,MAAM,CAACgB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeY,OAAO;AACtB,CAAC;AAACE,GAAA,GAZID,YAAY;AAclB,MAAME,eAAe,GAAG/B,MAAM,CAACgB,GAAG;AAClC;AACA;AACA,CAAC;AAACgB,GAAA,GAHID,eAAe;AAKrB,MAAME,UAAU,GAAGjC,MAAM,CAACkC,MAAM;AAChC;AACA;AACA;AACA,sBAAsB,CAAC;EAAEC;AAAO,CAAC,KAAMA,MAAM,GAAG,uBAAuB,GAAG,aAAc;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApBIH,UAAU;AAsBhB,eAAe,SAASI,OAAOA,CAAA,EAAG;EAAAC,EAAA;EAChC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMyD,WAAW,GAAGlD,WAAW,CAACmD,KAAK,IAAIA,KAAK,CAACD,WAAW,CAAC;EAE3D,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIZ,OAAO,EAAE;MACX;IACF;IACAa,YAAY,CAAC,CAAC;EAChB,CAAC;EAED,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpC,IAAIf,OAAO,EAAE;IAEbgB,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,KAAK,CAAC;IACvCrB,kBAAkB,CAAC,KAAK,CAAC;IACzBgB,YAAY,CAACE,MAAM,CAAC;EACtB,CAAC;EAED,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACzB,QAAQ,EAAE;MACb,IAAIQ,eAAe,KAAK,IAAI,EAAE;QAC5Bc,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,IAAI,CAAC;QACtCvB,WAAW,CAAC,IAAI,CAAC;QACjBE,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAIO,WAAW,CAACgB,MAAM,KAAK,CAAC,EAAE;UAC5BC,cAAc,CAAC,CAAC;QAClB;MACF,CAAC,MAAM;QACLL,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,KAAK,CAAC;QACvCvB,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,MAAM;MACLqB,MAAM,CAACC,WAAW,CAACK,eAAe,CAAC,CAAC;MACpC3B,WAAW,CAAC,KAAK,CAAC;MAClBU,cAAc,CAAC,EAAE,CAAC;MAClBR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAME,cAAc,GAAG,MAAMP,MAAM,CAACC,WAAW,CAACI,cAAc,CAC5DG,OAAO,CAACC,GAAG,CAACC,kBAAkB,GAAG,KAAK,GAAGF,OAAO,CAACC,GAAG,CAACE,aACvD,CAAC;IACDtB,cAAc,CAACkB,cAAc,CAACnB,WAAW,CAAC;EAC5C,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,GAAG,IAAK;IACjC5B,UAAU,CAAC,IAAI,CAAC;IAChBxC,KAAK,CAACqE,IAAI,CAAC,YAAYD,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE;MAC5CE,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGrB;MAC/B;IACF,CAAC,CAAC,CAACsB,IAAI,CAAEC,QAAQ,IAAK;MACpBhC,UAAU,CAAC,KAAK,CAAC;MACjBe,MAAM,CAACC,WAAW,CAACiB,WAAW,CAAC,CAAC;MAChC/B,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,CAACgC,KAAK,CAAEC,KAAK,IAAK;MAAA,IAAAC,eAAA;MAClBpC,UAAU,CAAC,KAAK,CAAC;MACjB,IAAI,EAAAoC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK3E,SAAS,CAAC2E,MAAM,CAACC,YAAY,EAAE;QAC5DvB,MAAM,CAACwB,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM5B,YAAY,GAAG,MAAAA,CAAOE,MAAM,GAAG,IAAI,KAAK;IAC5C,IAAIjB,WAAW,CAACsB,MAAM,KAAK,CAAC,IAAIL,MAAM,KAAK,IAAI,EAAE;MAC/C;IACF;IAEA,MAAM2B,IAAI,GAAG;MAACC,IAAI,EAAE5B,MAAM,KAAK,IAAI,GAAGA,MAAM,GAAGjB,WAAW;MAAE8C,eAAe,EAAEtC,cAAc;MAAEuC,sBAAsB,EAAErC;IAAY,CAAC;IAClIT,cAAc,CAAC,EAAE,CAAC;IAClBE,UAAU,CAAC,IAAI,CAAC;IAChBxC,KAAK,CAACqE,IAAI,CAAC,UAAU,EAAEY,IAAI,EAAE;MAC3BX,OAAO,EAAE;QACP,eAAe,EAAE,SAAS,GAAGrB;MAC/B;IACF,CAAC,CAAC,CAACsB,IAAI,CAAC,MAAOC,QAAQ,IAAK;MAC1BhC,UAAU,CAAC,KAAK,CAAC;MACjB,IAAIgC,QAAQ,CAACS,IAAI,CAACI,IAAI,KAAK,cAAc,EAAE;QACzC,IAAI,CAACxC,cAAc,IAAI2B,QAAQ,CAACS,IAAI,CAACK,4BAA4B,EAAE;UACjE,MAAMC,KAAK,GAAG,MAAMhC,MAAM,CAACC,WAAW,CAACgC,qBAAqB,CAAC,CAAC;UAC9D,IAAI,CAACD,KAAK,EAAE;YACVpB,iBAAiB,CAAC,CAAC;YACnB;UACF;QACF;QACArB,iBAAiB,CAACD,cAAc,IAAI2B,QAAQ,CAACS,IAAI,CAACK,4BAA4B,CAAC;QAC/EtC,eAAe,CAACD,YAAY,IAAIyB,QAAQ,CAACS,IAAI,CAACQ,mCAAmC,CAAC;QAClFlC,MAAM,CAACC,WAAW,CAACkC,wBAAwB,CAAC,CAAC3C,YAAY,IAAIyB,QAAQ,CAACS,IAAI,CAACQ,mCAAmC,EAAEE,QAAQ,CAAC,CAAC,CAAC;QAC3HpC,MAAM,CAACC,WAAW,CAACoC,aAAa,CAC9B7B,OAAO,CAACC,GAAG,CAACC,kBAAkB,GAAG,KAAK,GAAGF,OAAO,CAACC,GAAG,CAACE,aAAa,EAClEM,QAAQ,CAACS,IAAI,CAACY,SAAS,EACvBhD,cAAc,IAAI2B,QAAQ,CAACS,IAAI,CAACK,4BAClC,CAAC;QACD5C,kBAAkB,CAAC8B,QAAQ,CAACS,IAAI,CAACY,SAAS,CAAC;MAC7C;IACF,CAAC,CAAC,CAACnB,KAAK,CAAEC,KAAK,IAAK;MAAA,IAAAmB,gBAAA;MAClBtD,UAAU,CAAC,KAAK,CAAC;MACjB,IAAI,EAAAsD,gBAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,gBAAA,uBAAdA,gBAAA,CAAgBjB,MAAM,MAAK3E,SAAS,CAAC2E,MAAM,CAACC,YAAY,EAAE;QAC5DvB,MAAM,CAACwB,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,oBAAoB,GAAG,MAAOC,KAAK,IAAK;IAC5C,IAAIA,KAAK,EAAE;MACT,MAAMT,KAAK,GAAG,MAAMhC,MAAM,CAACC,WAAW,CAACgC,qBAAqB,CAAC,CAAC;MAC9D,IAAI,CAACD,KAAK,EAAE;QACVhC,MAAM,CAACC,WAAW,CAACyC,oBAAoB,CAAC,CAAC;QACzC;MACF;IACF;IACAnD,iBAAiB,CAACkD,KAAK,CAAC;EAC1B,CAAC;EAEDvG,SAAS,CAAC,MAAM;IAAA,IAAAyG,mBAAA;IACd,KAAAA,mBAAA,GAAI3C,MAAM,CAACC,WAAW,cAAA0C,mBAAA,eAAlBA,mBAAA,CAAoBC,eAAe,EAAE;MACvC5C,MAAM,CAACC,WAAW,CAAC2C,eAAe,CAAEC,QAAQ,IAAK;QAC/C7C,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,KAAK,CAAC;QACvCvB,WAAW,CAAC,IAAI,CAAC;QACjBQ,kBAAkB,CAAC0D,QAAQ,CAAC;QAC5BhE,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3C,SAAS,CAAC,MAAM;IAAA,IAAA4G,oBAAA;IACd,KAAAA,oBAAA,GAAI9C,MAAM,CAACC,WAAW,cAAA6C,oBAAA,eAAlBA,oBAAA,CAAoBC,aAAa,EAAE;MACrC/C,MAAM,CAACC,WAAW,CAAC8C,aAAa,CAAC,MAAM;QACrC5D,kBAAkB,CAAC,IAAI,CAAC;QACxBa,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,IAAI,CAAC;QACtCrB,kBAAkB,CAAC,IAAI,CAAC;QACxBQ,cAAc,CAAC,EAAE,CAAC;QAClBgB,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAENnE,SAAS,CAAC,MAAM;IACd,MAAM8G,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,uBAAuB,GAAG,MAAMjD,MAAM,CAACC,WAAW,CAACiD,0BAA0B,CAAC,CAAC;MACrF3D,iBAAiB,CAAC0D,uBAAuB,KAAK,MAAM,CAAC;IACvD,CAAC;IACDD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN9G,SAAS,CAAC,MAAM;IACd,MAAM8G,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMG,qBAAqB,GAAG,MAAMnD,MAAM,CAACC,WAAW,CAACmD,wBAAwB,CAAC,CAAC;MACjF3D,eAAe,CAAC0D,qBAAqB,KAAK,MAAM,CAAC;IACnD,CAAC;IACDH,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEjG,OAAA,CAACG,SAAS;IAAAmG,QAAA,gBACRtG,OAAA;MAAKuG,KAAK,EAAE;QAACC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAM,CAAE;MAAAL,QAAA,gBACjFtG,OAAA,CAACT,YAAY;QAACqH,OAAO,EAAEA,CAAA,KAAMxD,aAAa,CAAC,CAAE;QAAAkD,QAAA,eAC3CtG,OAAA;UACE6G,GAAG,EAAEvH,8BAA+B;UACpCwH,GAAG,EAAC,aAAa;UACjBH,MAAM,EAAE,EAAG;UACXJ,KAAK,EAAE;YAACQ,UAAU,EAAE,MAAM;YAAEC,aAAa,EAAE;UAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,EACdzF,QAAQ,iBACP3B,OAAA,CAAAE,SAAA;QAAAoG,QAAA,gBACEtG,OAAA;UAAKuG,KAAK,EAAE;YAACG,KAAK,EAAE;UAAM;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BpH,OAAA,CAACM,KAAK;UACJoF,KAAK,EAAE3D,WAAY;UACnBsF,QAAQ,EAAGC,CAAC,IAAKtF,cAAc,CAACsF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAChD8B,WAAW,EAAC,oBAAoB;UAChCC,SAAS,EAAGH,CAAC,IAAKA,CAAC,CAACI,GAAG,KAAK,OAAO,IAAI7E,WAAW,CAAC;QAAE;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,EACD,CAACnF,OAAO,IAAIE,eAAe,KAAK,IAAI,iBACnCnC,OAAA,CAAAE,SAAA;UAAAoG,QAAA,gBACEtG,OAAA;YAAKuG,KAAK,EAAE;cAACG,KAAK,EAAE;YAAK;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BpH,OAAA,CAACmB,eAAe;YAAAmF,QAAA,eACdtG,OAAA,CAACqB,UAAU;cACTE,MAAM,EAAEgB,cAAe;cACvBqE,OAAO,EAAEA,CAAA,KAAMnB,oBAAoB,CAAC,CAAClD,cAAc,CAAE;cAAA+D,QAAA,eAErDtG,OAAA,CAACH,iBAAiB;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAClBpH,OAAA;YAAKuG,KAAK,EAAE;cAACG,KAAK,EAAE;YAAK;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BpH,OAAA,CAACmB,eAAe;YAAAmF,QAAA,eACdtG,OAAA,CAACqB,UAAU;cACTE,MAAM,EAAEkB,YAAa;cACrBmE,OAAO,EAAEA,CAAA,KAAMlE,eAAe,CAAC,CAACD,YAAY,CAAE;cAAA6D,QAAA,eAE9CtG,OAAA,CAACF,OAAO;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eAClB,CACH,EACA,CAACnF,OAAO,IAAIE,eAAe,KAAK,IAAI,kBAAKnC,OAAA,CAACU,OAAO;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDpH,OAAA;UAAKuG,KAAK,EAAE;YAACG,KAAK,EAAE;UAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE9BjF,eAAe,KAAK,IAAI,iBAAInC,OAAA,CAAAE,SAAA;UAAAoG,QAAA,eACxBtG,OAAA,CAACR,UAAU;YAACmI,QAAQ,EAAC,MAAM;YAACC,KAAK,EAAC,OAAO;YAAChB,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAC1B,eAAe,CAAE;YAC1F0F,QAAQ,EAAE5F,OAAQ;YAAAqE,QAAA,eAClBtG,OAAA,CAACL,YAAY;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,gBACb,CAAC;MAAA,eAEL,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACLzF,QAAQ,IAAIE,eAAe,iBAC1B7B,OAAA,CAACY,gBAAgB;MAAA0F,QAAA,EACdjE,WAAW,CAACgB,MAAM,KAAK,CAAC,GACrByE,KAAK,CAACC,IAAI,CAAC;QAAE1E,MAAM,EAAE;MAAE,CAAC,CAAC,CAAC2E,GAAG,CAAC,CAACC,CAAC,EAAEC,GAAG,kBACnClI,OAAA,CAACiB,YAAY,MAAMiH,GAAG;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC1B,CAAC,GACF/E,WAAW,CAAC2F,GAAG,CAAC,CAACG,CAAC,EAAED,GAAG,kBACrBlI,OAAA,CAACc,cAAc;QAEb8F,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAACoF,CAAC,CAACC,SAAS,CAAE;QAAA9B,QAAA,EAE7C6B,CAAC,CAACE;MAAK,GAHHH,GAAG;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIM,CACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CACnB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB;AAAC1F,EAAA,CA5OuBD,OAAO;EAAA,QAUThC,WAAW;AAAA;AAAA6I,GAAA,GAVT7G,OAAO;AAAA,IAAApB,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAA8G,GAAA;AAAAC,YAAA,CAAAlI,EAAA;AAAAkI,YAAA,CAAA/H,GAAA;AAAA+H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}