{"ast": null, "code": "var _jsxFileName = \"F:\\\\zuomianwenjian\\\\ss\\\\neuralagent-main\\\\desktop\\\\neuralagent-app\\\\src\\\\components\\\\Elements\\\\Dialog\\\\index.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Dialog<PERSON>ontainer, DialogOverlay, DialogHeader, HeaderEnd, DialogTitle, DialogContent, DialogActions } from './DialogElements';\nimport { IconButton } from '../Button';\nimport { IoMdClose } from 'react-icons/io';\nimport { Divider } from '../SmallElements';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dialog = ({\n  child,\n  maxWidth,\n  isOpen,\n  setOpen,\n  persistant,\n  title = null,\n  flex = true,\n  padding,\n  scrollable = false,\n  actions = null,\n  isDarkMode = false\n}) => {\n  _s();\n  const onOverlayClick = () => {\n    if (persistant !== true) {\n      setOpen(false);\n    }\n  };\n  const isRTL = useSelector(state => state.isRTL);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isOpen ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(DialogOverlay, {\n        onClick: onOverlayClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DialogContainer, {\n        maxWidth: maxWidth,\n        isDarkMode: isDarkMode,\n        children: [/*#__PURE__*/_jsxDEV(DialogHeader, {\n          children: [title !== null ? /*#__PURE__*/_jsxDEV(DialogTitle, {\n            isDarkMode: isDarkMode,\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), /*#__PURE__*/_jsxDEV(HeaderEnd, {\n            isRTL: isRTL,\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setOpen(false),\n              iconSize: \"30px\",\n              color: isDarkMode ? \"rgba(255, 255, 255, 0.75)\" : \"rgba(0, 0, 0, 0.75)\",\n              children: /*#__PURE__*/_jsxDEV(IoMdClose, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          color: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',\n          style: {\n            marginBottom: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this), flex ? /*#__PURE__*/_jsxDEV(DialogContent, {\n          padding: padding,\n          scrollable: scrollable,\n          children: child\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 22\n        }, this) : child, actions !== null ? /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: actions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 34\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n  }, void 0, false);\n};\n_s(Dialog, \"4be5AVVFo8sXi1J7oAeipnnv2Bc=\", false, function () {\n  return [useSelector];\n});\n_c = Dialog;\nexport default Dialog;\nvar _c;\n$RefreshReg$(_c, \"Dialog\");", "map": {"version": 3, "names": ["React", "DialogContainer", "DialogOverlay", "DialogHeader", "HeaderEnd", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "IoMdClose", "Divider", "useSelector", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dialog", "child", "max<PERSON><PERSON><PERSON>", "isOpen", "<PERSON><PERSON><PERSON>", "persistant", "title", "flex", "padding", "scrollable", "actions", "isDarkMode", "_s", "onOverlayClick", "isRTL", "state", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "iconSize", "color", "style", "marginBottom", "_c", "$RefreshReg$"], "sources": ["F:/zuomianwenjian/ss/neuralagent-main/desktop/neuralagent-app/src/components/Elements/Dialog/index.js"], "sourcesContent": ["import React from 'react';\nimport {\n  DialogContainer,\n  Dialog<PERSON><PERSON>lay,\n  DialogHeader,\n  HeaderEnd,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from './DialogElements';\nimport {\n  IconButton\n} from '../Button';\nimport { IoMdClose } from 'react-icons/io';\nimport { Divider } from '../SmallElements';\nimport { useSelector } from 'react-redux';\n\nconst Dialog = ({ child, maxWidth, isOpen, setOpen, persistant, title=null, flex=true, padding, scrollable=false, actions=null, isDarkMode=false }) => {\n\n  const onOverlayClick = () => {\n    if (persistant !== true) {\n      setOpen(false);\n    }\n  };\n\n  const isRTL = useSelector(state => state.isRTL);\n\n  return (\n    <>\n      {\n        isOpen ? \n        <>\n          <DialogOverlay onClick={onOverlayClick} />\n          <DialogContainer maxWidth={maxWidth} isDarkMode={isDarkMode}>\n            <DialogHeader>\n              {\n                title !== null ?\n                <DialogTitle isDarkMode={isDarkMode}>\n                  {title}\n                </DialogTitle> :\n                <></>\n              }\n              <HeaderEnd isRTL={isRTL}>\n                <IconButton onClick={() => setOpen(false)} iconSize=\"30px\" color={isDarkMode ? \"rgba(255, 255, 255, 0.75)\" : \"rgba(0, 0, 0, 0.75)\"}>\n                  <IoMdClose />\n                </IconButton>\n              </HeaderEnd>\n            </DialogHeader>\n            <Divider color={isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'} style={{marginBottom: '10px'}} />\n            {\n              flex ? <DialogContent padding={padding} scrollable={scrollable}>\n                {child}\n              </DialogContent> : child\n            }\n            {\n              actions !== null ? <DialogActions>\n                {actions}\n              </DialogActions> : <></>\n            }\n          </DialogContainer>\n        </> : <></>\n      }\n    </>\n  )\n};\n\nexport default Dialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,eAAe,EACfC,aAAa,EACbC,YAAY,EACZC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,kBAAkB;AACzB,SACEC,UAAU,QACL,WAAW;AAClB,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,MAAM,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,OAAO;EAAEC,UAAU;EAAEC,KAAK,GAAC,IAAI;EAAEC,IAAI,GAAC,IAAI;EAAEC,OAAO;EAAEC,UAAU,GAAC,KAAK;EAAEC,OAAO,GAAC,IAAI;EAAEC,UAAU,GAAC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAErJ,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIR,UAAU,KAAK,IAAI,EAAE;MACvBD,OAAO,CAAC,KAAK,CAAC;IAChB;EACF,CAAC;EAED,MAAMU,KAAK,GAAGnB,WAAW,CAACoB,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAE/C,oBACEjB,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EAEIb,MAAM,gBACNN,OAAA,CAAAE,SAAA;MAAAiB,QAAA,gBACEnB,OAAA,CAACX,aAAa;QAAC+B,OAAO,EAAEJ;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CxB,OAAA,CAACZ,eAAe;QAACiB,QAAQ,EAAEA,QAAS;QAACS,UAAU,EAAEA,UAAW;QAAAK,QAAA,gBAC1DnB,OAAA,CAACV,YAAY;UAAA6B,QAAA,GAETV,KAAK,KAAK,IAAI,gBACdT,OAAA,CAACR,WAAW;YAACsB,UAAU,EAAEA,UAAW;YAAAK,QAAA,EACjCV;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,gBACdxB,OAAA,CAAAE,SAAA,mBAAI,CAAC,eAEPF,OAAA,CAACT,SAAS;YAAC0B,KAAK,EAAEA,KAAM;YAAAE,QAAA,eACtBnB,OAAA,CAACL,UAAU;cAACyB,OAAO,EAAEA,CAAA,KAAMb,OAAO,CAAC,KAAK,CAAE;cAACkB,QAAQ,EAAC,MAAM;cAACC,KAAK,EAAEZ,UAAU,GAAG,2BAA2B,GAAG,qBAAsB;cAAAK,QAAA,eACjInB,OAAA,CAACJ,SAAS;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACfxB,OAAA,CAACH,OAAO;UAAC6B,KAAK,EAAEZ,UAAU,GAAG,0BAA0B,GAAG,oBAAqB;UAACa,KAAK,EAAE;YAACC,YAAY,EAAE;UAAM;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE/Gd,IAAI,gBAAGV,OAAA,CAACP,aAAa;UAACkB,OAAO,EAAEA,OAAQ;UAACC,UAAU,EAAEA,UAAW;UAAAO,QAAA,EAC5Df;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,GAAGpB,KAAK,EAGxBS,OAAO,KAAK,IAAI,gBAAGb,OAAA,CAACN,aAAa;UAAAyB,QAAA,EAC9BN;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,gBAAGxB,OAAA,CAAAE,SAAA,mBAAI,CAAC;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC;IAAA,eAClB,CAAC,gBAAGxB,OAAA,CAAAE,SAAA,mBAAI;EAAC,gBAEb,CAAC;AAEP,CAAC;AAACa,EAAA,CA/CIZ,MAAM;EAAA,QAQIL,WAAW;AAAA;AAAA+B,EAAA,GARrB1B,MAAM;AAiDZ,eAAeA,MAAM;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}