{"name": "estree-walker", "description": "Traverse an ESTree-compliant AST", "version": "1.0.1", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/<PERSON>-<PERSON>/estree-walker"}, "main": "dist/estree-walker.umd.js", "module": "src/estree-walker.js", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build && npm test", "build": "tsc && rollup -c", "test": "mocha --opts mocha.opts"}, "devDependencies": {"@types/estree": "0.0.39", "mocha": "^5.2.0", "rollup": "^0.67.3", "rollup-plugin-sucrase": "^2.1.0", "typescript": "^3.6.3"}, "files": ["src", "dist", "types", "README.md"]}